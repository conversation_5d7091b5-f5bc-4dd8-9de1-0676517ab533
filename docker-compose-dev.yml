services:
  db:
    image: mysql:8.0
    container_name: db
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    ports:
      - 3306:3306
    environment:
      MYSQL_ROOT_PASSWORD: coffeelake
      MYSQL_DATABASE: hms
    volumes:
      - ~/mysql-data:/var/lib/mysql
    networks:
      - hms_network
  # cloudflared:
  #   image: cloudflare/cloudflared:latest
  #   container_name: cloudflared
  #   restart: always
  #   command: tunnel --no-autoupdate run
  #   environment:
  #     - 'TUNNEL_TOKEN=${CF_TOKEN}'
  #   networks:
  #     - hms_network

  adminer:
    container_name: adminer
    image: adminer
    restart: always
    ports:
      - 8080:8080
    networks:
      - hms_network
networks:
  hms_network:
    driver: bridge
