<p align="center"> 
<img src="./static/logo_krupet.png?style=for-the-badge&logo=jest&logoColor=white" width="120">   
<h2 align="center">🔥🔥 Krupet.com</h2>

<div align="center">
<h3>🔥Languages-Frameworks-Tools</h3>
    <img src="https://skillicons.dev/icons?i=bun,nodejs,svelte,react,typescript,mysql,prisma,docker" /><br>
    <img src="https://skillicons.dev/icons?i=,bootstrap,html,css,vscode,git,linux,bash,github,nginx," />

</div>

<br/>

## Developer

| Name        | Rol        | Link                           |
| ----------- | ---------- | ------------------------------ |
| AN Channean | Full Stack | https://github.com/ANNchannean |

# Fix caddy error

```bash
docker exec -w /etc/caddy caddy caddy fmt --overwrite
docker exec -w /etc/caddy caddy caddy reload
```

# Docker

```bash
sudo usermod -aG docker $USER;
rm -rf ~/.docker
```

# Allow permision folder

```bash
sudo chown -R $USER:$USER ./uploads
```

<hr/>
# Backup folder uploads using wget
```bash
wget -r -np -nH --cut-dirs=1 https://ndkpc.store/uploads/
wget -r -np -nH --cut-dirs=1 --accept-regex '.*\.(jpg|png|zip|pdf)' https://ndkpc.store/uploads

git config --global user.email "<EMAIL>"
git config --global user.name "annchannean"

```
<hr/>

## Todo

- [x] emplement switch from opd to ipd
- [x] fix phyciacal examination
```
