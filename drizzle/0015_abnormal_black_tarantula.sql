ALTER TABLE `appointment_injection` ADD `requester_id` int;--> statement-breakpoint
ALTER TABLE `appointment_injection` ADD `injecter_id` int;--> statement-breakpoint
ALTER TABLE `appointment_injection` ADD CONSTRAINT `appointment_injection_requester_id_staff_id_fk` FOREIGN KEY (`requester_id`) REFERENCES `staff`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `appointment_injection` ADD CONSTRAINT `appointment_injection_injecter_id_staff_id_fk` FOREIGN KEY (`injecter_id`) REFERENCES `staff`(`id`) ON DELETE no action ON UPDATE no action;