CREATE TABLE `commune` (
	`commune_id` int AUTO_INCREMENT NOT NULL,
	`name_khmer` varchar(50) NOT NULL,
	`name_latin` varchar(50) NOT NULL,
	`type` varchar(20) NOT NULL,
	`district_id` int,
	CONSTRAINT `commune_commune_id` PRIMARY KEY(`commune_id`)
);
--> statement-breakpoint
CREATE TABLE `district` (
	`district_id` int AUTO_INCREMENT NOT NULL,
	`name_khmer` varchar(50) NOT NULL,
	`name_latin` varchar(50) NOT NULL,
	`type` varchar(20) NOT NULL,
	`province_id` int,
	CONSTRAINT `district_district_id` PRIMARY KEY(`district_id`)
);
--> statement-breakpoint
CREATE TABLE `provice` (
	`provice_id` int AUTO_INCREMENT NOT NULL,
	`name_khmer` varchar(50) NOT NULL,
	`name_latin` varchar(50) NOT NULL,
	`type` varchar(20) NOT NULL,
	`contry_id` int,
	CONSTRAINT `provice_provice_id` PRIMARY KEY(`provice_id`)
);
--> statement-breakpoint
CREATE TABLE `village` (
	`village_id` int AUTO_INCREMENT NOT NULL,
	`name_khmer` varchar(50) NOT NULL,
	`name_latin` varchar(50) NOT NULL,
	`type` varchar(20) NOT NULL,
	`commune_id` int,
	CONSTRAINT `village_village_id` PRIMARY KEY(`village_id`)
);
--> statement-breakpoint
CREATE TABLE `diagnosis` (
	`id` int AUTO_INCREMENT NOT NULL,
	`diagnosis` varchar(255),
	`diagnosis_khmer` varchar(255),
	`diagnosis_type_id` int,
	CONSTRAINT `diagnosis_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `diagnosis_type` (
	`id` int AUTO_INCREMENT NOT NULL,
	`diagnosis_type` varchar(150) NOT NULL,
	CONSTRAINT `diagnosis_type_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `uploads` (
	`id` int AUTO_INCREMENT NOT NULL,
	`filename` varchar(255),
	`mimeType` varchar(255),
	`related_id` int,
	`related_type` varchar(50),
	CONSTRAINT `uploads_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `laboratory` (
	`id` int AUTO_INCREMENT NOT NULL,
	`doctor_comment` text,
	`input_by_id` int,
	`patient_id` int,
	`request_datetime` datetime,
	`finish_datetime` datetime,
	`sample` varchar(255),
	`status` boolean DEFAULT false,
	`visit_id` int,
	CONSTRAINT `laboratory_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `laboratory_group` (
	`id` int AUTO_INCREMENT NOT NULL,
	`laboratory_group` varchar(255) NOT NULL,
	CONSTRAINT `laboratory_group_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `laboratory_request` (
	`id` int AUTO_INCREMENT NOT NULL,
	`product_id` int,
	`visit_id` int,
	CONSTRAINT `laboratory_request_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `laboratory_result` (
	`id` int AUTO_INCREMENT NOT NULL,
	`laboratory_request_id` int,
	`parameter_id` int,
	`result` varchar(255),
	CONSTRAINT `laboratory_result_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `para_unit` (
	`id` int AUTO_INCREMENT NOT NULL,
	`unit` varchar(255),
	CONSTRAINT `para_unit_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `parameter` (
	`id` int AUTO_INCREMENT NOT NULL,
	`parameter` varchar(255),
	`description` longtext,
	`gender` varchar(10),
	`sign` varchar(10) NOT NULL DEFAULT '-',
	`para_unit_id` int,
	`mini` float,
	`maxi` float,
	`product_id` int,
	CONSTRAINT `parameter_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `occupation_list` (
	`id` int AUTO_INCREMENT NOT NULL,
	`occupation` text,
	CONSTRAINT `occupation_list_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `patient` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name_khmer` varchar(50) NOT NULL,
	`name_latin` varchar(50),
	`gender` varchar(10) NOT NULL,
	`dob` date,
	`id_cart_passport` varchar(255),
	`education` varchar(255),
	`nation` varchar(255),
	`material_status` varchar(255),
	`work_place` varchar(255),
	`occupation` varchar(255),
	`telephone` varchar(50),
	`blood_group` varchar(50),
	`other` varchar(255),
	`village_id` int,
	`commune_id` int,
	`district_id` int,
	`province_id` int,
	`created_at` datetime NOT NULL,
	`f_name_khmer` varchar(50),
	`f_name_latin` varchar(50),
	`f_telephone` varchar(50),
	`f_occupation` varchar(255),
	`m_name_khmer` varchar(50),
	`m_name_latin` varchar(50),
	`m_telephone` varchar(50),
	`m_occupation` varchar(255),
	`c_name_khmer` varchar(50),
	`c_name_latin` varchar(50),
	`c_telephone` varchar(50),
	`c_occupation` varchar(255),
	CONSTRAINT `patient_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `exam` (
	`id` int AUTO_INCREMENT NOT NULL,
	`examination` varchar(255),
	CONSTRAINT `exam_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `physical` (
	`id` int AUTO_INCREMENT NOT NULL,
	`physical` varchar(150),
	`exam_id` int,
	CONSTRAINT `physical_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `physical_exam` (
	`id` int AUTO_INCREMENT NOT NULL,
	`physical_id` int,
	`result` varchar(255),
	`visit_id` int,
	CONSTRAINT `physical_exam_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `brand` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255),
	CONSTRAINT `brand_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `category` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255),
	CONSTRAINT `category_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `group` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(100) NOT NULL,
	`vaccine_dose` text,
	`category_id` int,
	CONSTRAINT `group_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `inventory` (
	`id` int AUTO_INCREMENT NOT NULL,
	`product_id` int NOT NULL,
	`cost_unit_id` int,
	`exspend_id` int,
	`cost` decimal(18,2),
	`total_expense` decimal(18,2),
	`is_outstock` boolean NOT NULL DEFAULT false,
	`is_expire` boolean NOT NULL DEFAULT false,
	`is_close_inventory` boolean NOT NULL DEFAULT false,
	`qty_bought` int NOT NULL DEFAULT 0,
	`qty_expire` int NOT NULL DEFAULT 0,
	`group_id` int,
	`qty_adjustment` int NOT NULL DEFAULT 0,
	`is_count_stock` boolean NOT NULL DEFAULT false,
	`datetime_expire` datetime,
	`datetime_buy` datetime,
	`datetime_outstock` datetime,
	CONSTRAINT `inventory_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `product` (
	`id` int AUTO_INCREMENT NOT NULL,
	`products` text NOT NULL,
	`generic_name` text,
	`barcode` varchar(255),
	`group_id` int,
	`laboratory_group_id` int,
	`price` decimal(18,2),
	`brand_id` int,
	`category_id` int,
	`unit_id` int,
	`create_at` datetime,
	CONSTRAINT `product_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `product_order` (
	`id` int AUTO_INCREMENT NOT NULL,
	`created_at` datetime,
	`price` decimal(18,2),
	`total` decimal(18,2),
	`qty` int NOT NULL DEFAULT 1,
	`qty_adjustment` int NOT NULL DEFAULT 1,
	`inventory_id` int,
	`discount` varchar(50),
	`product_id` int NOT NULL,
	`unit_id` int,
	`charge_id` int NOT NULL,
	CONSTRAINT `product_order_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `sub_unit` (
	`id` int AUTO_INCREMENT NOT NULL,
	`qty_per_unit` int NOT NULL DEFAULT 0,
	`price` decimal(18,2),
	`unit_id` int NOT NULL,
	`product_id` int,
	CONSTRAINT `sub_unit_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `unit` (
	`id` int AUTO_INCREMENT NOT NULL,
	`unit` varchar(255),
	`vaccine_dose` text,
	CONSTRAINT `unit_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `units_to_groups` (
	`unit_id` int NOT NULL,
	`group_id` int NOT NULL,
	CONSTRAINT `units_to_groups_unit_id_group_id_pk` PRIMARY KEY(`unit_id`,`group_id`)
);
--> statement-breakpoint
CREATE TABLE `session` (
	`id` varchar(255) NOT NULL,
	`user_id` varchar(255) NOT NULL,
	`expires_at` timestamp NOT NULL,
	CONSTRAINT `session_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `user` (
	`id` varchar(255) NOT NULL,
	`username` varchar(50) NOT NULL,
	`password_hash` text,
	`staff_id` int,
	CONSTRAINT `user_id` PRIMARY KEY(`id`),
	CONSTRAINT `user_username_unique` UNIQUE(`username`)
);
--> statement-breakpoint
CREATE TABLE `designation` (
	`id` int AUTO_INCREMENT NOT NULL,
	`kh` varchar(100),
	`eng` varchar(100),
	CONSTRAINT `designation_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `role` (
	`id` int AUTO_INCREMENT NOT NULL,
	`role` varchar(255) NOT NULL,
	CONSTRAINT `role_id` PRIMARY KEY(`id`),
	CONSTRAINT `role_role_unique` UNIQUE(`role`)
);
--> statement-breakpoint
CREATE TABLE `staff` (
	`id` int AUTO_INCREMENT NOT NULL,
	`gender` varchar(10),
	`specialist` varchar(255),
	`designation_id` int,
	`title_id` int,
	`id_staff` varchar(100),
	`blood_group` varchar(20),
	`name_khmer` varchar(50),
	`name_latin` varchar(50),
	`dob` date,
	`datetime_start` datetime,
	`datetime_stop` datetime,
	`telephone` varchar(50),
	`village_id` int,
	`commune_id` int,
	`district_id` int,
	`province_id` int,
	CONSTRAINT `staff_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `staff_to_department` (
	`staff_id` int NOT NULL,
	`department_id` int NOT NULL,
	CONSTRAINT `staff_to_department_staff_id_department_id_pk` PRIMARY KEY(`staff_id`,`department_id`)
);
--> statement-breakpoint
CREATE TABLE `staff_to_role` (
	`staff_id` int NOT NULL,
	`role_id` int NOT NULL,
	CONSTRAINT `staff_to_role_staff_id_role_id_pk` PRIMARY KEY(`staff_id`,`role_id`)
);
--> statement-breakpoint
CREATE TABLE `title` (
	`id` int AUTO_INCREMENT NOT NULL,
	`kh` varchar(100),
	`eng` varchar(100),
	CONSTRAINT `title_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `subjective` (
	`id` int AUTO_INCREMENT NOT NULL,
	`cheif_complaint` text,
	`current_medication` varchar(255),
	`history_of_present_illness` varchar(255),
	`past_medical_history` text,
	`allesgy_medicine` varchar(255),
	`surgical_history` varchar(255),
	`pre_diagnosis` varchar(255),
	`family_and_social_history` varchar(255),
	`visit_id` int,
	CONSTRAINT `subjective_id` PRIMARY KEY(`id`),
	CONSTRAINT `subjective_visit_id_unique` UNIQUE(`visit_id`)
);
--> statement-breakpoint
CREATE TABLE `template` (
	`id` int AUTO_INCREMENT NOT NULL,
	`diagnosis` text,
	`template` longtext,
	`group_id` int,
	CONSTRAINT `template_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `appointment_injection` (
	`id` int AUTO_INCREMENT NOT NULL,
	`appointment` datetime NOT NULL,
	`datetime_inject` datetime,
	`status` boolean DEFAULT false,
	`discription` varchar(255),
	`times` int NOT NULL DEFAULT 1,
	`injection_id` int,
	CONSTRAINT `appointment_injection_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `injection` (
	`id` int AUTO_INCREMENT NOT NULL,
	`patient_id` int,
	`datetime` datetime NOT NULL,
	`unit_id` int,
	`discription` varchar(255),
	CONSTRAINT `injection_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `vaccine` (
	`id` int AUTO_INCREMENT NOT NULL,
	`visit_id` int,
	`injection_id` int,
	`product_id` int,
	`discription` varchar(255),
	CONSTRAINT `vaccine_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `nursing_process` (
	`id` int AUTO_INCREMENT NOT NULL,
	`datetime` datetime,
	`accessment` varchar(255),
	`health_problems` text,
	`actions` varchar(255),
	`evolution` varchar(255),
	`nursing_sign` int,
	`progress_note_id` int,
	`active_department_id` int,
	CONSTRAINT `nursing_process_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `progress_note` (
	`id` int AUTO_INCREMENT NOT NULL,
	`date_checkup` datetime NOT NULL,
	`date_checkout` datetime,
	`is_discharge` boolean NOT NULL DEFAULT false,
	`is_emr` boolean NOT NULL DEFAULT false,
	`staff_id` int,
	`etiology` varchar(255) NOT NULL,
	`status` varchar(10) NOT NULL DEFAULT 'LOADING',
	`department_id` int NOT NULL,
	`patient_id` int NOT NULL,
	`inclund_pay` varchar(30),
	CONSTRAINT `progress_note_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `visit` (
	`id` int AUTO_INCREMENT NOT NULL,
	`date_checkup` datetime NOT NULL,
	`patient_id` int NOT NULL,
	`department_id` int,
	`staff_id` int,
	`sender_id` int,
	`checkin_type` varchar(10) NOT NULL,
	`etiology` varchar(255) NOT NULL,
	`transfer` boolean NOT NULL DEFAULT false,
	`status` varchar(10) NOT NULL DEFAULT 'LOADING',
	`progress_note_id` int,
	CONSTRAINT `visit_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `vital_sign` (
	`id` int AUTO_INCREMENT NOT NULL,
	`dbp` float,
	`sbp` float,
	`pulse` float,
	`t` float,
	`sp02` float,
	`height` float,
	`weight` float,
	`rr` float,
	`bmi` float,
	`visit_id` int,
	`datetime` datetime,
	`stool` varchar(255),
	`urine` varchar(255),
	`note` text,
	`piv` int,
	`drink` int,
	`nasogastric_tube_in` int,
	`nasogastric_tube_out` int,
	`fluid_out` int,
	`vomiting` int,
	`by` int,
	`progress_note_id` int,
	`active_department_id` int,
	CONSTRAINT `vital_sign_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `imagerie_request` (
	`id` int AUTO_INCREMENT NOT NULL,
	`product_id` int,
	`input_by_id` int,
	`patient_id` int,
	`visit_id` int,
	`text` text,
	`is_ob_form` boolean NOT NULL DEFAULT false,
	`status` boolean DEFAULT false,
	`request_datetime` datetime,
	`finish_datetime` datetime,
	CONSTRAINT `imagerie_request_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `options` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(100),
	`result_form_id` int,
	CONSTRAINT `options_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `result_form` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(100),
	`index` int NOT NULL DEFAULT 0,
	`type` varchar(20) NOT NULL DEFAULT 'text',
	CONSTRAINT `result_form_id` PRIMARY KEY(`id`),
	CONSTRAINT `result_form_name_unique` UNIQUE(`name`)
);
--> statement-breakpoint
CREATE TABLE `result_imagerie` (
	`id` int AUTO_INCREMENT NOT NULL,
	`result` text,
	`imagerie_request_id` int,
	`result_form_id` int,
	CONSTRAINT `result_imagerie_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `advice_teaching` (
	`id` int AUTO_INCREMENT NOT NULL,
	`description` text,
	`visit_id` int,
	`progress_note_id` int,
	CONSTRAINT `advice_teaching_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `active_presrciption` (
	`id` int AUTO_INCREMENT NOT NULL,
	`active_for` varchar(100) NOT NULL,
	`datetime` datetime NOT NULL,
	`presrciption_id` int,
	`user_id` varchar(255),
	CONSTRAINT `active_presrciption_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `presrciption` (
	`id` int AUTO_INCREMENT NOT NULL,
	`visit_id` int,
	`progress_note_id` int,
	`product_id` int,
	`unit_id` int,
	`use` varchar(150),
	`duration` varchar(150),
	`amount` float,
	`morning` float,
	`noon` float,
	`afternoon` float,
	`evening` float,
	`night` float,
	CONSTRAINT `presrciption_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `use` (
	`id` int AUTO_INCREMENT NOT NULL,
	`description` varchar(150),
	CONSTRAINT `use_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `duration` (
	`id` int AUTO_INCREMENT NOT NULL,
	`description` varchar(50),
	CONSTRAINT `duration_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `appointment` (
	`id` int AUTO_INCREMENT NOT NULL,
	`visit_id` int,
	`progress_note_id` int,
	`datetime` datetime,
	`description` text,
	`datetime_come` datetime,
	`status` boolean NOT NULL DEFAULT false,
	CONSTRAINT `appointment_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `service` (
	`id` int AUTO_INCREMENT NOT NULL,
	`visit_id` int,
	`progress_note_id` int,
	`product_id` int,
	`is_paid_ipd` boolean NOT NULL DEFAULT false,
	CONSTRAINT `service_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `operation_protocol` (
	`id` int AUTO_INCREMENT NOT NULL,
	`surgeon` varchar(255),
	`assistant_surgeon` varchar(255),
	`anesthetist` varchar(255),
	`assistant_anesthetist` varchar(255),
	`scrub_nurse` varchar(255),
	`cirulating_nurse_block` varchar(255),
	`midwife` varchar(255),
	`pre_diagnosis` varchar(255),
	`post_diagnosis` varchar(255),
	`type_anesthesia` varchar(255),
	`opertive_technique` text,
	`blood_less` varchar(255),
	`notes` text,
	`date` date,
	`start_time` time,
	`finish_time` time,
	`service_id` int,
	CONSTRAINT `operation_protocol_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `bed` (
	`id` int AUTO_INCREMENT NOT NULL,
	`bed` varchar(100),
	`room_id` int,
	`ward_id` int,
	`description` text,
	CONSTRAINT `bed_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `room` (
	`id` int AUTO_INCREMENT NOT NULL,
	`room` varchar(100),
	`product_id` int,
	`department_id` int,
	`ward_id` int,
	`description` text,
	CONSTRAINT `room_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `ward` (
	`id` int AUTO_INCREMENT NOT NULL,
	`ward` varchar(100),
	`description` text,
	CONSTRAINT `ward_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `accessment` (
	`id` int AUTO_INCREMENT NOT NULL,
	`visit_id` int,
	`progress_note_id` int,
	`diagnosis_or_problem` text,
	`differential_diagnosis` text,
	`assessment_process` text,
	CONSTRAINT `accessment_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `clinicinfo` (
	`id` int AUTO_INCREMENT NOT NULL,
	`title_khmer` text NOT NULL,
	`title_english` text NOT NULL,
	`address` text NOT NULL,
	`detail` text NOT NULL,
	`contact` text NOT NULL,
	CONSTRAINT `clinicinfo_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `billing` (
	`id` int AUTO_INCREMENT NOT NULL,
	`visit_id` int,
	`patient_id` int,
	`progress_note_id` int,
	`discount` varchar(50) NOT NULL DEFAULT '0',
	`amount` decimal(18,2),
	`total` decimal(18,2),
	`total_after_tax` decimal(18,2),
	`total_after_vat` decimal(18,2),
	`paid` decimal(18,2),
	`tax` float NOT NULL DEFAULT 0,
	`vat` float NOT NULL DEFAULT 0,
	`balance` decimal(18,2),
	`return` decimal(18,2),
	`status` varchar(10) NOT NULL DEFAULT 'checking',
	`billing_type` varchar(10),
	`created_at` datetime,
	`hold` boolean NOT NULL DEFAULT false,
	`note` text,
	`staff_id` int,
	`service_type_id` int,
	CONSTRAINT `billing_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `charge` (
	`id` int AUTO_INCREMENT NOT NULL,
	`created_at` datetime,
	`price` decimal(18,2),
	`charge_on` varchar(20),
	`billing_id` int NOT NULL,
	CONSTRAINT `charge_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `tax` (
	`id` int AUTO_INCREMENT NOT NULL,
	`created_at` datetime,
	`value` float NOT NULL DEFAULT 0,
	CONSTRAINT `tax_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `payment` (
	`id` int AUTO_INCREMENT NOT NULL,
	`value` decimal(18,2),
	`payment_type_id` int,
	`billing_id` int,
	`datetime` datetime,
	`note` text,
	`staff_id` int,
	`exspend_id` int,
	CONSTRAINT `payment_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `payment_service` (
	`id` int AUTO_INCREMENT NOT NULL,
	`code` varchar(255),
	`reference` varchar(255),
	`status` varchar(255) DEFAULT 'paid',
	`datetime_paid` datetime,
	`service_type_id` int,
	`billing_id` int,
	CONSTRAINT `payment_service_id` PRIMARY KEY(`id`),
	CONSTRAINT `payment_service_billing_id_unique` UNIQUE(`billing_id`)
);
--> statement-breakpoint
CREATE TABLE `payment_type` (
	`id` int AUTO_INCREMENT NOT NULL,
	`by` varchar(255),
	CONSTRAINT `payment_type_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `service_type` (
	`id` int AUTO_INCREMENT NOT NULL,
	`by` varchar(255),
	CONSTRAINT `service_type_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `document` (
	`id` int AUTO_INCREMENT NOT NULL,
	`datetime` datetime,
	`title` varchar(255),
	`content` text,
	`visit_id` int,
	`progress_note_id` int,
	CONSTRAINT `document_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `fields` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255),
	`result` text,
	`document_id` int,
	CONSTRAINT `fields_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `test` (
	`id` int AUTO_INCREMENT NOT NULL,
	`float` float,
	`decimal` decimal(4,2),
	`datetime` datetime,
	`time` time,
	`date` date,
	CONSTRAINT `test_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `words` (
	`id` int AUTO_INCREMENT NOT NULL,
	`category` varchar(100) NOT NULL DEFAULT 'common',
	`type` varchar(100) NOT NULL,
	`text` text NOT NULL,
	CONSTRAINT `words_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `currency` (
	`id` int AUTO_INCREMENT NOT NULL,
	`currency` varchar(5) NOT NULL,
	`currency_rate` float NOT NULL,
	`exchang_to` varchar(5) NOT NULL,
	`exchang_rate` float NOT NULL,
	CONSTRAINT `currency_id` PRIMARY KEY(`id`),
	CONSTRAINT `currency_currency_unique` UNIQUE(`currency`),
	CONSTRAINT `currency_currency_rate_unique` UNIQUE(`currency_rate`),
	CONSTRAINT `currency_exchang_to_unique` UNIQUE(`exchang_to`)
);
--> statement-breakpoint
CREATE TABLE `document_setting` (
	`id` int AUTO_INCREMENT NOT NULL,
	`logo_size` varchar(20),
	`clinic_title_en_size` varchar(20),
	`clinic_title_en_color` varchar(20),
	`clinic_title_kh_color` varchar(20),
	`clinic_title_kh_size` varchar(20),
	`header_size` varchar(20),
	`header_color` varchar(20),
	`title_size` varchar(20),
	`title_color` varchar(20),
	`footer_color` varchar(20),
	`footer_size` varchar(20),
	`text_body_color` varchar(20),
	`text_input_color` varchar(20),
	CONSTRAINT `document_setting_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `setting` (
	`id` int AUTO_INCREMENT NOT NULL,
	`print_bill` boolean NOT NULL DEFAULT false,
	`warring_expire_day` int NOT NULL DEFAULT 14,
	CONSTRAINT `setting_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `exspend` (
	`id` int AUTO_INCREMENT NOT NULL,
	`amount` decimal(18,2),
	`credit` decimal(18,2),
	`paid` decimal(18,2),
	`supplier_id` int,
	`recorder_id` int,
	`exspend_type_id` int,
	`invoice_no` varchar(255),
	`datetime_invoice` datetime NOT NULL,
	`description` text,
	CONSTRAINT `exspend_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `exspend_type` (
	`id` int AUTO_INCREMENT NOT NULL,
	`type` varchar(255) NOT NULL,
	CONSTRAINT `exspend_type_id` PRIMARY KEY(`id`),
	CONSTRAINT `exspend_type_type_unique` UNIQUE(`type`)
);
--> statement-breakpoint
CREATE TABLE `supplier` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(100),
	`contact` varchar(100),
	`address` varchar(255),
	`company_name` varchar(100),
	CONSTRAINT `supplier_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `active_bed` (
	`id` int AUTO_INCREMENT NOT NULL,
	`datetime_in` datetime NOT NULL,
	`datetime_out` datetime,
	`day_stay` int NOT NULL DEFAULT 0,
	`progress_note_id` int,
	`active_department_id` int,
	`active` boolean NOT NULL DEFAULT true,
	`bed_id` int,
	CONSTRAINT `active_bed_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `active_department` (
	`id` int AUTO_INCREMENT NOT NULL,
	`datetime_in` datetime NOT NULL,
	`datetime_out` datetime,
	`sender_id` int,
	`getter_id` int,
	`progress_note_id` int,
	`department_id` int NOT NULL,
	`active` boolean NOT NULL DEFAULT true,
	`remark` text,
	CONSTRAINT `active_department_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `leave` (
	`id` int AUTO_INCREMENT NOT NULL,
	`staff_id` int,
	`start_date` datetime NOT NULL,
	`end_date` datetime NOT NULL,
	`days` float NOT NULL,
	`reason` text,
	`salary_id` int,
	`status` varchar(20) DEFAULT 'PENDING',
	CONSTRAINT `leave_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `payroll` (
	`id` int AUTO_INCREMENT NOT NULL,
	`salary_id` int,
	`payment_date` datetime NOT NULL,
	`amount` decimal(18,2),
	`note` text,
	CONSTRAINT `payroll_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `salary` (
	`id` int AUTO_INCREMENT NOT NULL,
	`base_salary` decimal(18,2),
	`allowance` decimal(18,2),
	`deduction` decimal(18,2),
	`bunos` decimal(18,2),
	`total_salary` decimal(18,2),
	`staff_id` int,
	`effective_date` datetime NOT NULL,
	CONSTRAINT `salary_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
ALTER TABLE `commune` ADD CONSTRAINT `commune_district_id_district_district_id_fk` FOREIGN KEY (`district_id`) REFERENCES `district`(`district_id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `district` ADD CONSTRAINT `district_province_id_provice_provice_id_fk` FOREIGN KEY (`province_id`) REFERENCES `provice`(`provice_id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `village` ADD CONSTRAINT `village_commune_id_commune_commune_id_fk` FOREIGN KEY (`commune_id`) REFERENCES `commune`(`commune_id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `diagnosis` ADD CONSTRAINT `diagnosis_diagnosis_type_id_diagnosis_type_id_fk` FOREIGN KEY (`diagnosis_type_id`) REFERENCES `diagnosis_type`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `laboratory` ADD CONSTRAINT `laboratory_input_by_id_staff_id_fk` FOREIGN KEY (`input_by_id`) REFERENCES `staff`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `laboratory` ADD CONSTRAINT `laboratory_patient_id_patient_id_fk` FOREIGN KEY (`patient_id`) REFERENCES `patient`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `laboratory` ADD CONSTRAINT `laboratory_visit_id_visit_id_fk` FOREIGN KEY (`visit_id`) REFERENCES `visit`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `laboratory_request` ADD CONSTRAINT `laboratory_request_product_id_product_id_fk` FOREIGN KEY (`product_id`) REFERENCES `product`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `laboratory_request` ADD CONSTRAINT `laboratory_request_visit_id_visit_id_fk` FOREIGN KEY (`visit_id`) REFERENCES `visit`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `laboratory_result` ADD CONSTRAINT `laboratory_result_laboratory_request_id_laboratory_request_id_fk` FOREIGN KEY (`laboratory_request_id`) REFERENCES `laboratory_request`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `laboratory_result` ADD CONSTRAINT `laboratory_result_parameter_id_parameter_id_fk` FOREIGN KEY (`parameter_id`) REFERENCES `parameter`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `parameter` ADD CONSTRAINT `parameter_para_unit_id_para_unit_id_fk` FOREIGN KEY (`para_unit_id`) REFERENCES `para_unit`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `parameter` ADD CONSTRAINT `parameter_product_id_product_id_fk` FOREIGN KEY (`product_id`) REFERENCES `product`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `patient` ADD CONSTRAINT `patient_village_id_village_village_id_fk` FOREIGN KEY (`village_id`) REFERENCES `village`(`village_id`) ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `patient` ADD CONSTRAINT `patient_commune_id_commune_commune_id_fk` FOREIGN KEY (`commune_id`) REFERENCES `commune`(`commune_id`) ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `patient` ADD CONSTRAINT `patient_district_id_district_district_id_fk` FOREIGN KEY (`district_id`) REFERENCES `district`(`district_id`) ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `patient` ADD CONSTRAINT `patient_province_id_provice_provice_id_fk` FOREIGN KEY (`province_id`) REFERENCES `provice`(`provice_id`) ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `physical` ADD CONSTRAINT `physical_exam_id_exam_id_fk` FOREIGN KEY (`exam_id`) REFERENCES `exam`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `physical_exam` ADD CONSTRAINT `physical_exam_physical_id_physical_id_fk` FOREIGN KEY (`physical_id`) REFERENCES `physical`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `physical_exam` ADD CONSTRAINT `physical_exam_visit_id_visit_id_fk` FOREIGN KEY (`visit_id`) REFERENCES `visit`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `group` ADD CONSTRAINT `group_category_id_category_id_fk` FOREIGN KEY (`category_id`) REFERENCES `category`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `inventory` ADD CONSTRAINT `inventory_product_id_product_id_fk` FOREIGN KEY (`product_id`) REFERENCES `product`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `inventory` ADD CONSTRAINT `inventory_cost_unit_id_unit_id_fk` FOREIGN KEY (`cost_unit_id`) REFERENCES `unit`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `inventory` ADD CONSTRAINT `inventory_exspend_id_exspend_id_fk` FOREIGN KEY (`exspend_id`) REFERENCES `exspend`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `inventory` ADD CONSTRAINT `inventory_group_id_group_id_fk` FOREIGN KEY (`group_id`) REFERENCES `group`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `product` ADD CONSTRAINT `product_group_id_group_id_fk` FOREIGN KEY (`group_id`) REFERENCES `group`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `product` ADD CONSTRAINT `product_laboratory_group_id_laboratory_group_id_fk` FOREIGN KEY (`laboratory_group_id`) REFERENCES `laboratory_group`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `product` ADD CONSTRAINT `product_brand_id_brand_id_fk` FOREIGN KEY (`brand_id`) REFERENCES `brand`(`id`) ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `product` ADD CONSTRAINT `product_category_id_category_id_fk` FOREIGN KEY (`category_id`) REFERENCES `category`(`id`) ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `product` ADD CONSTRAINT `product_unit_id_unit_id_fk` FOREIGN KEY (`unit_id`) REFERENCES `unit`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `product_order` ADD CONSTRAINT `product_order_inventory_id_inventory_id_fk` FOREIGN KEY (`inventory_id`) REFERENCES `inventory`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `product_order` ADD CONSTRAINT `product_order_product_id_product_id_fk` FOREIGN KEY (`product_id`) REFERENCES `product`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `product_order` ADD CONSTRAINT `product_order_unit_id_unit_id_fk` FOREIGN KEY (`unit_id`) REFERENCES `unit`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `product_order` ADD CONSTRAINT `product_order_charge_id_charge_id_fk` FOREIGN KEY (`charge_id`) REFERENCES `charge`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `sub_unit` ADD CONSTRAINT `sub_unit_unit_id_unit_id_fk` FOREIGN KEY (`unit_id`) REFERENCES `unit`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `sub_unit` ADD CONSTRAINT `sub_unit_product_id_product_id_fk` FOREIGN KEY (`product_id`) REFERENCES `product`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `units_to_groups` ADD CONSTRAINT `units_to_groups_unit_id_unit_id_fk` FOREIGN KEY (`unit_id`) REFERENCES `unit`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `units_to_groups` ADD CONSTRAINT `units_to_groups_group_id_group_id_fk` FOREIGN KEY (`group_id`) REFERENCES `group`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `session` ADD CONSTRAINT `session_user_id_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `user` ADD CONSTRAINT `user_staff_id_staff_id_fk` FOREIGN KEY (`staff_id`) REFERENCES `staff`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `staff` ADD CONSTRAINT `staff_designation_id_designation_id_fk` FOREIGN KEY (`designation_id`) REFERENCES `designation`(`id`) ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `staff` ADD CONSTRAINT `staff_title_id_title_id_fk` FOREIGN KEY (`title_id`) REFERENCES `title`(`id`) ON DELETE no action ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `staff` ADD CONSTRAINT `staff_village_id_village_village_id_fk` FOREIGN KEY (`village_id`) REFERENCES `village`(`village_id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `staff` ADD CONSTRAINT `staff_commune_id_commune_commune_id_fk` FOREIGN KEY (`commune_id`) REFERENCES `commune`(`commune_id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `staff` ADD CONSTRAINT `staff_district_id_district_district_id_fk` FOREIGN KEY (`district_id`) REFERENCES `district`(`district_id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `staff` ADD CONSTRAINT `staff_province_id_provice_provice_id_fk` FOREIGN KEY (`province_id`) REFERENCES `provice`(`provice_id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `staff_to_department` ADD CONSTRAINT `staff_to_department_staff_id_staff_id_fk` FOREIGN KEY (`staff_id`) REFERENCES `staff`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `staff_to_department` ADD CONSTRAINT `staff_to_department_department_id_product_id_fk` FOREIGN KEY (`department_id`) REFERENCES `product`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `staff_to_role` ADD CONSTRAINT `staff_to_role_staff_id_staff_id_fk` FOREIGN KEY (`staff_id`) REFERENCES `staff`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `staff_to_role` ADD CONSTRAINT `staff_to_role_role_id_role_id_fk` FOREIGN KEY (`role_id`) REFERENCES `role`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `subjective` ADD CONSTRAINT `subjective_visit_id_visit_id_fk` FOREIGN KEY (`visit_id`) REFERENCES `visit`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `template` ADD CONSTRAINT `template_group_id_group_id_fk` FOREIGN KEY (`group_id`) REFERENCES `group`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `appointment_injection` ADD CONSTRAINT `appointment_injection_injection_id_injection_id_fk` FOREIGN KEY (`injection_id`) REFERENCES `injection`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `injection` ADD CONSTRAINT `injection_patient_id_patient_id_fk` FOREIGN KEY (`patient_id`) REFERENCES `patient`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `injection` ADD CONSTRAINT `injection_unit_id_unit_id_fk` FOREIGN KEY (`unit_id`) REFERENCES `unit`(`id`) ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `vaccine` ADD CONSTRAINT `vaccine_visit_id_visit_id_fk` FOREIGN KEY (`visit_id`) REFERENCES `visit`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `vaccine` ADD CONSTRAINT `vaccine_injection_id_injection_id_fk` FOREIGN KEY (`injection_id`) REFERENCES `injection`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `vaccine` ADD CONSTRAINT `vaccine_product_id_product_id_fk` FOREIGN KEY (`product_id`) REFERENCES `product`(`id`) ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `nursing_process` ADD CONSTRAINT `nursing_process_nursing_sign_staff_id_fk` FOREIGN KEY (`nursing_sign`) REFERENCES `staff`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `nursing_process` ADD CONSTRAINT `nursing_process_progress_note_id_progress_note_id_fk` FOREIGN KEY (`progress_note_id`) REFERENCES `progress_note`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `nursing_process` ADD CONSTRAINT `nursing_process_active_department_id_active_department_id_fk` FOREIGN KEY (`active_department_id`) REFERENCES `active_department`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `progress_note` ADD CONSTRAINT `progress_note_staff_id_staff_id_fk` FOREIGN KEY (`staff_id`) REFERENCES `staff`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `progress_note` ADD CONSTRAINT `progress_note_department_id_product_id_fk` FOREIGN KEY (`department_id`) REFERENCES `product`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `progress_note` ADD CONSTRAINT `progress_note_patient_id_patient_id_fk` FOREIGN KEY (`patient_id`) REFERENCES `patient`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `visit` ADD CONSTRAINT `visit_patient_id_patient_id_fk` FOREIGN KEY (`patient_id`) REFERENCES `patient`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `visit` ADD CONSTRAINT `visit_department_id_product_id_fk` FOREIGN KEY (`department_id`) REFERENCES `product`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `visit` ADD CONSTRAINT `visit_staff_id_staff_id_fk` FOREIGN KEY (`staff_id`) REFERENCES `staff`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `visit` ADD CONSTRAINT `visit_sender_id_staff_id_fk` FOREIGN KEY (`sender_id`) REFERENCES `staff`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `visit` ADD CONSTRAINT `visit_progress_note_id_progress_note_id_fk` FOREIGN KEY (`progress_note_id`) REFERENCES `progress_note`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `vital_sign` ADD CONSTRAINT `vital_sign_visit_id_visit_id_fk` FOREIGN KEY (`visit_id`) REFERENCES `visit`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `vital_sign` ADD CONSTRAINT `vital_sign_by_staff_id_fk` FOREIGN KEY (`by`) REFERENCES `staff`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `vital_sign` ADD CONSTRAINT `vital_sign_progress_note_id_progress_note_id_fk` FOREIGN KEY (`progress_note_id`) REFERENCES `progress_note`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `vital_sign` ADD CONSTRAINT `vital_sign_active_department_id_active_department_id_fk` FOREIGN KEY (`active_department_id`) REFERENCES `active_department`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `imagerie_request` ADD CONSTRAINT `imagerie_request_product_id_product_id_fk` FOREIGN KEY (`product_id`) REFERENCES `product`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `imagerie_request` ADD CONSTRAINT `imagerie_request_input_by_id_staff_id_fk` FOREIGN KEY (`input_by_id`) REFERENCES `staff`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `imagerie_request` ADD CONSTRAINT `imagerie_request_patient_id_patient_id_fk` FOREIGN KEY (`patient_id`) REFERENCES `patient`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `imagerie_request` ADD CONSTRAINT `imagerie_request_visit_id_visit_id_fk` FOREIGN KEY (`visit_id`) REFERENCES `visit`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `options` ADD CONSTRAINT `options_result_form_id_result_form_id_fk` FOREIGN KEY (`result_form_id`) REFERENCES `result_form`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `result_imagerie` ADD CONSTRAINT `result_imagerie_imagerie_request_id_imagerie_request_id_fk` FOREIGN KEY (`imagerie_request_id`) REFERENCES `imagerie_request`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `result_imagerie` ADD CONSTRAINT `result_imagerie_result_form_id_result_form_id_fk` FOREIGN KEY (`result_form_id`) REFERENCES `result_form`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `advice_teaching` ADD CONSTRAINT `advice_teaching_visit_id_visit_id_fk` FOREIGN KEY (`visit_id`) REFERENCES `visit`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `advice_teaching` ADD CONSTRAINT `advice_teaching_progress_note_id_progress_note_id_fk` FOREIGN KEY (`progress_note_id`) REFERENCES `progress_note`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `active_presrciption` ADD CONSTRAINT `active_presrciption_presrciption_id_presrciption_id_fk` FOREIGN KEY (`presrciption_id`) REFERENCES `presrciption`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `active_presrciption` ADD CONSTRAINT `active_presrciption_user_id_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `presrciption` ADD CONSTRAINT `presrciption_visit_id_visit_id_fk` FOREIGN KEY (`visit_id`) REFERENCES `visit`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `presrciption` ADD CONSTRAINT `presrciption_progress_note_id_progress_note_id_fk` FOREIGN KEY (`progress_note_id`) REFERENCES `progress_note`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `presrciption` ADD CONSTRAINT `presrciption_product_id_product_id_fk` FOREIGN KEY (`product_id`) REFERENCES `product`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `presrciption` ADD CONSTRAINT `presrciption_unit_id_unit_id_fk` FOREIGN KEY (`unit_id`) REFERENCES `unit`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `appointment` ADD CONSTRAINT `appointment_visit_id_visit_id_fk` FOREIGN KEY (`visit_id`) REFERENCES `visit`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `appointment` ADD CONSTRAINT `appointment_progress_note_id_progress_note_id_fk` FOREIGN KEY (`progress_note_id`) REFERENCES `progress_note`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `service` ADD CONSTRAINT `service_visit_id_visit_id_fk` FOREIGN KEY (`visit_id`) REFERENCES `visit`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `service` ADD CONSTRAINT `service_progress_note_id_progress_note_id_fk` FOREIGN KEY (`progress_note_id`) REFERENCES `progress_note`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `service` ADD CONSTRAINT `service_product_id_product_id_fk` FOREIGN KEY (`product_id`) REFERENCES `product`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `operation_protocol` ADD CONSTRAINT `operation_protocol_service_id_service_id_fk` FOREIGN KEY (`service_id`) REFERENCES `service`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `bed` ADD CONSTRAINT `bed_room_id_room_id_fk` FOREIGN KEY (`room_id`) REFERENCES `room`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `bed` ADD CONSTRAINT `bed_ward_id_ward_id_fk` FOREIGN KEY (`ward_id`) REFERENCES `ward`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `room` ADD CONSTRAINT `room_product_id_product_id_fk` FOREIGN KEY (`product_id`) REFERENCES `product`(`id`) ON DELETE set null ON UPDATE set null;--> statement-breakpoint
ALTER TABLE `room` ADD CONSTRAINT `room_department_id_product_id_fk` FOREIGN KEY (`department_id`) REFERENCES `product`(`id`) ON DELETE set null ON UPDATE set null;--> statement-breakpoint
ALTER TABLE `room` ADD CONSTRAINT `room_ward_id_ward_id_fk` FOREIGN KEY (`ward_id`) REFERENCES `ward`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `accessment` ADD CONSTRAINT `accessment_visit_id_visit_id_fk` FOREIGN KEY (`visit_id`) REFERENCES `visit`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `accessment` ADD CONSTRAINT `accessment_progress_note_id_progress_note_id_fk` FOREIGN KEY (`progress_note_id`) REFERENCES `progress_note`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `billing` ADD CONSTRAINT `billing_visit_id_visit_id_fk` FOREIGN KEY (`visit_id`) REFERENCES `visit`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `billing` ADD CONSTRAINT `billing_patient_id_patient_id_fk` FOREIGN KEY (`patient_id`) REFERENCES `patient`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `billing` ADD CONSTRAINT `billing_progress_note_id_progress_note_id_fk` FOREIGN KEY (`progress_note_id`) REFERENCES `progress_note`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `billing` ADD CONSTRAINT `billing_staff_id_staff_id_fk` FOREIGN KEY (`staff_id`) REFERENCES `staff`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `billing` ADD CONSTRAINT `billing_service_type_id_service_type_id_fk` FOREIGN KEY (`service_type_id`) REFERENCES `service_type`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `charge` ADD CONSTRAINT `charge_billing_id_billing_id_fk` FOREIGN KEY (`billing_id`) REFERENCES `billing`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `payment` ADD CONSTRAINT `payment_payment_type_id_payment_type_id_fk` FOREIGN KEY (`payment_type_id`) REFERENCES `payment_type`(`id`) ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `payment` ADD CONSTRAINT `payment_billing_id_billing_id_fk` FOREIGN KEY (`billing_id`) REFERENCES `billing`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `payment` ADD CONSTRAINT `payment_staff_id_staff_id_fk` FOREIGN KEY (`staff_id`) REFERENCES `staff`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `payment` ADD CONSTRAINT `payment_exspend_id_exspend_id_fk` FOREIGN KEY (`exspend_id`) REFERENCES `exspend`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `payment_service` ADD CONSTRAINT `payment_service_service_type_id_service_type_id_fk` FOREIGN KEY (`service_type_id`) REFERENCES `service_type`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `payment_service` ADD CONSTRAINT `payment_service_billing_id_billing_id_fk` FOREIGN KEY (`billing_id`) REFERENCES `billing`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `document` ADD CONSTRAINT `document_visit_id_visit_id_fk` FOREIGN KEY (`visit_id`) REFERENCES `visit`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `document` ADD CONSTRAINT `document_progress_note_id_progress_note_id_fk` FOREIGN KEY (`progress_note_id`) REFERENCES `progress_note`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `fields` ADD CONSTRAINT `fields_document_id_document_id_fk` FOREIGN KEY (`document_id`) REFERENCES `document`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `exspend` ADD CONSTRAINT `exspend_supplier_id_supplier_id_fk` FOREIGN KEY (`supplier_id`) REFERENCES `supplier`(`id`) ON DELETE set null ON UPDATE set null;--> statement-breakpoint
ALTER TABLE `exspend` ADD CONSTRAINT `exspend_recorder_id_staff_id_fk` FOREIGN KEY (`recorder_id`) REFERENCES `staff`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `exspend` ADD CONSTRAINT `exspend_exspend_type_id_exspend_type_id_fk` FOREIGN KEY (`exspend_type_id`) REFERENCES `exspend_type`(`id`) ON DELETE set null ON UPDATE set null;--> statement-breakpoint
ALTER TABLE `active_bed` ADD CONSTRAINT `active_bed_progress_note_id_progress_note_id_fk` FOREIGN KEY (`progress_note_id`) REFERENCES `progress_note`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `active_bed` ADD CONSTRAINT `active_bed_active_department_id_active_department_id_fk` FOREIGN KEY (`active_department_id`) REFERENCES `active_department`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `active_bed` ADD CONSTRAINT `active_bed_bed_id_bed_id_fk` FOREIGN KEY (`bed_id`) REFERENCES `bed`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `active_department` ADD CONSTRAINT `active_department_sender_id_staff_id_fk` FOREIGN KEY (`sender_id`) REFERENCES `staff`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `active_department` ADD CONSTRAINT `active_department_getter_id_staff_id_fk` FOREIGN KEY (`getter_id`) REFERENCES `staff`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `active_department` ADD CONSTRAINT `active_department_progress_note_id_progress_note_id_fk` FOREIGN KEY (`progress_note_id`) REFERENCES `progress_note`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `active_department` ADD CONSTRAINT `active_department_department_id_product_id_fk` FOREIGN KEY (`department_id`) REFERENCES `product`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `leave` ADD CONSTRAINT `leave_staff_id_staff_id_fk` FOREIGN KEY (`staff_id`) REFERENCES `staff`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `leave` ADD CONSTRAINT `leave_salary_id_salary_id_fk` FOREIGN KEY (`salary_id`) REFERENCES `salary`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `payroll` ADD CONSTRAINT `payroll_salary_id_salary_id_fk` FOREIGN KEY (`salary_id`) REFERENCES `salary`(`id`) ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE `salary` ADD CONSTRAINT `salary_staff_id_staff_id_fk` FOREIGN KEY (`staff_id`) REFERENCES `staff`(`id`) ON DELETE cascade ON UPDATE cascade;