{"version": "5", "dialect": "mysql", "id": "f907486f-c6bc-4279-91f6-991716cc5da6", "prevId": "155dc58f-3f43-4c58-8b42-32066810cd95", "tables": {"accessment": {"name": "accessment", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "visit_id": {"name": "visit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "progress_note_id": {"name": "progress_note_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "diagnosis_or_problem": {"name": "diagnosis_or_problem", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "differential_diagnosis": {"name": "differential_diagnosis", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "assessment_process": {"name": "assessment_process", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"accessment_visit_id_visit_id_fk": {"name": "accessment_visit_id_visit_id_fk", "tableFrom": "accessment", "tableTo": "visit", "columnsFrom": ["visit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "accessment_progress_note_id_progress_note_id_fk": {"name": "accessment_progress_note_id_progress_note_id_fk", "tableFrom": "accessment", "tableTo": "progress_note", "columnsFrom": ["progress_note_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"accessment_id": {"name": "accessment_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "active_bed": {"name": "active_bed", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "datetime_in": {"name": "datetime_in", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "datetime_out": {"name": "datetime_out", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "day_stay": {"name": "day_stay", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "progress_note_id": {"name": "progress_note_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "active_department_id": {"name": "active_department_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "bed_id": {"name": "bed_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"active_bed_progress_note_id_progress_note_id_fk": {"name": "active_bed_progress_note_id_progress_note_id_fk", "tableFrom": "active_bed", "tableTo": "progress_note", "columnsFrom": ["progress_note_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "active_bed_active_department_id_active_department_id_fk": {"name": "active_bed_active_department_id_active_department_id_fk", "tableFrom": "active_bed", "tableTo": "active_department", "columnsFrom": ["active_department_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "active_bed_bed_id_bed_id_fk": {"name": "active_bed_bed_id_bed_id_fk", "tableFrom": "active_bed", "tableTo": "bed", "columnsFrom": ["bed_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"active_bed_id": {"name": "active_bed_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "active_department": {"name": "active_department", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "datetime_in": {"name": "datetime_in", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "datetime_out": {"name": "datetime_out", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "sender_id": {"name": "sender_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "getter_id": {"name": "getter_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "progress_note_id": {"name": "progress_note_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "department_id": {"name": "department_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "remark": {"name": "remark", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"active_department_sender_id_staff_id_fk": {"name": "active_department_sender_id_staff_id_fk", "tableFrom": "active_department", "tableTo": "staff", "columnsFrom": ["sender_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "active_department_getter_id_staff_id_fk": {"name": "active_department_getter_id_staff_id_fk", "tableFrom": "active_department", "tableTo": "staff", "columnsFrom": ["getter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "active_department_progress_note_id_progress_note_id_fk": {"name": "active_department_progress_note_id_progress_note_id_fk", "tableFrom": "active_department", "tableTo": "progress_note", "columnsFrom": ["progress_note_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "active_department_department_id_product_id_fk": {"name": "active_department_department_id_product_id_fk", "tableFrom": "active_department", "tableTo": "product", "columnsFrom": ["department_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"active_department_id": {"name": "active_department_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "active_presrciption": {"name": "active_presrciption", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "active_for": {"name": "active_for", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "datetime": {"name": "datetime", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "presrciption_id": {"name": "presrciption_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"active_presrciption_presrciption_id_presrciption_id_fk": {"name": "active_presrciption_presrciption_id_presrciption_id_fk", "tableFrom": "active_presrciption", "tableTo": "presrciption", "columnsFrom": ["presrciption_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "active_presrciption_user_id_user_id_fk": {"name": "active_presrciption_user_id_user_id_fk", "tableFrom": "active_presrciption", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"active_presrciption_id": {"name": "active_presrciption_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "advice_teaching": {"name": "advice_teaching", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "visit_id": {"name": "visit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "progress_note_id": {"name": "progress_note_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"advice_teaching_visit_id_visit_id_fk": {"name": "advice_teaching_visit_id_visit_id_fk", "tableFrom": "advice_teaching", "tableTo": "visit", "columnsFrom": ["visit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "advice_teaching_progress_note_id_progress_note_id_fk": {"name": "advice_teaching_progress_note_id_progress_note_id_fk", "tableFrom": "advice_teaching", "tableTo": "progress_note", "columnsFrom": ["progress_note_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"advice_teaching_id": {"name": "advice_teaching_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "appointment": {"name": "appointment", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "visit_id": {"name": "visit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "progress_note_id": {"name": "progress_note_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "datetime": {"name": "datetime", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "datetime_come": {"name": "datetime_come", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}}, "indexes": {}, "foreignKeys": {"appointment_visit_id_visit_id_fk": {"name": "appointment_visit_id_visit_id_fk", "tableFrom": "appointment", "tableTo": "visit", "columnsFrom": ["visit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "appointment_progress_note_id_progress_note_id_fk": {"name": "appointment_progress_note_id_progress_note_id_fk", "tableFrom": "appointment", "tableTo": "progress_note", "columnsFrom": ["progress_note_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"appointment_id": {"name": "appointment_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "appointment_injection": {"name": "appointment_injection", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "appointment": {"name": "appointment", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "requester_id": {"name": "requester_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "injecter_id": {"name": "injecter_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "datetime_inject": {"name": "datetime_inject", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "discription": {"name": "discription", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "times": {"name": "times", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "injection_id": {"name": "injection_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"appointment_injection_requester_id_staff_id_fk": {"name": "appointment_injection_requester_id_staff_id_fk", "tableFrom": "appointment_injection", "tableTo": "staff", "columnsFrom": ["requester_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "appointment_injection_injecter_id_staff_id_fk": {"name": "appointment_injection_injecter_id_staff_id_fk", "tableFrom": "appointment_injection", "tableTo": "staff", "columnsFrom": ["injecter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "appointment_injection_injection_id_injection_id_fk": {"name": "appointment_injection_injection_id_injection_id_fk", "tableFrom": "appointment_injection", "tableTo": "injection", "columnsFrom": ["injection_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"appointment_injection_id": {"name": "appointment_injection_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "bed": {"name": "bed", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "bed": {"name": "bed", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "room_id": {"name": "room_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "ward_id": {"name": "ward_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"bed_room_id_room_id_fk": {"name": "bed_room_id_room_id_fk", "tableFrom": "bed", "tableTo": "room", "columnsFrom": ["room_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "bed_ward_id_ward_id_fk": {"name": "bed_ward_id_ward_id_fk", "tableFrom": "bed", "tableTo": "ward", "columnsFrom": ["ward_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"bed_id": {"name": "bed_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "billing": {"name": "billing", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "visit_id": {"name": "visit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "patient_id": {"name": "patient_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "progress_note_id": {"name": "progress_note_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "discount": {"name": "discount", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0'"}, "amount": {"name": "amount", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "total": {"name": "total", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "total_after_tax": {"name": "total_after_tax", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "total_after_vat": {"name": "total_after_vat", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "paid": {"name": "paid", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "tax": {"name": "tax", "type": "float", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "vat": {"name": "vat", "type": "float", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "balance": {"name": "balance", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "return": {"name": "return", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'checking'"}, "billing_type": {"name": "billing_type", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "hold": {"name": "hold", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "staff_id": {"name": "staff_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "service_type_id": {"name": "service_type_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"billing_visit_id_visit_id_fk": {"name": "billing_visit_id_visit_id_fk", "tableFrom": "billing", "tableTo": "visit", "columnsFrom": ["visit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "billing_patient_id_patient_id_fk": {"name": "billing_patient_id_patient_id_fk", "tableFrom": "billing", "tableTo": "patient", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "billing_progress_note_id_progress_note_id_fk": {"name": "billing_progress_note_id_progress_note_id_fk", "tableFrom": "billing", "tableTo": "progress_note", "columnsFrom": ["progress_note_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "billing_staff_id_staff_id_fk": {"name": "billing_staff_id_staff_id_fk", "tableFrom": "billing", "tableTo": "staff", "columnsFrom": ["staff_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "billing_service_type_id_service_type_id_fk": {"name": "billing_service_type_id_service_type_id_fk", "tableFrom": "billing", "tableTo": "service_type", "columnsFrom": ["service_type_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"billing_id": {"name": "billing_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "brand": {"name": "brand", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"brand_id": {"name": "brand_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "category": {"name": "category", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"category_id": {"name": "category_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "charge": {"name": "charge", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "price": {"name": "price", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "charge_on": {"name": "charge_on", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "billing_id": {"name": "billing_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"charge_billing_id_billing_id_fk": {"name": "charge_billing_id_billing_id_fk", "tableFrom": "charge", "tableTo": "billing", "columnsFrom": ["billing_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"charge_id": {"name": "charge_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "clinicinfo": {"name": "clinicinfo", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "title_khmer": {"name": "title_khmer", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "title_english": {"name": "title_english", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "detail": {"name": "detail", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "contact": {"name": "contact", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"clinicinfo_id": {"name": "clinicinfo_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "commune": {"name": "commune", "columns": {"commune_id": {"name": "commune_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name_khmer": {"name": "name_khmer", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name_latin": {"name": "name_latin", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false}, "district_id": {"name": "district_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"commune_district_id_district_district_id_fk": {"name": "commune_district_id_district_district_id_fk", "tableFrom": "commune", "tableTo": "district", "columnsFrom": ["district_id"], "columnsTo": ["district_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"commune_commune_id": {"name": "commune_commune_id", "columns": ["commune_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "currency": {"name": "currency", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(5)", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency_rate": {"name": "currency_rate", "type": "float", "primaryKey": false, "notNull": true, "autoincrement": false}, "exchang_to": {"name": "exchang_to", "type": "<PERSON><PERSON><PERSON>(5)", "primaryKey": false, "notNull": true, "autoincrement": false}, "exchang_rate": {"name": "exchang_rate", "type": "float", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"currency_id": {"name": "currency_id", "columns": ["id"]}}, "uniqueConstraints": {"currency_currency_unique": {"name": "currency_currency_unique", "columns": ["currency"]}, "currency_currency_rate_unique": {"name": "currency_currency_rate_unique", "columns": ["currency_rate"]}, "currency_exchang_to_unique": {"name": "currency_exchang_to_unique", "columns": ["exchang_to"]}}, "checkConstraint": {}}, "designation": {"name": "designation", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "kh": {"name": "kh", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "eng": {"name": "eng", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"designation_id": {"name": "designation_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "diagnosis": {"name": "diagnosis", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "diagnosis": {"name": "diagnosis", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "diagnosis_khmer": {"name": "diagnosis_khmer", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "diagnosis_type_id": {"name": "diagnosis_type_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"diagnosis_diagnosis_type_id_diagnosis_type_id_fk": {"name": "diagnosis_diagnosis_type_id_diagnosis_type_id_fk", "tableFrom": "diagnosis", "tableTo": "diagnosis_type", "columnsFrom": ["diagnosis_type_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"diagnosis_id": {"name": "diagnosis_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "diagnosis_type": {"name": "diagnosis_type", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "diagnosis_type": {"name": "diagnosis_type", "type": "<PERSON><PERSON><PERSON>(150)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"diagnosis_type_id": {"name": "diagnosis_type_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "district": {"name": "district", "columns": {"district_id": {"name": "district_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name_khmer": {"name": "name_khmer", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name_latin": {"name": "name_latin", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false}, "province_id": {"name": "province_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"district_province_id_provice_provice_id_fk": {"name": "district_province_id_provice_provice_id_fk", "tableFrom": "district", "tableTo": "provice", "columnsFrom": ["province_id"], "columnsTo": ["provice_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"district_district_id": {"name": "district_district_id", "columns": ["district_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "document": {"name": "document", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "datetime": {"name": "datetime", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "visit_id": {"name": "visit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "progress_note_id": {"name": "progress_note_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"document_visit_id_visit_id_fk": {"name": "document_visit_id_visit_id_fk", "tableFrom": "document", "tableTo": "visit", "columnsFrom": ["visit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "document_progress_note_id_progress_note_id_fk": {"name": "document_progress_note_id_progress_note_id_fk", "tableFrom": "document", "tableTo": "progress_note", "columnsFrom": ["progress_note_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"document_id": {"name": "document_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "document_setting": {"name": "document_setting", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "logo_size": {"name": "logo_size", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "clinic_title_en_size": {"name": "clinic_title_en_size", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "clinic_title_en_color": {"name": "clinic_title_en_color", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "clinic_title_kh_color": {"name": "clinic_title_kh_color", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "clinic_title_kh_size": {"name": "clinic_title_kh_size", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "header_size": {"name": "header_size", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "header_color": {"name": "header_color", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "title_size": {"name": "title_size", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "title_color": {"name": "title_color", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "footer_color": {"name": "footer_color", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "footer_size": {"name": "footer_size", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "text_body_color": {"name": "text_body_color", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "text_input_color": {"name": "text_input_color", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"document_setting_id": {"name": "document_setting_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "duration": {"name": "duration", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"duration_id": {"name": "duration_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "error_handler": {"name": "error_handler", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "log_datetime": {"name": "log_datetime", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "log": {"name": "log", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"error_handler_id": {"name": "error_handler_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "exam": {"name": "exam", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "examination": {"name": "examination", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"exam_id": {"name": "exam_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "exspend": {"name": "exspend", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "amount": {"name": "amount", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "credit": {"name": "credit", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "paid": {"name": "paid", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "supplier_id": {"name": "supplier_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "recorder_id": {"name": "recorder_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "exspend_type_id": {"name": "exspend_type_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "invoice_no": {"name": "invoice_no", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "datetime_invoice": {"name": "datetime_invoice", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"exspend_supplier_id_supplier_id_fk": {"name": "exspend_supplier_id_supplier_id_fk", "tableFrom": "exspend", "tableTo": "supplier", "columnsFrom": ["supplier_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "set null"}, "exspend_recorder_id_staff_id_fk": {"name": "exspend_recorder_id_staff_id_fk", "tableFrom": "exspend", "tableTo": "staff", "columnsFrom": ["recorder_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "exspend_exspend_type_id_exspend_type_id_fk": {"name": "exspend_exspend_type_id_exspend_type_id_fk", "tableFrom": "exspend", "tableTo": "exspend_type", "columnsFrom": ["exspend_type_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "set null"}}, "compositePrimaryKeys": {"exspend_id": {"name": "exspend_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "exspend_type": {"name": "exspend_type", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"exspend_type_id": {"name": "exspend_type_id", "columns": ["id"]}}, "uniqueConstraints": {"exspend_type_type_unique": {"name": "exspend_type_type_unique", "columns": ["type"]}}, "checkConstraint": {}}, "fields": {"name": "fields", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "result": {"name": "result", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "document_id": {"name": "document_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"fields_document_id_document_id_fk": {"name": "fields_document_id_document_id_fk", "tableFrom": "fields", "tableTo": "document", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"fields_id": {"name": "fields_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "group": {"name": "group", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "vaccine_dose": {"name": "vaccine_dose", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "category_id": {"name": "category_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"group_category_id_category_id_fk": {"name": "group_category_id_category_id_fk", "tableFrom": "group", "tableTo": "category", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"group_id": {"name": "group_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "imagerie_request": {"name": "imagerie_request", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "product_id": {"name": "product_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "input_by_id": {"name": "input_by_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "patient_id": {"name": "patient_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "visit_id": {"name": "visit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_ob_form": {"name": "is_ob_form", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "status": {"name": "status", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "request_datetime": {"name": "request_datetime", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "finish_datetime": {"name": "finish_datetime", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"imagerie_request_product_id_product_id_fk": {"name": "imagerie_request_product_id_product_id_fk", "tableFrom": "imagerie_request", "tableTo": "product", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "imagerie_request_input_by_id_staff_id_fk": {"name": "imagerie_request_input_by_id_staff_id_fk", "tableFrom": "imagerie_request", "tableTo": "staff", "columnsFrom": ["input_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "imagerie_request_patient_id_patient_id_fk": {"name": "imagerie_request_patient_id_patient_id_fk", "tableFrom": "imagerie_request", "tableTo": "patient", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "imagerie_request_visit_id_visit_id_fk": {"name": "imagerie_request_visit_id_visit_id_fk", "tableFrom": "imagerie_request", "tableTo": "visit", "columnsFrom": ["visit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"imagerie_request_id": {"name": "imagerie_request_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "injection": {"name": "injection", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "patient_id": {"name": "patient_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "datetime": {"name": "datetime", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "unit_id": {"name": "unit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "discription": {"name": "discription", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"injection_patient_id_patient_id_fk": {"name": "injection_patient_id_patient_id_fk", "tableFrom": "injection", "tableTo": "patient", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "injection_unit_id_unit_id_fk": {"name": "injection_unit_id_unit_id_fk", "tableFrom": "injection", "tableTo": "unit", "columnsFrom": ["unit_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {"injection_id": {"name": "injection_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "inventory": {"name": "inventory", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "product_id": {"name": "product_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "cost_unit_id": {"name": "cost_unit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "exspend_id": {"name": "exspend_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "cost": {"name": "cost", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "total_expense": {"name": "total_expense", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_outstock": {"name": "is_outstock", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "is_expire": {"name": "is_expire", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "is_close_inventory": {"name": "is_close_inventory", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "qty_bought": {"name": "qty_bought", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "qty_expire": {"name": "qty_expire", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "group_id": {"name": "group_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "qty_adjustment": {"name": "qty_adjustment", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "is_count_stock": {"name": "is_count_stock", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "datetime_expire": {"name": "datetime_expire", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "datetime_buy": {"name": "datetime_buy", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "datetime_outstock": {"name": "datetime_outstock", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"inventory_product_id_product_id_fk": {"name": "inventory_product_id_product_id_fk", "tableFrom": "inventory", "tableTo": "product", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "inventory_cost_unit_id_unit_id_fk": {"name": "inventory_cost_unit_id_unit_id_fk", "tableFrom": "inventory", "tableTo": "unit", "columnsFrom": ["cost_unit_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "inventory_exspend_id_exspend_id_fk": {"name": "inventory_exspend_id_exspend_id_fk", "tableFrom": "inventory", "tableTo": "exspend", "columnsFrom": ["exspend_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "inventory_group_id_group_id_fk": {"name": "inventory_group_id_group_id_fk", "tableFrom": "inventory", "tableTo": "group", "columnsFrom": ["group_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"inventory_id": {"name": "inventory_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "laboratory": {"name": "laboratory", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "doctor_comment": {"name": "doctor_comment", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "input_by_id": {"name": "input_by_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "patient_id": {"name": "patient_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "request_datetime": {"name": "request_datetime", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "finish_datetime": {"name": "finish_datetime", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "sample": {"name": "sample", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "visit_id": {"name": "visit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"laboratory_input_by_id_staff_id_fk": {"name": "laboratory_input_by_id_staff_id_fk", "tableFrom": "laboratory", "tableTo": "staff", "columnsFrom": ["input_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "laboratory_patient_id_patient_id_fk": {"name": "laboratory_patient_id_patient_id_fk", "tableFrom": "laboratory", "tableTo": "patient", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "laboratory_visit_id_visit_id_fk": {"name": "laboratory_visit_id_visit_id_fk", "tableFrom": "laboratory", "tableTo": "visit", "columnsFrom": ["visit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"laboratory_id": {"name": "laboratory_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "laboratory_group": {"name": "laboratory_group", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "laboratory_group": {"name": "laboratory_group", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"laboratory_group_id": {"name": "laboratory_group_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "laboratory_request": {"name": "laboratory_request", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "product_id": {"name": "product_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "visit_id": {"name": "visit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"laboratory_request_product_id_product_id_fk": {"name": "laboratory_request_product_id_product_id_fk", "tableFrom": "laboratory_request", "tableTo": "product", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "laboratory_request_visit_id_visit_id_fk": {"name": "laboratory_request_visit_id_visit_id_fk", "tableFrom": "laboratory_request", "tableTo": "visit", "columnsFrom": ["visit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"laboratory_request_id": {"name": "laboratory_request_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "laboratory_result": {"name": "laboratory_result", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "laboratory_request_id": {"name": "laboratory_request_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "parameter_id": {"name": "parameter_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "result": {"name": "result", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"laboratory_result_laboratory_request_id_laboratory_request_id_fk": {"name": "laboratory_result_laboratory_request_id_laboratory_request_id_fk", "tableFrom": "laboratory_result", "tableTo": "laboratory_request", "columnsFrom": ["laboratory_request_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "laboratory_result_parameter_id_parameter_id_fk": {"name": "laboratory_result_parameter_id_parameter_id_fk", "tableFrom": "laboratory_result", "tableTo": "parameter", "columnsFrom": ["parameter_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"laboratory_result_id": {"name": "laboratory_result_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "leave": {"name": "leave", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "staff_id": {"name": "staff_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "start_date": {"name": "start_date", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "end_date": {"name": "end_date", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "days": {"name": "days", "type": "float", "primaryKey": false, "notNull": true, "autoincrement": false}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "salary_id": {"name": "salary_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'PENDING'"}}, "indexes": {}, "foreignKeys": {"leave_staff_id_staff_id_fk": {"name": "leave_staff_id_staff_id_fk", "tableFrom": "leave", "tableTo": "staff", "columnsFrom": ["staff_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "leave_salary_id_salary_id_fk": {"name": "leave_salary_id_salary_id_fk", "tableFrom": "leave", "tableTo": "salary", "columnsFrom": ["salary_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"leave_id": {"name": "leave_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "nursing_process": {"name": "nursing_process", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "datetime": {"name": "datetime", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "accessment": {"name": "accessment", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "health_problems": {"name": "health_problems", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "actions": {"name": "actions", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "evolution": {"name": "evolution", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "nursing_sign": {"name": "nursing_sign", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "progress_note_id": {"name": "progress_note_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "active_department_id": {"name": "active_department_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"nursing_process_nursing_sign_staff_id_fk": {"name": "nursing_process_nursing_sign_staff_id_fk", "tableFrom": "nursing_process", "tableTo": "staff", "columnsFrom": ["nursing_sign"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "nursing_process_progress_note_id_progress_note_id_fk": {"name": "nursing_process_progress_note_id_progress_note_id_fk", "tableFrom": "nursing_process", "tableTo": "progress_note", "columnsFrom": ["progress_note_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "nursing_process_active_department_id_active_department_id_fk": {"name": "nursing_process_active_department_id_active_department_id_fk", "tableFrom": "nursing_process", "tableTo": "active_department", "columnsFrom": ["active_department_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"nursing_process_id": {"name": "nursing_process_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "occupation_list": {"name": "occupation_list", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "occupation": {"name": "occupation", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"occupation_list_id": {"name": "occupation_list_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "operation_protocol": {"name": "operation_protocol", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "surgeon": {"name": "surgeon", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "assistant_surgeon": {"name": "assistant_surgeon", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "anesthetist": {"name": "anesthetist", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "assistant_anesthetist": {"name": "assistant_anesthetist", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "scrub_nurse": {"name": "scrub_nurse", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "cirulating_nurse_block": {"name": "cirulating_nurse_block", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "midwife": {"name": "midwife", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "pre_diagnosis": {"name": "pre_diagnosis", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "post_diagnosis": {"name": "post_diagnosis", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "type_anesthesia": {"name": "type_anesthesia", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "opertive_technique": {"name": "opertive_technique", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "blood_less": {"name": "blood_less", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": false, "autoincrement": false}, "start_time": {"name": "start_time", "type": "time", "primaryKey": false, "notNull": false, "autoincrement": false}, "finish_time": {"name": "finish_time", "type": "time", "primaryKey": false, "notNull": false, "autoincrement": false}, "service_id": {"name": "service_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"operation_protocol_service_id_service_id_fk": {"name": "operation_protocol_service_id_service_id_fk", "tableFrom": "operation_protocol", "tableTo": "service", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"operation_protocol_id": {"name": "operation_protocol_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "options": {"name": "options", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "result_form_id": {"name": "result_form_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"options_result_form_id_result_form_id_fk": {"name": "options_result_form_id_result_form_id_fk", "tableFrom": "options", "tableTo": "result_form", "columnsFrom": ["result_form_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"options_id": {"name": "options_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "para_unit": {"name": "para_unit", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "unit": {"name": "unit", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"para_unit_id": {"name": "para_unit_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "parameter": {"name": "parameter", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "parameter": {"name": "parameter", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "longtext", "primaryKey": false, "notNull": false, "autoincrement": false}, "gender": {"name": "gender", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "autoincrement": false}, "sign": {"name": "sign", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'-'"}, "para_unit_id": {"name": "para_unit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "mini": {"name": "mini", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}, "maxi": {"name": "maxi", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}, "product_id": {"name": "product_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"parameter_para_unit_id_para_unit_id_fk": {"name": "parameter_para_unit_id_para_unit_id_fk", "tableFrom": "parameter", "tableTo": "para_unit", "columnsFrom": ["para_unit_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "parameter_product_id_product_id_fk": {"name": "parameter_product_id_product_id_fk", "tableFrom": "parameter", "tableTo": "product", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"parameter_id": {"name": "parameter_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "patient": {"name": "patient", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name_khmer": {"name": "name_khmer", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name_latin": {"name": "name_latin", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "gender": {"name": "gender", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "autoincrement": false}, "dob": {"name": "dob", "type": "date", "primaryKey": false, "notNull": false, "autoincrement": false}, "id_cart_passport": {"name": "id_cart_passport", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "education": {"name": "education", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "nation": {"name": "nation", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "material_status": {"name": "material_status", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "work_place": {"name": "work_place", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "occupation": {"name": "occupation", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "telephone": {"name": "telephone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "blood_group": {"name": "blood_group", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "other": {"name": "other", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "village_id": {"name": "village_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "commune_id": {"name": "commune_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "district_id": {"name": "district_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "province_id": {"name": "province_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "f_name_khmer": {"name": "f_name_khmer", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "f_name_latin": {"name": "f_name_latin", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "f_telephone": {"name": "f_telephone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "f_occupation": {"name": "f_occupation", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "m_name_khmer": {"name": "m_name_khmer", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "m_name_latin": {"name": "m_name_latin", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "m_telephone": {"name": "m_telephone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "m_occupation": {"name": "m_occupation", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "c_name_khmer": {"name": "c_name_khmer", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "c_name_latin": {"name": "c_name_latin", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "c_telephone": {"name": "c_telephone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "c_occupation": {"name": "c_occupation", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"patient_village_id_village_village_id_fk": {"name": "patient_village_id_village_village_id_fk", "tableFrom": "patient", "tableTo": "village", "columnsFrom": ["village_id"], "columnsTo": ["village_id"], "onDelete": "set null", "onUpdate": "no action"}, "patient_commune_id_commune_commune_id_fk": {"name": "patient_commune_id_commune_commune_id_fk", "tableFrom": "patient", "tableTo": "commune", "columnsFrom": ["commune_id"], "columnsTo": ["commune_id"], "onDelete": "set null", "onUpdate": "no action"}, "patient_district_id_district_district_id_fk": {"name": "patient_district_id_district_district_id_fk", "tableFrom": "patient", "tableTo": "district", "columnsFrom": ["district_id"], "columnsTo": ["district_id"], "onDelete": "set null", "onUpdate": "no action"}, "patient_province_id_provice_provice_id_fk": {"name": "patient_province_id_provice_provice_id_fk", "tableFrom": "patient", "tableTo": "provice", "columnsFrom": ["province_id"], "columnsTo": ["provice_id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {"patient_id": {"name": "patient_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "payment": {"name": "payment", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "value": {"name": "value", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_type_id": {"name": "payment_type_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "billing_id": {"name": "billing_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "datetime": {"name": "datetime", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "staff_id": {"name": "staff_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "exspend_id": {"name": "exspend_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"payment_payment_type_id_payment_type_id_fk": {"name": "payment_payment_type_id_payment_type_id_fk", "tableFrom": "payment", "tableTo": "payment_type", "columnsFrom": ["payment_type_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}, "payment_billing_id_billing_id_fk": {"name": "payment_billing_id_billing_id_fk", "tableFrom": "payment", "tableTo": "billing", "columnsFrom": ["billing_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "payment_staff_id_staff_id_fk": {"name": "payment_staff_id_staff_id_fk", "tableFrom": "payment", "tableTo": "staff", "columnsFrom": ["staff_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "payment_exspend_id_exspend_id_fk": {"name": "payment_exspend_id_exspend_id_fk", "tableFrom": "payment", "tableTo": "exspend", "columnsFrom": ["exspend_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"payment_id": {"name": "payment_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "payment_service": {"name": "payment_service", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "reference": {"name": "reference", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'paid'"}, "datetime_paid": {"name": "datetime_paid", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "service_type_id": {"name": "service_type_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "billing_id": {"name": "billing_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"payment_service_service_type_id_service_type_id_fk": {"name": "payment_service_service_type_id_service_type_id_fk", "tableFrom": "payment_service", "tableTo": "service_type", "columnsFrom": ["service_type_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "payment_service_billing_id_billing_id_fk": {"name": "payment_service_billing_id_billing_id_fk", "tableFrom": "payment_service", "tableTo": "billing", "columnsFrom": ["billing_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"payment_service_id": {"name": "payment_service_id", "columns": ["id"]}}, "uniqueConstraints": {"payment_service_billing_id_unique": {"name": "payment_service_billing_id_unique", "columns": ["billing_id"]}}, "checkConstraint": {}}, "payment_type": {"name": "payment_type", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "by": {"name": "by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"payment_type_id": {"name": "payment_type_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "payroll": {"name": "payroll", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "salary_id": {"name": "salary_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "payment_type_id": {"name": "payment_type_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_date": {"name": "payment_date", "type": "date", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"payroll_salary_id_salary_id_fk": {"name": "payroll_salary_id_salary_id_fk", "tableFrom": "payroll", "tableTo": "salary", "columnsFrom": ["salary_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "payroll_payment_type_id_payment_type_id_fk": {"name": "payroll_payment_type_id_payment_type_id_fk", "tableFrom": "payroll", "tableTo": "payment_type", "columnsFrom": ["payment_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"payroll_id": {"name": "payroll_id", "columns": ["id"]}}, "uniqueConstraints": {"payroll_salary_id_unique": {"name": "payroll_salary_id_unique", "columns": ["salary_id"]}}, "checkConstraint": {}}, "physical": {"name": "physical", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "physical": {"name": "physical", "type": "<PERSON><PERSON><PERSON>(150)", "primaryKey": false, "notNull": false, "autoincrement": false}, "exam_id": {"name": "exam_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"physical_exam_id_exam_id_fk": {"name": "physical_exam_id_exam_id_fk", "tableFrom": "physical", "tableTo": "exam", "columnsFrom": ["exam_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"physical_id": {"name": "physical_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "physical_exam": {"name": "physical_exam", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "physical_id": {"name": "physical_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "result": {"name": "result", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "visit_id": {"name": "visit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"physical_exam_physical_id_physical_id_fk": {"name": "physical_exam_physical_id_physical_id_fk", "tableFrom": "physical_exam", "tableTo": "physical", "columnsFrom": ["physical_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "physical_exam_visit_id_visit_id_fk": {"name": "physical_exam_visit_id_visit_id_fk", "tableFrom": "physical_exam", "tableTo": "visit", "columnsFrom": ["visit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"physical_exam_id": {"name": "physical_exam_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "presrciption": {"name": "presrciption", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "visit_id": {"name": "visit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "progress_note_id": {"name": "progress_note_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "product_id": {"name": "product_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "unit_id": {"name": "unit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "use": {"name": "use", "type": "<PERSON><PERSON><PERSON>(150)", "primaryKey": false, "notNull": false, "autoincrement": false}, "duration": {"name": "duration", "type": "<PERSON><PERSON><PERSON>(150)", "primaryKey": false, "notNull": false, "autoincrement": false}, "amount": {"name": "amount", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}, "morning": {"name": "morning", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}, "noon": {"name": "noon", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}, "afternoon": {"name": "afternoon", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}, "evening": {"name": "evening", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}, "night": {"name": "night", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"presrciption_visit_id_visit_id_fk": {"name": "presrciption_visit_id_visit_id_fk", "tableFrom": "presrciption", "tableTo": "visit", "columnsFrom": ["visit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "presrciption_progress_note_id_progress_note_id_fk": {"name": "presrciption_progress_note_id_progress_note_id_fk", "tableFrom": "presrciption", "tableTo": "progress_note", "columnsFrom": ["progress_note_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "presrciption_product_id_product_id_fk": {"name": "presrciption_product_id_product_id_fk", "tableFrom": "presrciption", "tableTo": "product", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "presrciption_unit_id_unit_id_fk": {"name": "presrciption_unit_id_unit_id_fk", "tableFrom": "presrciption", "tableTo": "unit", "columnsFrom": ["unit_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"presrciption_id": {"name": "presrciption_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "product": {"name": "product", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "products": {"name": "products", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "generic_name": {"name": "generic_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "barcode": {"name": "barcode", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "group_id": {"name": "group_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "laboratory_group_id": {"name": "laboratory_group_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "price": {"name": "price", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "brand_id": {"name": "brand_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "category_id": {"name": "category_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "unit_id": {"name": "unit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "create_at": {"name": "create_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"product_group_id_group_id_fk": {"name": "product_group_id_group_id_fk", "tableFrom": "product", "tableTo": "group", "columnsFrom": ["group_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "product_laboratory_group_id_laboratory_group_id_fk": {"name": "product_laboratory_group_id_laboratory_group_id_fk", "tableFrom": "product", "tableTo": "laboratory_group", "columnsFrom": ["laboratory_group_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "product_brand_id_brand_id_fk": {"name": "product_brand_id_brand_id_fk", "tableFrom": "product", "tableTo": "brand", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "product_category_id_category_id_fk": {"name": "product_category_id_category_id_fk", "tableFrom": "product", "tableTo": "category", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "product_unit_id_unit_id_fk": {"name": "product_unit_id_unit_id_fk", "tableFrom": "product", "tableTo": "unit", "columnsFrom": ["unit_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"product_id": {"name": "product_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "product_order": {"name": "product_order", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "price": {"name": "price", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "total": {"name": "total", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "qty": {"name": "qty", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "qty_adjustment": {"name": "qty_adjustment", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "inventory_id": {"name": "inventory_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "discount": {"name": "discount", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "product_id": {"name": "product_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "unit_id": {"name": "unit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "charge_id": {"name": "charge_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"product_order_inventory_id_inventory_id_fk": {"name": "product_order_inventory_id_inventory_id_fk", "tableFrom": "product_order", "tableTo": "inventory", "columnsFrom": ["inventory_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "product_order_product_id_product_id_fk": {"name": "product_order_product_id_product_id_fk", "tableFrom": "product_order", "tableTo": "product", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "product_order_unit_id_unit_id_fk": {"name": "product_order_unit_id_unit_id_fk", "tableFrom": "product_order", "tableTo": "unit", "columnsFrom": ["unit_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "product_order_charge_id_charge_id_fk": {"name": "product_order_charge_id_charge_id_fk", "tableFrom": "product_order", "tableTo": "charge", "columnsFrom": ["charge_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"product_order_id": {"name": "product_order_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "progress_note": {"name": "progress_note", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "date_checkup": {"name": "date_checkup", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "date_checkout": {"name": "date_checkout", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_discharge": {"name": "is_discharge", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "is_emr": {"name": "is_emr", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "staff_id": {"name": "staff_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "etiology": {"name": "etiology", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'LOADING'"}, "department_id": {"name": "department_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "patient_id": {"name": "patient_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "inclund_pay": {"name": "inclund_pay", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"progress_note_staff_id_staff_id_fk": {"name": "progress_note_staff_id_staff_id_fk", "tableFrom": "progress_note", "tableTo": "staff", "columnsFrom": ["staff_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "progress_note_department_id_product_id_fk": {"name": "progress_note_department_id_product_id_fk", "tableFrom": "progress_note", "tableTo": "product", "columnsFrom": ["department_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "progress_note_patient_id_patient_id_fk": {"name": "progress_note_patient_id_patient_id_fk", "tableFrom": "progress_note", "tableTo": "patient", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"progress_note_id": {"name": "progress_note_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "provice": {"name": "provice", "columns": {"provice_id": {"name": "provice_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name_khmer": {"name": "name_khmer", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name_latin": {"name": "name_latin", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false}, "contry_id": {"name": "contry_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"provice_provice_id": {"name": "provice_provice_id", "columns": ["provice_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "result_form": {"name": "result_form", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "index": {"name": "index", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'text'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"result_form_id": {"name": "result_form_id", "columns": ["id"]}}, "uniqueConstraints": {"result_form_name_unique": {"name": "result_form_name_unique", "columns": ["name"]}}, "checkConstraint": {}}, "result_imagerie": {"name": "result_imagerie", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "result": {"name": "result", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "imagerie_request_id": {"name": "imagerie_request_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "result_form_id": {"name": "result_form_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"result_imagerie_imagerie_request_id_imagerie_request_id_fk": {"name": "result_imagerie_imagerie_request_id_imagerie_request_id_fk", "tableFrom": "result_imagerie", "tableTo": "imagerie_request", "columnsFrom": ["imagerie_request_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "result_imagerie_result_form_id_result_form_id_fk": {"name": "result_imagerie_result_form_id_result_form_id_fk", "tableFrom": "result_imagerie", "tableTo": "result_form", "columnsFrom": ["result_form_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"result_imagerie_id": {"name": "result_imagerie_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "role": {"name": "role", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"role_id": {"name": "role_id", "columns": ["id"]}}, "uniqueConstraints": {"role_role_unique": {"name": "role_role_unique", "columns": ["role"]}}, "checkConstraint": {}}, "room": {"name": "room", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "room": {"name": "room", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "product_id": {"name": "product_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "department_id": {"name": "department_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "ward_id": {"name": "ward_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"room_product_id_product_id_fk": {"name": "room_product_id_product_id_fk", "tableFrom": "room", "tableTo": "product", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "set null"}, "room_department_id_product_id_fk": {"name": "room_department_id_product_id_fk", "tableFrom": "room", "tableTo": "product", "columnsFrom": ["department_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "set null"}, "room_ward_id_ward_id_fk": {"name": "room_ward_id_ward_id_fk", "tableFrom": "room", "tableTo": "ward", "columnsFrom": ["ward_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"room_id": {"name": "room_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "salary": {"name": "salary", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "base_salary": {"name": "base_salary", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "allowance": {"name": "allowance", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "deduction": {"name": "deduction", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "bonus": {"name": "bonus", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "total_salary": {"name": "total_salary", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "staff_id": {"name": "staff_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "effective_date": {"name": "effective_date", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"salary_staff_id_staff_id_fk": {"name": "salary_staff_id_staff_id_fk", "tableFrom": "salary", "tableTo": "staff", "columnsFrom": ["staff_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"salary_id": {"name": "salary_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "service": {"name": "service", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "visit_id": {"name": "visit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "progress_note_id": {"name": "progress_note_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "product_id": {"name": "product_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_paid_ipd": {"name": "is_paid_ipd", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}}, "indexes": {}, "foreignKeys": {"service_visit_id_visit_id_fk": {"name": "service_visit_id_visit_id_fk", "tableFrom": "service", "tableTo": "visit", "columnsFrom": ["visit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "service_progress_note_id_progress_note_id_fk": {"name": "service_progress_note_id_progress_note_id_fk", "tableFrom": "service", "tableTo": "progress_note", "columnsFrom": ["progress_note_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "service_product_id_product_id_fk": {"name": "service_product_id_product_id_fk", "tableFrom": "service", "tableTo": "product", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"service_id": {"name": "service_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "service_type": {"name": "service_type", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "by": {"name": "by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"service_type_id": {"name": "service_type_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "session": {"name": "session", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"session_id": {"name": "session_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "setting": {"name": "setting", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "print_bill": {"name": "print_bill", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "warring_expire_day": {"name": "warring_expire_day", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 14}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"setting_id": {"name": "setting_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "staff": {"name": "staff", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "gender": {"name": "gender", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "autoincrement": false}, "specialist": {"name": "specialist", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "designation_id": {"name": "designation_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "title_id": {"name": "title_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "id_staff": {"name": "id_staff", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "blood_group": {"name": "blood_group", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "name_khmer": {"name": "name_khmer", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "base_salary": {"name": "base_salary", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "name_latin": {"name": "name_latin", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "dob": {"name": "dob", "type": "date", "primaryKey": false, "notNull": false, "autoincrement": false}, "datetime_start": {"name": "datetime_start", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "datetime_stop": {"name": "datetime_stop", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "telephone": {"name": "telephone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "village_id": {"name": "village_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "commune_id": {"name": "commune_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "district_id": {"name": "district_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "province_id": {"name": "province_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"staff_designation_id_designation_id_fk": {"name": "staff_designation_id_designation_id_fk", "tableFrom": "staff", "tableTo": "designation", "columnsFrom": ["designation_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "cascade"}, "staff_title_id_title_id_fk": {"name": "staff_title_id_title_id_fk", "tableFrom": "staff", "tableTo": "title", "columnsFrom": ["title_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "cascade"}, "staff_village_id_village_village_id_fk": {"name": "staff_village_id_village_village_id_fk", "tableFrom": "staff", "tableTo": "village", "columnsFrom": ["village_id"], "columnsTo": ["village_id"], "onDelete": "no action", "onUpdate": "no action"}, "staff_commune_id_commune_commune_id_fk": {"name": "staff_commune_id_commune_commune_id_fk", "tableFrom": "staff", "tableTo": "commune", "columnsFrom": ["commune_id"], "columnsTo": ["commune_id"], "onDelete": "no action", "onUpdate": "no action"}, "staff_district_id_district_district_id_fk": {"name": "staff_district_id_district_district_id_fk", "tableFrom": "staff", "tableTo": "district", "columnsFrom": ["district_id"], "columnsTo": ["district_id"], "onDelete": "no action", "onUpdate": "no action"}, "staff_province_id_provice_provice_id_fk": {"name": "staff_province_id_provice_provice_id_fk", "tableFrom": "staff", "tableTo": "provice", "columnsFrom": ["province_id"], "columnsTo": ["provice_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"staff_id": {"name": "staff_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "staff_to_department": {"name": "staff_to_department", "columns": {"staff_id": {"name": "staff_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "department_id": {"name": "department_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"staff_to_department_staff_id_staff_id_fk": {"name": "staff_to_department_staff_id_staff_id_fk", "tableFrom": "staff_to_department", "tableTo": "staff", "columnsFrom": ["staff_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "staff_to_department_department_id_product_id_fk": {"name": "staff_to_department_department_id_product_id_fk", "tableFrom": "staff_to_department", "tableTo": "product", "columnsFrom": ["department_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"staff_to_department_staff_id_department_id_pk": {"name": "staff_to_department_staff_id_department_id_pk", "columns": ["staff_id", "department_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "staff_to_role": {"name": "staff_to_role", "columns": {"staff_id": {"name": "staff_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "role_id": {"name": "role_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"staff_to_role_staff_id_staff_id_fk": {"name": "staff_to_role_staff_id_staff_id_fk", "tableFrom": "staff_to_role", "tableTo": "staff", "columnsFrom": ["staff_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "staff_to_role_role_id_role_id_fk": {"name": "staff_to_role_role_id_role_id_fk", "tableFrom": "staff_to_role", "tableTo": "role", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"staff_to_role_staff_id_role_id_pk": {"name": "staff_to_role_staff_id_role_id_pk", "columns": ["staff_id", "role_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "sub_unit": {"name": "sub_unit", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "qty_per_unit": {"name": "qty_per_unit", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "price": {"name": "price", "type": "decimal(18,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "unit_id": {"name": "unit_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_id": {"name": "product_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"sub_unit_unit_id_unit_id_fk": {"name": "sub_unit_unit_id_unit_id_fk", "tableFrom": "sub_unit", "tableTo": "unit", "columnsFrom": ["unit_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "sub_unit_product_id_product_id_fk": {"name": "sub_unit_product_id_product_id_fk", "tableFrom": "sub_unit", "tableTo": "product", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"sub_unit_id": {"name": "sub_unit_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "subjective": {"name": "subjective", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "cheif_complaint": {"name": "cheif_complaint", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "current_medication": {"name": "current_medication", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "history_of_present_illness": {"name": "history_of_present_illness", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "past_medical_history": {"name": "past_medical_history", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "allesgy_medicine": {"name": "allesgy_medicine", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "surgical_history": {"name": "surgical_history", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "pre_diagnosis": {"name": "pre_diagnosis", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "family_and_social_history": {"name": "family_and_social_history", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "visit_id": {"name": "visit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"subjective_visit_id_visit_id_fk": {"name": "subjective_visit_id_visit_id_fk", "tableFrom": "subjective", "tableTo": "visit", "columnsFrom": ["visit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"subjective_id": {"name": "subjective_id", "columns": ["id"]}}, "uniqueConstraints": {"subjective_visit_id_unique": {"name": "subjective_visit_id_unique", "columns": ["visit_id"]}}, "checkConstraint": {}}, "supplier": {"name": "supplier", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "contact": {"name": "contact", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "company_name": {"name": "company_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"supplier_id": {"name": "supplier_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "tax": {"name": "tax", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "value": {"name": "value", "type": "float", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"tax_id": {"name": "tax_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "template": {"name": "template", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "diagnosis": {"name": "diagnosis", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "template": {"name": "template", "type": "longtext", "primaryKey": false, "notNull": false, "autoincrement": false}, "group_id": {"name": "group_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"template_group_id_group_id_fk": {"name": "template_group_id_group_id_fk", "tableFrom": "template", "tableTo": "group", "columnsFrom": ["group_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"template_id": {"name": "template_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "test": {"name": "test", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "float": {"name": "float", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}, "decimal": {"name": "decimal", "type": "decimal(4,2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "datetime": {"name": "datetime", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "time": {"name": "time", "type": "time", "primaryKey": false, "notNull": false, "autoincrement": false}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"test_id": {"name": "test_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "title": {"name": "title", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "kh": {"name": "kh", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "eng": {"name": "eng", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"title_id": {"name": "title_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "unit": {"name": "unit", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "unit": {"name": "unit", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "vaccine_dose": {"name": "vaccine_dose", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"unit_id": {"name": "unit_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "units_to_groups": {"name": "units_to_groups", "columns": {"unit_id": {"name": "unit_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "group_id": {"name": "group_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"units_to_groups_unit_id_unit_id_fk": {"name": "units_to_groups_unit_id_unit_id_fk", "tableFrom": "units_to_groups", "tableTo": "unit", "columnsFrom": ["unit_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "units_to_groups_group_id_group_id_fk": {"name": "units_to_groups_group_id_group_id_fk", "tableFrom": "units_to_groups", "tableTo": "group", "columnsFrom": ["group_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"units_to_groups_unit_id_group_id_pk": {"name": "units_to_groups_unit_id_group_id_pk", "columns": ["unit_id", "group_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "uploads": {"name": "uploads", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "mimeType": {"name": "mimeType", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "related_id": {"name": "related_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "related_type": {"name": "related_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"uploads_id": {"name": "uploads_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "use": {"name": "use", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(150)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"use_id": {"name": "use_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "user": {"name": "user", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "staff_id": {"name": "staff_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"user_staff_id_staff_id_fk": {"name": "user_staff_id_staff_id_fk", "tableFrom": "user", "tableTo": "staff", "columnsFrom": ["staff_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"user_id": {"name": "user_id", "columns": ["id"]}}, "uniqueConstraints": {"user_username_unique": {"name": "user_username_unique", "columns": ["username"]}}, "checkConstraint": {}}, "vaccine": {"name": "vaccine", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "visit_id": {"name": "visit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "injection_id": {"name": "injection_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "product_id": {"name": "product_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "discription": {"name": "discription", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"vaccine_visit_id_visit_id_fk": {"name": "vaccine_visit_id_visit_id_fk", "tableFrom": "vaccine", "tableTo": "visit", "columnsFrom": ["visit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "vaccine_injection_id_injection_id_fk": {"name": "vaccine_injection_id_injection_id_fk", "tableFrom": "vaccine", "tableTo": "injection", "columnsFrom": ["injection_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "vaccine_product_id_product_id_fk": {"name": "vaccine_product_id_product_id_fk", "tableFrom": "vaccine", "tableTo": "product", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {"vaccine_id": {"name": "vaccine_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "village": {"name": "village", "columns": {"village_id": {"name": "village_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name_khmer": {"name": "name_khmer", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name_latin": {"name": "name_latin", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false}, "commune_id": {"name": "commune_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"village_commune_id_commune_commune_id_fk": {"name": "village_commune_id_commune_commune_id_fk", "tableFrom": "village", "tableTo": "commune", "columnsFrom": ["commune_id"], "columnsTo": ["commune_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"village_village_id": {"name": "village_village_id", "columns": ["village_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "visit": {"name": "visit", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "date_checkup": {"name": "date_checkup", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "patient_id": {"name": "patient_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "department_id": {"name": "department_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "staff_id": {"name": "staff_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "sender_id": {"name": "sender_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "checkin_type": {"name": "checkin_type", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "autoincrement": false}, "etiology": {"name": "etiology", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "transfer": {"name": "transfer", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'LOADING'"}, "progress_note_id": {"name": "progress_note_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"visit_patient_id_patient_id_fk": {"name": "visit_patient_id_patient_id_fk", "tableFrom": "visit", "tableTo": "patient", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "visit_department_id_product_id_fk": {"name": "visit_department_id_product_id_fk", "tableFrom": "visit", "tableTo": "product", "columnsFrom": ["department_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "visit_staff_id_staff_id_fk": {"name": "visit_staff_id_staff_id_fk", "tableFrom": "visit", "tableTo": "staff", "columnsFrom": ["staff_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "visit_sender_id_staff_id_fk": {"name": "visit_sender_id_staff_id_fk", "tableFrom": "visit", "tableTo": "staff", "columnsFrom": ["sender_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "visit_progress_note_id_progress_note_id_fk": {"name": "visit_progress_note_id_progress_note_id_fk", "tableFrom": "visit", "tableTo": "progress_note", "columnsFrom": ["progress_note_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"visit_id": {"name": "visit_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "vital_sign": {"name": "vital_sign", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "dbp": {"name": "dbp", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}, "sbp": {"name": "sbp", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}, "pulse": {"name": "pulse", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}, "t": {"name": "t", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}, "sp02": {"name": "sp02", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}, "height": {"name": "height", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}, "weight": {"name": "weight", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}, "rr": {"name": "rr", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}, "bmi": {"name": "bmi", "type": "float", "primaryKey": false, "notNull": false, "autoincrement": false}, "visit_id": {"name": "visit_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "datetime": {"name": "datetime", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "stool": {"name": "stool", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "urine": {"name": "urine", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "piv": {"name": "piv", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "drink": {"name": "drink", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "nasogastric_tube_in": {"name": "nasogastric_tube_in", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "nasogastric_tube_out": {"name": "nasogastric_tube_out", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "fluid_out": {"name": "fluid_out", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "vomiting": {"name": "vomiting", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "by": {"name": "by", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "progress_note_id": {"name": "progress_note_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "active_department_id": {"name": "active_department_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"vital_sign_visit_id_visit_id_fk": {"name": "vital_sign_visit_id_visit_id_fk", "tableFrom": "vital_sign", "tableTo": "visit", "columnsFrom": ["visit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "vital_sign_by_staff_id_fk": {"name": "vital_sign_by_staff_id_fk", "tableFrom": "vital_sign", "tableTo": "staff", "columnsFrom": ["by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vital_sign_progress_note_id_progress_note_id_fk": {"name": "vital_sign_progress_note_id_progress_note_id_fk", "tableFrom": "vital_sign", "tableTo": "progress_note", "columnsFrom": ["progress_note_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "vital_sign_active_department_id_active_department_id_fk": {"name": "vital_sign_active_department_id_active_department_id_fk", "tableFrom": "vital_sign", "tableTo": "active_department", "columnsFrom": ["active_department_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"vital_sign_id": {"name": "vital_sign_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "ward": {"name": "ward", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "ward": {"name": "ward", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"ward_id": {"name": "ward_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "words": {"name": "words", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'common'"}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"words_id": {"name": "words_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}