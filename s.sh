#!/bin/bash

# set -e  # Exit on any error
# === Update System ===
# === Configuration Variables ===
SWAP_SIZE="2G"
NEW_USER="coffee"
PASSWORD="atthemorning"

# === Add Swap Space ===
echo "Adding $SWAP_SIZE swap space..."
sudo fallocate -l "$SWAP_SIZE" /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab

# === Create a New Sudo User ===
echo "Creating new sudo user: $NEW_USER"
sudo adduser --disabled-password --gecos "" "$NEW_USER"
echo "$NEW_USER:$PASSWORD" | sudo chpasswd
sudo usermod -aG sudo "$NEW_USER"

# === Disable Root SSH Login ===
echo "Disabling root SSH login..."
sudo sed -i 's/^PermitRootLogin.*/PermitRootLogin no/' /etc/ssh/sshd_config
grep -q "^PermitRootLogin" /etc/ssh/sshd_config || echo "PermitRootLogin no" | sudo tee -a /etc/ssh/sshd_config
sudo systemctl reload ssh
sudo passwd -l root
# === Remove Old Swap Space
# sudo swapoff /swapfile
# swapon --show
# sudo rm /swapfile
sudo su - $NEW_USER