(function(n,t){typeof exports=="object"&&typeof module!="undefined"?module.exports=t():typeof define=="function"&&define.amd?define(t):(n=typeof globalThis!="undefined"?globalThis:n||self,n.bs5=t())})(this,function(){"use strict";return{Toast:class{#n;#s;#h;#l;#a;#d;#r;#c;#o;#f;#i;#e;constructor(t){this.#n=this.#t(t.body,""),this.#s=this.#t(t.animation,!0),this.#h=this.#t(t.autohide,!0),this.#l=this.#t(t.btnClose,!0),this.#a=this.#t(t.btnCloseWhite,!1),this.#d=this.#t(t.className,""),this.#r=this.#t(t.delay,5e3),this.#c=this.#t(t.gap,16),this.#o=this.#t(t.header,""),this.#f=this.#t(t.margin,"1rem"),this.#i=this.#t(t.placement,"top-right"),this.#e=this.#i.split("-");const s='<button type="button" hidden class="btn-close flex-shrink-0" data-bs-dismiss="toast" aria-label="Close"></button>';let o=`style="margin:${this.#f};${this.#e[0]}:0;${this.#e[1]}:${this.#s?"-50%":0};z-index:1081"`,i=document.createElement("template");i.innerHTML=`<div class="toast position-fixed toast-${this.#i} ${this.#d}" ${o} role="alert" aria-live="assertive" aria-atomic="true">
				<div class="toast-header" hidden><div class="d-flex align-items-center flex-grow-1">${this.#o}</div>${s}</div>
				<div class="toast-body"><div class="d-flex w-100"><div class="flex-grow-1">${this.#n}</div>${s}</div></div>
			</div>`;let h=i.content.firstChild;if(h instanceof HTMLDivElement){const e=h.querySelectorAll(".btn-close");e.forEach(l=>{this.#l&&l.removeAttribute("hidden"),this.#a&&l.classList.add("btn-close-white")}),this.#o!==""&&(h.querySelector(".toast-header").removeAttribute("hidden"),e[1].remove()),this.element=h}document.body.insertAdjacentElement("afterbegin",this.element),this.bootstrapToast=new bootstrap.Toast(this.element,{animation:this.#s,autohide:this.#h,delay:this.#r}),this.element.addEventListener("hidden.bs.toast",()=>{this.element.remove(),this.#u()}),this.element.addEventListener("show.bs.toast",()=>{let e=this,l=setInterval(d,0);function d(){if(e.element.offsetHeight>0){if(clearInterval(l),e.#s){const a=parseFloat(getComputedStyle(e.element).transitionDuration)*1e3;e.element.style.transition=`all ${a*4}ms cubic-bezier(0.16, 1, 0.3, 1), opacity ${a}ms linear`,e.element.style[e.#e[1]]=0}e.#u()}}})}#t(t,s){return t!==void 0?t:s}show(){this.bootstrapToast.show()}hide(){this.bootstrapToast.hide()}#u(){const t=document.body.querySelectorAll(`.toast-${this.#i}`);let s=[];t.forEach((o,i)=>{o instanceof HTMLElement&&(i===0&&s.push(0),t[i+1]instanceof HTMLElement&&s.push(s[i]+o.offsetHeight),o.style[this.#e[0]]=s[i]+this.#c*i+"px")})}}}});
