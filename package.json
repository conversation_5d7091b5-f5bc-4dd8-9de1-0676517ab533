{"name": "hms", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev --port 3000", "build": "vite build", "prod": "node -r dotenv/config server.js ", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test": "vitest", "lint": "prettier --check . && eslint .", "format": "prettier --write .", "db:push": "drizzle-kit push ", "db:gen": "drizzle-kit generate ", "db:mig": "drizzle-kit migrate", "db:seedd": "tsx src/lib/server/db/seed.ts ", "db:drop": "tsx src/lib/server/db/drop.ts", "db:create": "tsx src/lib/server/db/create.ts "}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@playwright/test": "^1.49.1", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/enhanced-img": "^0.6.0", "@sveltejs/kit": "^2.26.1", "@sveltejs/vite-plugin-svelte": "^6.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.4", "@types/archiver": "^6.0.3", "@types/node": "^22", "@types/qrcode": "^1.5.5", "@types/unzipper": "^0.10.11", "drizzle-kit": "^0.30.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "html5-qrcode": "^2.3.8", "jsdom": "^26.0.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "svelte": "^5.33.14", "svelte-check": "^4.3.0", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-eslint": "^8.20.0", "vite": "^6.2.6", "vitest": "^3.0.0"}, "dependencies": {"@cropper/element-canvas": "^2.0.0", "@node-rs/argon2": "^2.0.2", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "archiver": "^7.0.1", "chart.js": "^4.4.7", "dotenv": "^16.4.5", "drizzle-orm": "^0.44.2", "eslint-plugin-drizzle": "^0.2.3", "express": "^5.1.0", "jsbarcode": "^3.11.6", "jsqr": "^1.4.0", "mysql2": "^3.12.0", "qrcode": "^1.5.4", "sharp": "^0.34.2", "suneditor": "^2.47.2", "unzipper": "^0.12.3", "vite-imagetools": "^7.1.0"}}