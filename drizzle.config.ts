import 'dotenv/config';
import { defineConfig } from 'drizzle-kit';
const { DB_HOST, DB_USER, DB_PORT, DB_NAME, DB_PASSWORD } = process.env;
if (!DB_HOST || !DB_PORT || !DB_USER || !DB_PASSWORD || !DB_NAME)
	throw new Error('DATABASE_URL is not set');
const db_connection = `mysql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}`;
export default defineConfig({
	schema: './src/lib/server/schemas/index.ts',
	dbCredentials: {
		url: db_connection
	},
	dialect: 'mysql',
	strict: true,
	verbose: true
});
