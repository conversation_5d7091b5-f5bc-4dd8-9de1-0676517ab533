<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	import type { ActionData, PageServerData } from '../../routes/(dash)/settup/img-template/$types';
	type Data = Pick<PageServerData, 'get_templates' | 'get_groups'>;
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import TextEditor from '$lib/coms-cu/TextEditor.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	interface Props {
		form: ActionData;
		template_id: number | null | undefined;
		data: Data;
	}

	let { form, template_id = $bindable(), data }: Props = $props();
	let { get_templates, get_groups } = $derived(data);
	let find_template = $derived(get_templates[0]);
	let loading = $state(false);
</script>

<!-- @_List_Parameter -->
<div class="modal fade" id="create_template" data-bs-backdrop="static" data-bs-focus="false">
	<div class="modal-dialog modal-dialog-scrollabl modal-xl">
		<Form
			enctype="multipart/form-data"
			action={find_template?.id ? '?/update_template' : '?/create_template'}
			method="post"
			fnSuccess={() => {
				template_id = 0;
				document.getElementById('close_create_template')?.click();
			}}
			bind:loading
			class="modal-content"
		>
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('template_imagerie')}</h4>
				<button
					id="close_create_template"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="card-body pt-0">
				<div class="modal-body">
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<input value={find_template?.id} type="hidden" name="template_id" />
								<label for="diagnosis">{locale.T('diagnosis')}</label>
								<input
									value={find_template?.diagnosis ?? ''}
									name="diagnosis"
									type="diagnosis"
									class="form-control"
									id="diagnosis"
								/>
								{#if form?.diagnosis}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if}
							</div>
						</div>
						<div class="col-12">
							<label for="diagnosis">{locale.T('imagerie_group')}</label>
							<select value={find_template?.group_id} name="group_id" class="form-control" id="">
								<option value="">None</option>
								{#each get_groups as item}
									<option value={item.id}>{item?.name ?? ''}</option>
								{/each}
							</select>
							{#if form?.group_id}
								<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
							{/if}
						</div>
						<div class="col-12">
							<div class=" pb-3">
								<label for="template">{locale.T('template')}</label>
								<TextEditor name="template_" setValue={find_template?.template ?? ''} />
								{#if form?.template_}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if}
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>
