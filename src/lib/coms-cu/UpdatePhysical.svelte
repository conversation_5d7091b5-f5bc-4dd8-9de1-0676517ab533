<script lang="ts">
	import type { PageServerData } from '../../routes/(dash)/settup/physical-exam/$types';
	import { enhance } from '$app/forms';
	import SubmitButton from '../coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { SubmitFunction } from '@sveltejs/kit';
	type Data = Pick<PageServerData, 'get_examas'>;
	interface Props {
		data: Data;
		exam_id?: number;
	}

	let { data, exam_id = $bindable() }: Props = $props();
	let { get_examas } = $derived(data);
	let find_exam = $derived(get_examas[0]);
	let loading = $state(false);
	const onSubmit: SubmitFunction = () => {
		loading = true;
		return async ({ update, result, action }) => {
			await update();
			loading = false;
			if (result.type !== 'failure') {
				if (action.search !== '?/delete_physical') {
					exam_id = 0;
					document.getElementById('close_update_physical')?.click();
				}
			}
		};
	};
</script>

<!-- @_Add_MedicineType -->
<div class="modal fade" id="update-physical" data-bs-backdrop="static">
	<div class="modal-dialog modal-xl">
		<form action="?/delete_physical" method="post" class="modal-content" use:enhance={onSubmit}>
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('physical')}</h4>
				<button
					id="close_update_physical"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div class="card-body pt-0">
					<div class="row">
						{#each find_exam?.physical || [] as item}
							<div class="col-10">
								<div class=" pb-3">
									<input
										value={item?.physical ?? ''}
										name="physical_name"
										type="text"
										class="form-control"
										id="physical_name"
									/>
									<!-- {#if form?.examination}
								<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if} -->
								</div>
							</div>
							<div class="col-2">
								<input type="hidden" name="id" value={item?.id ?? ''} />
								<button
									onclick={(e) =>
										confirm(locale.T('confirm_delete')) && e.currentTarget.form?.requestSubmit()}
									aria-label="submit"
									class="btn btn-danger btn"
									type="button"><i class="fa-solid fa-trash-can"></i></button
								>
							</div>
						{/each}
					</div>
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton formaction="?/update_physical" {loading} />
			</div>
		</form>
	</div>
</div>
