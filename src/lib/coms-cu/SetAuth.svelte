<script lang="ts">
	import { enhance } from '$app/forms';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { SubmitFunction } from '@sveltejs/kit';
	interface Props {
		staf_id: number;
		staff_name: string;
		role: string;
		user_id: string | null;
	}
	let { staf_id, staff_name, role, user_id }: Props = $props();
	let loading = $state(false);
	let id = $state(`id${Math.random().toString(36).substring(2, 9)}`);
	let message = $state('');
	let modal = $state(false);
	const onSubmit: SubmitFunction = () => {
		loading = true;
		return async ({ update, result }) => {
			await update();
			loading = false;
			if ('data' in result) {
				message = result?.data?.message ? String(result?.data?.message) : '';
			} else {
				message = '';
			}
			if (result.type !== 'failure') {
				user_id = null;
				modal = false;
				document.getElementById('close_set_auth')?.click();
			}
		};
	};
</script>

<button
	onclick={() => (modal = true)}
	aria-label="Set Authentication"
	type="button"
	class="btn btn-warning btn-sm"
	data-bs-toggle="modal"
	data-bs-target={`#${id}`}
>
	{#if user_id}
		<i class="fa-solid fa-key"></i>
	{:else}
		<i class="fa-solid fa-unlock-keyhole"></i>
	{/if}
</button>
<div
	class="modal fade"
	{id}
	tabindex="-1"
	data-bs-backdrop="static"
	aria-labelledby="exampleModalLabel"
	aria-hidden="true"
>
	<div class="modal-dialog">
		{#if modal}
			<form
				method="post"
				action={user_id ? '/user?/update_user' : '/user?/register'}
				use:enhance={onSubmit}
				class="modal-content"
			>
				<div class="modal-header">
					<h1 class="modal-title fs-5" id="exampleModalLabel">
						{locale.T('staff')}
						#{staff_name}
					</h1>

					<button
						onclick={() => (modal = false)}
						type="button"
						id="close_set_auth"
						class="btn-close"
						data-bs-dismiss="modal"
						aria-label="Close"
					></button>
				</div>
				<div class="modal-body">
					{#if message}
						<div class="alert alert-danger py-2" role="alert">
							{message}
						</div>
					{/if}
					{#if user_id}
						<div>
							<div class="mb-3">
								<label for="message-text" class="col-form-label">{locale.T('password')}</label>
								<input class="form-control" type="password" name="password" id="message-text" />
							</div>
							<div class="mb-3">
								<label for="recipient-name" class="col-form-label">{locale.T('username')}</label>
								<input type="text" name="username" class="form-control" id="recipient-name" />
							</div>
							<div class="mb-3">
								<label for="new_password" class="col-form-label">{locale.T('new_password')}</label>
								<input class="form-control" type="password" name="new_password" id="new_password" />
							</div>
						</div>
					{:else}
						<div>
							<input type="hidden" name="role" value={role} />
							<input type="hidden" name="staff_id" value={staf_id} />
							<div class="mb-3">
								<label for="recipient-name" class="col-form-label">{locale.T('username')}</label>
								<input type="text" name="username" class="form-control" id="recipient-name" />
							</div>
							<div class="mb-3">
								<label for="message-text" class="col-form-label">{locale.T('password')}</label>
								<input class="form-control" type="password" name="password" id="message-text" />
							</div>
						</div>
					{/if}
				</div>
				<div class="modal-footer">
					<SubmitButton {loading} />
				</div>
			</form>
		{/if}
	</div>
</div>
