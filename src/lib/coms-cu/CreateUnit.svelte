<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from '../../routes/(dash)/product/category/$types';
	import Form from '$lib/coms-form/Form.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import { store } from '$lib/store/store.svelte';
	interface Props {
		get_units: PageServerData['get_units'];
	}
	let { get_units }: Props = $props();
	let unit_id: number | null = $state(null);
	let find_unit = $derived(get_units.find((e) => e.id === unit_id));
	let loading = $state(false);
	function handleClick(id: number) {
		if (unit_id === id) {
			unit_id = null;
			return;
		} else {
			unit_id = id;
		}
	}
	let inerHight = $derived(
		(Number(store.inerHight.replace('px', '')) - 110).toString().concat('px')
	);
</script>

<DeleteModal delete_modal="delete_unit" action="?/delete_unit" id={unit_id} />

<div class="modal fade" id="create_new_unit" data-bs-backdrop="static">
	<div class="modal-dialog modal-dialog-scrollabl modal-xl">
		<Form
			action={find_unit?.id ? '?/update_unit' : '?/create_unit'}
			method="post"
			bind:loading
			fnSuccess={() => {
				unit_id = null;
			}}
			class="modal-content"
		>
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('unit')}</h4>
				<button
					id="close_create_unit"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="card-body pt-0">
				<div class="card-header px-3 pt-3">
					<div class="row">
						<div class="col-10">
							<input value={find_unit?.id} type="hidden" name="unit_id" />
							<input
								value={find_unit?.unit ?? ''}
								name="unit_"
								type="text"
								class="form-control"
								id="unit"
							/>
						</div>
						<div class="col-2">
							<button class="btn btn-success w-100">{locale.T('save')}</button>
						</div>
					</div>
				</div>
				<div class="modal-body">
					<div style="height: {inerHight}" class="card-body table-responsive p-0 m-0">
						<table class="table table-bordered table-hover text-nowrap table-light">
							<thead class="sticky-top top-0 bg-light table-active">
								<tr>
									<th>{locale.T('n')}</th>
									<th>{locale.T('unit')}</th>
									<th>{locale.T('action')}</th>
								</tr>
							</thead>
							<tbody>
								{#each get_units as item, index (item.id)}
									<tr class={unit_id === item.id ? 'table-primary' : ''}>
										<td>{index + 1}</td>
										<td>{item.unit}</td>
										<td>
											<button
												aria-label="createunit"
												onclick={() => handleClick(item.id)}
												type="button"
												class="btn btn-primary btn-sm"
												><i class="fa-solid fa-file-pen"></i>
											</button>
											<button
												aria-label="deletemodal"
												onclick={() => {
													unit_id = item.id;
												}}
												type="button"
												class="btn btn-danger btn-sm"
												data-bs-toggle="modal"
												data-bs-target="#delete_unit"
												><i class="fa-solid fa-trash-can"></i>
											</button>
										</td>
									</tr>
								{/each}
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</Form>
	</div>
</div>
