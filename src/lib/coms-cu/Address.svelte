<script lang="ts">
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import { type PageServerData } from '../../routes/(dash)/staff/create/$types';
	type Data = Pick<
		PageServerData,
		'get_provinces' | 'get_districts' | 'get_conmunies' | 'get_vilages'
	>;
	interface Props {
		data: Data;
		defaultValue: {
			province_id: number | null;
			district_id: number | null;
			commune_id: number | null;
			village_id: number | null;
		};
	}
	let { data, defaultValue }: Props = $props();
	let { get_provinces, get_districts, get_conmunies, get_vilages } = $derived(data);
	let province_id: number | null = $state(defaultValue.province_id || null);
	let district_id: number | null = $state(defaultValue.district_id || null);
	let commune_id: number | null = $state(defaultValue.commune_id || null);
	let village_id: number | null = $state(defaultValue.village_id || null);
</script>

<div class="row pb-3">
	<div class="col-md-3">
		<div class=" ">
			<label for="province">{locale.T('province')}</label>
			<SelectParam
				fn={() => {
					commune_id = null;
					village_id = null;
					district_id = null;
				}}
				bind:value={province_id}
				name="province_id"
				items={get_provinces.map((e) => ({
					id: e.id,
					name: e.name_khmer.concat(' ').concat(e.name_latin)
				}))}
			/>
		</div>
	</div>
	<div class="col-md-3">
		<div class=" ">
			<label for="district">{locale.T('district')}</label>
			<SelectParam
				fn={() => {
					commune_id = null;
					village_id = null;
				}}
				bind:value={district_id}
				name="district_id"
				items={get_districts.map((e) => ({
					id: e.id,
					name: e.name_khmer.concat(' ').concat(e.name_latin)
				})) || []}
			/>
		</div>
	</div>

	<div class="col-md-3">
		<div class=" ">
			<label for="commune">{locale.T('commune')}</label>
			<SelectParam
				fn={() => {
					village_id = null;
				}}
				bind:value={commune_id}
				name="commune_id"
				items={get_conmunies.map((e) => ({
					id: e.id,
					name: e.name_khmer.concat(' ').concat(e.name_latin)
				})) || []}
			/>
		</div>
	</div>
	<div class="col-md-3">
		<div class=" ">
			<label for="village">{locale.T('village')}</label>
			<SelectParam
				bind:value={village_id}
				name="village_id"
				items={get_vilages.map((e) => ({
					id: e.id,
					name: e.name_khmer.concat(' ').concat(e.name_latin)
				})) || []}
			/>
		</div>
	</div>
</div>
