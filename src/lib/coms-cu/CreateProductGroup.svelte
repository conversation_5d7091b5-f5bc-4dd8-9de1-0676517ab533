<script lang="ts">
	import type { PageServerData } from '../../routes/(dash)/product/$types';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	let category_id: number = $state(0);
	type Data = Pick<PageServerData, 'get_categories'>;
	interface Props {
		data: Data;
	}

	let { data }: Props = $props();
	let { get_categories } = $derived(data);
	let find_category = $derived(get_categories.find((e) => e.id === category_id));
	let loading = $state(false);
</script>

<!-- @_Create Unit -->
<div class="modal fade" id="create_name" data-bs-backdrop="static">
	<div class="modal-dialog modal-dialog-scrollabl modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('product_group')}</h4>
				<button
					onclick={() => (category_id = 0)}
					id="close_create_name"
					type="button"
					class="btn-close"
					data-bs-toggle="modal"
					data-bs-target="#create-product"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body pt-0">
				<Form
					action={find_category?.id ? '?/update_name' : '?/create_name'}
					method="post"
					bind:loading
					class="card-body"
					fnSuccess={() => (category_id = 0)}
				>
					<input type="hidden" name="id" value={find_category?.id} />
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<label for="name">{locale.T('category')}</label>
								<input
									value={find_category?.name ?? ''}
									name="name"
									type="text"
									class="form-control"
									id="name"
								/>
								<!-- {#if form?.descriptionErr}
									<p class="text-danger p-0 m-0">{locale.T('input_data')}</p>
								{/if} -->
							</div>
						</div>
					</div>

					<SubmitButton {loading} />
				</Form>
				<br />
				<div>
					<table class="table text-nowrap table-bordered">
						<thead class="table-light table-active">
							<tr>
								<th>N</th>
								<th>ProductGroup</th>
								<th>Action</th>
							</tr>
						</thead>
						<tbody>
							{#each get_categories as item, index}
								<tr>
									<td>{index + 1}</td>
									<td> {item.name ?? ''} </td>

									<td>
										<div>
											<Form action="?/delete_name" method="post">
												<input type="hidden" name="id" value={item.id} />
												<button
													aria-label="category_id"
													onclick={() => {
														category_id = 0;
														category_id = item.id;
													}}
													type="button"
													class="btn btn-success btn-sm"
													><i class="fa-solid fa-file-pen"></i>
												</button>
												<button aria-label="submit" type="submit" class="btn btn-danger btn-sm"
													><i class="fa-solid fa-trash-can"></i>
												</button>
											</Form>
										</div>
									</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>
