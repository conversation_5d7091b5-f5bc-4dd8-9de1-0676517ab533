<script lang="ts">
	import { browser } from '$app/environment';

	interface Props {
		html?: string | null | undefined;
		// contenteditable?: boolean;
		style?: string;
		class?: string;
	}

	let { html = $bindable(''), style, class: className }: Props = $props();
</script>

<!-- {#if contenteditable}
	<div style="max-width:100%;{style}" contenteditable="true" bind:innerHTML={html}></div>
{:else}
	<div style="max-width:100%;{style}" contenteditable="false" bind:innerHTML={html}></div>
{/if} -->
<div class={className} style="max-width:100%;{style}">
	{#if browser}
		{@html browser ? html : ''}
	{/if}
</div>
