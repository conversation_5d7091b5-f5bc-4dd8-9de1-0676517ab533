<script lang="ts">
	import { navigating } from '$app/state';
	import { fade } from 'svelte/transition';

	let show = $state(false);
	let delayTimer: NodeJS.Timeout;

	$effect(() => {
		if (['form', 'goto', 'leave', 'link'].includes(navigating?.type ?? '')) {
			// Add delay before showing spinner
			delayTimer = setTimeout(() => {
				show = true;
			}, 200);
		} else {
			clearTimeout(delayTimer);
			show = false;
		}

		return () => clearTimeout(delayTimer);
	});
	$inspect(navigating)
</script>

{#if show}
	<!-- Dark overlay only on main content area -->
	<div
		class="position-fixed bg-dark rounded m-1"
		style="top: 56px; left: 225px; right: 0; bottom: 0; z-index: 1050; opacity: 0.1;"
		in:fade={{ duration: 200 }}
		out:fade={{ duration: 100 }}
	></div>

	<!-- Spinner -->
	<div
		class="position-fixed top-50 start-50 translate-middle"
		style="z-index: 1055; margin-left: 112px;"
		in:fade={{ duration: 200 }}
		out:fade={{ duration: 100 }}
	>
		<div class="bg-white rounded-circle p-3 shadow-lg border">
			<i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
		</div>
	</div>
{/if}
