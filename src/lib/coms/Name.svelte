<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	import type { Staff, TTitle } from '$lib/type';
	type NameOnly = Pick<Staff, 'name_latin' | 'name_khmer'>;
	interface Props<T extends NameOnly = NameOnly> {
		name?: T | null;
		class?: string;
		both?: boolean;
		title?: TTitle | null;
	}
	let { both, name, class: className = '', title }: Props<NameOnly> = $props();
</script>

{#if both}
	<span class={className}>
		{title?.kh ?? ''}
		{name?.name_khmer}
	</span>
	<br />
	<span class={className}>
		{name?.name_latin}
	</span>
{:else if locale.L === 'km'}
	<span class={className}>
		{title?.kh}
		{name?.name_khmer}
	</span>
{:else}
	<span class={className}>
		{title?.eng}
		{name?.name_latin || name?.name_khmer}
	</span>
{/if}
