<script lang="ts">
	import 'suneditor/dist/css/suneditor.min.css';
	let id = $state(`id${Math.random().toString(36).substring(2, 9)}`);

	interface Props {
		value: string | null | undefined;
		setWidth?: string;
	}

	let { value, setWidth = '' }: Props = $props();
	let destoryEditor: any = $state();
	$effect(() => {
		(async () => {
			const suneditor = (await import('suneditor')).default as any;
			const plugins = await import('suneditor/src/plugins');
			const element = document.getElementById(id);
			const editor = suneditor.create(element!, {
				font: ['KhmerOSBattambang', 'KhmerOSMuolLight', 'KhmerOSMuol', 'TimesNewRoman'],
				defaultStyle: 'font-size:18px;font-family:KhmerOSBattambang',
				plugins: plugins,
				buttonList: [
					[
						'undo',
						'redo',
						'font',
						'fontSize',
						'formatBlock',
						'paragraphStyle',
						'blockquote',
						'bold',
						'underline',
						'italic',
						'strike',
						'subscript',
						'superscript',
						'fontColor',
						'hiliteColor',
						'textStyle',
						'removeFormat',
						'outdent',
						'indent',
						'align',
						'horizontalRule',
						'list',
						'lineHeight',
						'table',
						'link',
						'image',
						'video',
						'audio' /** 'math', */, // You must add the 'katex' library at options to use the 'math' plugin.
						/** 'imageGallery', */ // You must add the "imageGalleryUrl".
						'fullScreen',
						'showBlocks',
						'codeView',
						'preview',
						'print',
						'save'

						/** 'dir', 'dir_ltr', 'dir_rtl' */ // "dir": Toggle text direction, "dir_ltr": Right to Left, "dir_rtl": Left to Right
					]
				]
			});

			editor.setOptions({
				height: '100%',
				width: setWidth ? setWidth : undefined
			});
			destoryEditor = editor;
			// setWidth
			// 	? editor.setOptions({ height: '100%', width: setWidth })
			// 	: editor.setOptions({ height: '100%' });
			editor.readOnly(true);
			editor.toolbar.hide();
		})();
		return () => {
			destoryEditor.destroy();
		};
	});

	$effect(() => {
		destoryEditor?.setContents(value);
	});
</script>

<div>
	<textarea class="form-control text-wrap" {id}>{value} </textarea>
</div>
