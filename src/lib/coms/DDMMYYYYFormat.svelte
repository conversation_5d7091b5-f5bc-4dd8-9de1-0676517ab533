<script lang="ts">
	import { DDMMYYYY_Format } from '$lib/helper';
	import { locale } from '$lib/translations/locales.svelte';

	interface Props {
		date: string | null | undefined;
		style?: 'date' | 'time' | 'datetime';
	}

	let { date, style = 'datetime' }: Props = $props();
</script>

{#if style === 'date'}
	{DDMMYYYY_Format(date, 'date')}
{:else if style === 'time'}
	{DDMMYYYY_Format(date, 'time').replace('am', locale.T('am')).replace('pm', locale.T('pm'))}
{:else if style === 'datetime'}
	{DDMMYYYY_Format(date, 'datetime').replace('am', locale.T('am')).replace('pm', locale.T('pm'))}
{/if}
