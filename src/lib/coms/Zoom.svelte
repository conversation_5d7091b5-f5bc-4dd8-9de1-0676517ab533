<script lang="ts">
	let {
		zoom = $bindable(100),
		class: className = 'btn btn-light active'
	}: { zoom: number; class?: string } = $props();
	function plus() {
		zoom += 10;
	}
	function minus() {
		zoom -= 10;
	}
</script>

<button onclick={plus} aria-label="plus" type="button" class={className}
	><i class="fa-solid fa-magnifying-glass-plus"></i></button
>
<span class={className.replace('btn-', 'btn-outline-')}>{zoom}%</span>
<button onclick={minus} aria-label="plus" type="button" class={className}
	><i class="fa-solid fa-magnifying-glass-minus"></i></button
>
