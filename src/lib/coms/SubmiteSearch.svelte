<script lang="ts">
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	import { navigating, page } from '$app/state';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { EventHandler } from 'svelte/elements';
	interface Props {
		items: { name: any; id: any; price: any }[];
		height?: string;
		placeholder?: string;
		q_name?: string;
		action: string;
		children?: import('svelte').Snippet;
	}

	let { items, height = '300', placeholder = '', q_name = '', children, action }: Props = $props();
	let q = $state('');
	let timeout: number | NodeJS.Timeout | null = $state(null);
	const handleQ: EventHandler<Event, HTMLInputElement> = ({ currentTarget }) => {
		clearTimeout(timeout!);
		timeout = setTimeout(() => {
			if (q_name) {
				const newUrl = new URL(page.url);
				newUrl?.searchParams?.set(q_name, currentTarget?.value);
				goto(newUrl, { keepFocus: true, noScroll: true });
			} else {
				q = currentTarget.value;
			}
		}, 400);
	};
</script>

<!-- svelte-ignore a11y_click_events_have_key_events -->
<!-- svelte-ignore a11y_no_static_element_interactions -->
<div
	onclick={(e) => {
		e.stopPropagation();
	}}
	class="dropdown form-control m-0 p-0 shadow-none border-0"
>
	<input
		oninput={handleQ}
		{placeholder}
		name="q"
		autocomplete="off"
		data-bs-toggle="dropdown"
		id="dropdown"
		bind:value={q}
		class="form-control"
		type="text"
		data-bs-auto-close="outsite"
	/>
	<ul style="width: 100%;" class="dropdown-menu">
		<div style=" max-height: {height.concat('px')}; overflow-y: auto;">
			{#if ['form', 'goto', 'leave', 'popstate'].includes(navigating?.type ?? '')}
				<button type="button" class="dropdown-item">
					<i class="fa-solid fa-spinner fa-spin"></i>
					{locale.T('loading_')}
				</button>
			{:else}
				{#each items as item (item.id)}
					<!-- svelte-ignore a11y_autofocus -->
					<form
						autofocus
						method="post"
						use:enhance={() => {
							store.globalLoading = true;
							return async ({ update }) => {
								q = '';
								await update();
								store.globalLoading = false;
								document.getElementById('dropdown')?.focus();
							};
						}}
						{action}
					>
						<li>
							<input type="hidden" value={item.id ?? ''} name="product_id" />
							<input type="hidden" value={item.price ?? ''} name="price" />
							{@render children?.()}
							<button id="submit" type="submit" class="dropdown-item">{item.name ?? ''}</button>
						</li>
					</form>
				{:else}
					<button type="button" class="dropdown-item">
						<i class="fa-solid fa-magnifying-glass"></i>
						{locale.T('none_data')}
					</button>
				{/each}
			{/if}
		</div>
	</ul>
</div>
