<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';

	interface Props {
		time: string | null | undefined;
	}

	let { time }: Props = $props();
	function convertTo12HourFormat({ time }: Props) {
		if (!time) return '';
		const [hour, minute] = time.split(':');
		const ampm = +hour >= 12 ? locale.T('pm') : locale.T('am');
		const adjustedHour = +hour % 12 || 12;
		return `${adjustedHour}:${minute} ${ampm}`;
	}
</script>

{convertTo12HourFormat({ time })}
