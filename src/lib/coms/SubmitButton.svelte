<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	interface Props {
		sm?: boolean;
		icon_only?: boolean;
		text_only?: boolean;
		loading?: boolean;
		name?: string;
		style?: string;
		formaction?: string;
		form?: string;
	}

	let {
		sm = false,
		icon_only = false,
		text_only = false,
		loading = false,
		name = '',
		style = '',
		form = '',
		formaction = ''
	}: Props = $props();
</script>

<button
	form={form ? form : undefined}
	formaction={formaction ? formaction : undefined}
	disabled={loading}
	class:btn-sm={sm === true}
	type="submit"
	class={`btn btn-primary d-print-none ${style}`}
>
	{#if icon_only}
		{#if loading}
			<i class="fa-solid fa-spinner fa-spin"></i>
		{/if}
		{#if !loading}
			<i class="fa-regular fa-floppy-disk"></i>
		{/if}
	{/if}
	{#if !icon_only}
		{#if text_only}
			{name ? name : locale.T('save')}
		{/if}
		{#if !text_only}
			{#if loading}
				<i class="fa-solid fa-spinner fa-spin"></i>
				{name ? name : locale.T('save')}
			{/if}
			{#if !loading}
				<i class="fa-regular fa-floppy-disk"></i> {name ? name : locale.T('save')}
			{/if}
		{/if}
	{/if}
</button>
