<script lang="ts">
	import { type TCurrency } from '$lib/type';
	interface Props {
		amount: number | undefined | null;
		symbol?: string | undefined;
		get_currency?: TCurrency;
		class?: string;
	}
	let {
		amount,
		symbol,
		get_currency,
		class: className = 'btn btn-sm btn-warning py-0 '
	}: Props = $props();

	let conerting: string = $state('');
	let total = $derived(
		(Number(amount) * Number(get_currency?.exchang_rate)) / Number(get_currency?.currency_rate) || 0
	);

	$effect(() => {
		if (get_currency?.id) {
			conerting = new Intl.NumberFormat('en-US', { style: 'decimal' })
				.format(+total.toFixed(2))
				.concat(' ' + get_currency?.exchang_to || '');
		} else {
			conerting = new Intl.NumberFormat('en-US', { style: 'decimal' })
				.format(amount || 0)
				.concat(' ' + symbol || '');
		}
	});
</script>

{#if conerting}
	<span class={className}>
		{conerting}
	</span>
{/if}
