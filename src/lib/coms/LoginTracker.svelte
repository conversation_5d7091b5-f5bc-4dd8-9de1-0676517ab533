<script lang="ts">
	import { enhance } from '$app/forms';
	interface Props {
		action?: string;
		version?: string;
		username?: string;
		ip_address?: string;
		mac_address?: string;
	}

	let {
		action = '',
		version = '',
		username = '',
		ip_address = '',
		mac_address = ''
	}: Props = $props();
</script>

<form use:enhance {action} method="post">
	<input type="hidden" name="version" value={version} />
	<input type="hidden" name="username" value={username} />
	<input type="hidden" name="ip_address" value={ip_address} />
	<input type="hidden" name="mac_address" value={mac_address} />
</form>
