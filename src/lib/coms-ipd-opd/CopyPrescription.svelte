<script lang="ts">
	import { store } from '$lib/store/store.svelte';
	import type { TPrescription } from '$lib/type/index';
	import { locale } from '$lib/translations/locales.svelte';
	function copy() {
		if (store.copyPrescription?.length) store.copyPrescription = [];
		if (!store.copyPrescription?.length) store.copyPrescription = data;
	}
	interface Props {
		data: TPrescription[];
		class?: string;
	}

	let { data, class: className = 'btn btn-warning btn-sm' }: Props = $props();
</script>

{#if data.length}
	<button
		class:text-primary={store.copyPrescription.length === data.length}
		onclick={copy}
		type="button"
		class={className}
	>
		{#if store.copyPrescription.length === data.length}
			{locale.T('copied')}
			<i class="fa-solid fa-check"></i>
		{:else}
			{locale.T('copy')}
			<i class="fa-regular fa-copy"></i>
		{/if}
	</button>
{/if}
