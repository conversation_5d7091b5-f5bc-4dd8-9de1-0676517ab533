<script lang="ts">
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from '../../routes/(dash)/visit/$types';
	type Data = Pick<PageServerData, 'get_progress_notes' | 'get_wards'>;

	interface Props {
		data: Data;
		bed_id: number | null;
		department_id: number | null;
	}

	let { data, bed_id = $bindable(0), department_id = $bindable(null) }: Props = $props();
	let { get_wards, get_progress_notes } = $derived(data);
	$effect(() => {
		for (const e of get_wards) {
			if (e.room.some((e) => e.department_id !== department_id)) {
				bed_id = null;
			}
		}
	});
</script>

<!-- add bed -->
<div class="modal fade" id="add_bed_ipd">
	<div class="modal-dialog modal-dialog-scrollabl modal-xl">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('department')}-{locale.T('room')}-{locale.T('bed')}</h4>
				<button
					id="close_add_bed_ipd"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>

			<div class="modal-body">
				<br />
				{#each get_wards as item (item.id)}
					{@const rooms = item.room.filter((e) => e.department_id === department_id)}
					{#if rooms.length}
						<div class="card border-success border-3 mb-5 position-relative">
							<span
								class="position-absolute top-0 start-50 translate-middle btn btn-success btn-lg"
							>
								{item.ward}
							</span>
							<div class="card-body">
								{#each rooms as iitem (iitem.id)}
									{@const beds = iitem.bed}
									<div class="card border-primary border-3 mt-5 px-3 py-1 position-relative">
										<span class="position-absolute top-0 start-50 translate-middle btn btn-primary">
											{item.ward}-{iitem.room}
										</span>

										<div class="row pt-4">
											{#each beds as bed (bed.id)}
												{@const find_progress_note = get_progress_notes.find(
													(e) => e.activeBed.find((e) => e.datetime_out === null)?.bed_id === bed.id
												)}
												<div class="card bg-light col-6">
													<div class="row">
														<div
															class:text-danger={find_progress_note}
															class="col-md-3 text-primary"
														>
															<i class="fa-solid fa-bed fa-6x"></i>
														</div>
														<div class="col-md-9">
															<div class="card-body">
																<button
																	type="button"
																	onclick={() => {
																		if (bed_id === bed.id) {
																			bed_id = 0;
																		} else {
																			bed_id = 0;
																			bed_id = bed.id;
																		}
																		document.getElementById('close_add_bed_ipd')?.click();
																	}}
																	class:btn-danger={find_progress_note || bed.id === bed_id}
																	disabled={find_progress_note || bed.id === bed_id ? true : false}
																	class=" btn btn-primary m-0 btn-sm"
																>
																	{bed.bed}
																</button>

																<table>
																	<thead>
																		{#if find_progress_note}
																			<tr>
																				<td>{locale.T('name_khmer')}</td>
																				<td> : </td>
																				<td>
																					<p class="card-text">
																						{find_progress_note?.patient.name_khmer ?? ''}
																					</p>
																				</td>
																			</tr>
																			<tr>
																				<td>{locale.T('name_latin')}</td>
																				<td> : </td>
																				<td>
																					<p class="card-text">
																						{find_progress_note?.patient.name_latin ?? ''}
																					</p>
																				</td>
																			</tr>
																			<tr>
																				<td>{locale.T('etiology')}</td>
																				<td> : </td>
																				<td>
																					<p class="card-text">
																						{find_progress_note?.etiology ?? ''}
																					</p>
																				</td>
																			</tr>
																			<tr>
																				<td>{locale.T('date')}</td>
																				<td> : </td>
																				<td>
																					<p class="card-text">
																						<DDMMYYYYFormat
																							date={find_progress_note?.date_checkup}
																						/>
																					</p>
																				</td>
																			</tr>
																		{:else}
																			<tr>
																				<td>{locale.T('name_khmer')}</td>
																				<td> : </td>
																				<td> </td>
																			</tr>
																			<tr>
																				<td>{locale.T('name_latin')}</td>
																				<td> : </td>
																				<td> </td>
																			</tr>
																			<tr>
																				<td>{locale.T('etiology')}</td>
																				<td> : </td>
																				<td> </td>
																			</tr>
																			<tr>
																				<td>{locale.T('date')}</td>
																				<td> : </td>
																				<td> </td>
																			</tr>
																		{/if}
																	</thead>
																</table>
															</div>
														</div>
													</div>
												</div>
											{/each}
										</div>
									</div>
								{/each}
							</div>
						</div>
					{/if}
				{/each}
			</div>
		</div>
	</div>
</div>
