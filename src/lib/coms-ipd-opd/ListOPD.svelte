<script lang="ts">
	import { enhance } from '$app/forms';
	import ActiveVisit from '$lib/coms-ipd-opd/ActiveVisit.svelte';
	import ConfirmModal from '$lib/coms-form/ConfirmModal.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from '../../routes/(dash)/patient/opd/$types';
	import GenderAge from '$lib/coms/GenderAge.svelte';
	import type { SubmitFunction } from '@sveltejs/kit';
	import Name from '$lib/coms/Name.svelte';
	type Data = Pick<PageServerData, 'get_visits' | 'get_departments' | 'get_staffs'>;
	interface Props {
		data: Data;
		n: number;
	}
	let { data, n }: Props = $props();
	let { get_visits, get_departments, get_staffs } = $derived(data);
	let editEtiology = $state(false);
	let editDepartment = $state(false);
	let editDoctor = $state(false);
	let visit_id = $state(0);
	let billing_id = $state(0);
	const onSubmit: SubmitFunction = () => {
		store.globalLoading = true;
		return async ({ update }) => {
			await update({ reset: false });
			store.globalLoading = false;
			editDepartment = false;
			editEtiology = false;
			editDoctor = false;
		};
	};
	let total_male = $derived(
		get_visits.filter((item) => item.patient.gender.toLowerCase() === 'male').length
	);
</script>

<DeleteModal action="/patient/opd?/delete_visit" id={visit_id} />
<ConfirmModal action="/patient/opd?/process_billing" id={billing_id} />

<table class="table table-hover text-nowrap table-valign-middle table-bordered table-light mb-0">
	<thead class="sticky-top bg-light table-active">
		<tr class="text-center">
			<th style="width: 3%;">{locale.T('n')}</th>
			<th style="width: 7%;">{locale.T('date')}</th>
			<th style="width: 5%;">{locale.T('id')}</th>
			<th style="width: 10%;">{locale.T('patient_name')}</th>
			<th style="width: 15%;">{locale.T('symptoms')}</th>
			<th style="width: 15%;">{locale.T('department')}</th>
			<th style="width: 10%;">{locale.T('doctor')}</th>
			<th colspan="2" style="width: 5%;">{locale.T('payment')}</th>
			<th style="width: 5%;">{locale.T('status')}</th>
			<th style="width: 7%;">{locale.T('transfer_to_ipd')}</th>
			<th style="width: 3%;"></th>
		</tr>
	</thead>
	<tbody>
		{#each get_visits as item, index (item.id)}
			<tr class="text-center">
				<td class="text-left">{index + n}</td>
				<td>
					<DDMMYYYYFormat style="date" date={item.date_checkup} />
					<br />
					<DDMMYYYYFormat style="time" date={item.date_checkup} />
				</td>

				<td class="text-center">VS{item.id ?? ''} <br /> PT{item.patient_id ?? ''}</td>
				<td>
					{#if item.transfer}
						<button class="btn btn-link opacity-75">
							{item.patient?.name_khmer}<br />
							{item.patient?.name_latin}
						</button>
					{:else if item.status === 'LOADING'}
						<ActiveVisit
							action="/patient/opd?/active_visit"
							visit_id={item.id}
							class="btn btn-link text-danger"
						>
							{item.patient?.name_khmer}<br />
							{item.patient?.name_latin}

							<GenderAge dob={item.patient.dob} date={new Date()} gender={item.patient.gender} />
						</ActiveVisit>
					{:else}
						<a href="/opd/{item.id}/subjective">
							{item.patient?.name_khmer} <br />
							{item.patient?.name_latin}
							<GenderAge dob={item.patient.dob} date={new Date()} gender={item.patient.gender} />
						</a>
					{/if}
				</td>
				<td>
					{#if editEtiology && item.id === visit_id}
						<form
							onfocusout={() => (editEtiology = false)}
							data-sveltekit-keepfocus
							use:enhance={onSubmit}
							method="post"
							action="/patient/opd?/update_etiology"
							onchange={(e) => {
								e.currentTarget.requestSubmit();
								editEtiology = false;
							}}
						>
							<input
								name="etiology"
								class="bg-primary-subtle form-control text-center"
								value={item.etiology}
								type="text"
							/>
							<input type="hidden" name="id" value={item.id} />
						</form>
					{:else}
						<button
							disabled={item.transfer}
							class="btn btn-link text-break text-decoration-none text-dark"
							onclick={() => {
								editEtiology = true;
								editDepartment = false;
								editDoctor = false;
								visit_id = item.id;
							}}
						>
							{item.etiology ?? ''}
						</button>
					{/if}
				</td>
				<td>
					{#if editDepartment && visit_id === item.id}
						<form
							onfocusout={() => (editDepartment = false)}
							data-sveltekit-keepfocus
							use:enhance={() => {
								store.globalLoading = true;
								return async ({ update }) => {
									await update({ reset: false });
									editDepartment = false;
									editEtiology = false;
									editDoctor = false;
									store.globalLoading = false;
								};
							}}
							method="post"
							action="/patient/opd?/update_department"
							onchange={(e) => {
								e.currentTarget.requestSubmit();
								editDepartment = false;
							}}
						>
							<select
								class="form-control text-center bg-primary-subtle"
								name="department_id"
								id="department_id"
							>
								{#each get_departments as iitem}
									<option selected={item.department_id === iitem.id} value={iitem.id}
										>{iitem?.products ?? ''}</option
									>
								{/each}
							</select>
							<input type="hidden" name="id" value={item.id} />
						</form>
					{:else}
						<button
							disabled={item.transfer}
							class="btn btn-link text-decoration-none text-dark"
							onclick={(e) => {
								if (e.target) editDepartment = true;
								editEtiology = false;
								editDoctor = false;
								visit_id = item.id;
							}}>{item.department?.products ?? ''}</button
						>
					{/if}
				</td>
				<td>
					{#if editDoctor && visit_id === item.id}
						<form
							onfocusout={() => (editDoctor = false)}
							data-sveltekit-keepfocus
							use:enhance={() => {
								store.globalLoading = true;
								return async ({ update }) => {
									await update({ reset: false });
									editDepartment = false;
									editEtiology = false;
									editDoctor = false;
									store.globalLoading = false;
								};
							}}
							method="post"
							action="/patient/opd?/update_staff"
							onchange={(e) => {
								e.currentTarget.requestSubmit();
								editDepartment = false;
								editDoctor = false;
								editEtiology = false;
							}}
						>
							<select
								class="form-control text-center bg-primary-subtle"
								name="staff_id"
								id="staff_id"
							>
								{#each get_staffs as iitem}
									<option selected={item.staff_id === iitem.id} value={iitem.id}
										>{iitem.name_latin}</option
									>
								{/each}
							</select>
							<input type="hidden" name="id" value={item.id} />
						</form>
					{:else}
						<button
							disabled={item.transfer}
							class="btn btn-link text-decoration-none text-dark"
							onclick={() => {
								editDepartment = false;
								editEtiology = false;
								editDoctor = true;
								visit_id = item.id;
							}}
						>
							<Name both title={item.staff?.title} name={item.staff} />
						</button>
					{/if}
				</td>
				<td>
					<a
						href="/patient/payment-service?id={item.billing?.paymentService?.id}&billing_id={item
							.billing?.id}">{item.billing?.serviceType?.by || locale.T('none_data')}</a
					>
				</td>
				<td>
					{#if item.billing?.status === 'checking'}
						<button disabled={item.transfer} type="button" class="btn btn-warning btn-sm"
							><i class="fa-regular fa-circle-xmark"></i> {locale.T('not_yet_paid')}</button
						>
					{:else if item.billing?.status === 'paying'}
						<button type="button" class="btn btn-success btn-sm"
							><i class="fa-solid fa-spinner fa-spin"></i>
							{locale.T('send_to_payment')}</button
						>
					{:else}
						<button type="button" class="btn btn-primary btn-sm"
							><i class="fa-regular fa-circle-check"></i> {locale.T('already_paid')}</button
						>
					{/if}
				</td>
				<td>
					{#if item.status === 'LOADING'}
						<button class="btn btn-warning btn-sm"
							><i class="fa-solid fa-arrow-down-1-9"></i> {locale.T('loading')}</button
						>
					{/if}
					{#if item.status === 'CHECKING'}
						<button class="btn btn-success btn-sm"
							><i class="fa-solid fa-spinner fa-spin"></i>
							{locale.T('checking')}</button
						>
					{/if}
					{#if item.status === 'DONE'}
						<button class="btn btn-primary btn-sm"
							><i class="fa-regular fa-circle-check"></i> {locale.T('done')}</button
						>
					{/if}
				</td>
				<td>
					<div>
						{#if item.transfer}
							<button type="button" class="btn btn-danger btn-sm"
								>{locale.T('admitted_to_ipd')}</button
							>
						{:else}
							<a
								href="/visit?visit_type=ipd&patient_id={item.patient_id}&visit_id={item.id}"
								class="btn btn-light btn-sm">{locale.T('transfer_to_ipd')}</a
							>
						{/if}
					</div>
				</td>
				<td>
					<div>
						<button
							aria-label="deletemodal"
							disabled={item.transfer}
							onclick={() => {
								visit_id = item.id;
							}}
							type="button"
							class="btn btn-danger btn-sm"
							data-bs-toggle="modal"
							data-bs-target="#delete_modal"
							><i class="fa-solid fa-trash-can"></i>
						</button>
					</div>
				</td>
			</tr>
		{/each}
		<tr class="text-center table-success">
			<td colspan="12">
				{locale.T('total')}: {get_visits.length}
				{locale.T('people')},
				{locale.T('male')}: {total_male}
				{locale.T('female')}: {get_visits.length - total_male}
			</td>
		</tr>
	</tbody>
</table>
