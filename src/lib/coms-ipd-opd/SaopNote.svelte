<script lang="ts">
	import type { PageServerData } from '../../routes/(dash)/ipd/[progress_note_id]/progress-note/$types';
	import Athtml from '$lib/coms/Athtml.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import TimeFormat from '$lib/coms/TimeFormat.svelte';
	type V = PageServerData['get_progress_note']['visit'];
	type VV = NonNullable<V>[number];
	type E = PageServerData['get_exams'];
	interface Props {
		find_old_visit: VV;
		get_exams: E;
	}

	let { find_old_visit, get_exams }: Props = $props();
	let mean_arterial_pressure = $derived(
		(1 / 3) * Number(find_old_visit?.vitalSign?.sbp) +
			(2 / 3) * Number(find_old_visit?.vitalSign?.dbp)
	);
</script>

<div>
	{#if find_old_visit?.vitalSign}
		<span class="btn btn-success btn-sm mb-2 py-0">{locale.T('vital_sign')}</span>
		<div class="border rounded border-1 p-2 mb-2">
			<table class="table table-sm table-light">
				<thead>
					{#if find_old_visit?.vitalSign?.sbp}
						<tr>
							<td style="width: 40%;">BP(mmHg)</td>
							<td style="width: 5%;">:</td>
							<td style="width: 55%;">
								{find_old_visit?.vitalSign?.sbp?.toFixed(0).concat(' /') ?? ''}
								{find_old_visit?.vitalSign?.dbp?.toFixed(0).concat(' mmHg') ?? ''}
							</td>
						</tr>
					{/if}
					{#if mean_arterial_pressure}
						<tr>
							<td style="width: 40%;">MAP</td>
							<td style="width: 5%;">:</td>
							<td style="width: 55%;">
								{#if mean_arterial_pressure}
									{mean_arterial_pressure ? mean_arterial_pressure?.toFixed(0).concat(' mmHg') : ''}
								{/if}
							</td>
						</tr>
					{/if}
					{#if find_old_visit?.vitalSign?.pulse}
						<tr>
							<td style="width: 40%;">Pulse (min)</td>
							<td style="width: 5%;">:</td>
							<td style="width: 20%;"
								>{find_old_visit?.vitalSign?.pulse?.toFixed(0).concat(' /min') ?? ''}</td
							>
						</tr>
					{/if}
					{#if find_old_visit?.vitalSign?.t}
						<tr>
							<td style="width: 40%;">Temperature °C</td>
							<td style="width: 5%;">:</td>
							<td style="width: 55%;"
								><Athtml
									html={find_old_visit?.vitalSign?.t?.toFixed(1).concat(' &deg;C') ?? ''}
								/></td
							>
						</tr>
					{/if}
					{#if find_old_visit?.vitalSign?.rr}
						<tr>
							<td style="width: 40%;">RR (min)</td>
							<td style="width: 5%;">:</td>
							<td style="width: 55%;"
								>{find_old_visit?.vitalSign?.rr?.toFixed(0).concat(' /min') ?? ''}</td
							>
						</tr>
					{/if}
					{#if find_old_visit?.vitalSign?.sp02}
						<tr>
							<td style="width: 40%;">SpO2 (%)</td>
							<td style="width: 5%;">:</td>
							<td style="width: 55%;"
								>{find_old_visit?.vitalSign?.sp02?.toFixed(0).concat(' %') ?? ''}</td
							>
						</tr>
					{/if}
					{#if find_old_visit?.vitalSign?.height}
						<tr>
							<td style="width: 40%;">Height (cm)</td>
							<td style="width: 5%;">:</td>
							<td style="width: 55%;"
								>{find_old_visit?.vitalSign?.height?.toFixed(0).concat(' cm') ?? ''}</td
							>
						</tr>
					{/if}
					{#if find_old_visit?.vitalSign?.weight}
						<tr>
							<td style="width: 40%;">Weight (kg)</td>
							<td style="width: 5%;">:</td>
							<td style="width: 55%;"
								>{find_old_visit?.vitalSign?.weight?.toFixed(0).concat(' kg') ?? ''}</td
							>
						</tr>
					{/if}
					{#if find_old_visit?.vitalSign?.bmi}
						<tr>
							<td style="width: 40%;">BMI</td>
							<td style="width: 5%;">:</td>
							<td style="width: 55%;"
								>{find_old_visit?.vitalSign?.bmi?.toFixed(1).concat(' kg/m2') ?? ''}</td
							>
						</tr>
					{/if}
				</thead>
			</table>
		</div>
	{/if}
	{#if find_old_visit?.subjective?.cheif_complaint}
		<div class="border rounded border-1 p-2 mb-2">
			<span class="btn btn-success btn-sm mb-2 py-0">Cheif complaint</span>
			<div class="text-break">
				{find_old_visit?.subjective?.cheif_complaint ?? ''}
			</div>
		</div>
	{/if}
	{#if find_old_visit?.subjective?.history_of_present_illness}
		<div class="border rounded border-1 p-2 mb-2">
			<span class="btn btn-success btn-sm mb-2 py-0">History of Present illness</span>
			<div class="text-break">
				{find_old_visit?.subjective?.history_of_present_illness ?? ''}
			</div>
		</div>
	{/if}
	{#if find_old_visit?.subjective}
		<div class="border rounded border-1 p-2 mb-2">
			<span class="btn btn-success btn-sm mb-2 py-0">Past medicine history</span>
			<table class="table-sm table table-light">
				<thead>
					{#if find_old_visit?.subjective?.current_medication}
						<tr>
							<td style="width: 40%;"> Current Medication</td>
							<td style="width: 5%;">:</td>
							<td style="width: 55%;">
								{find_old_visit?.subjective?.current_medication ?? ''}
							</td>
						</tr>
					{/if}
					{#if find_old_visit?.subjective?.past_medical_history}
						<tr>
							<td style="width: 40%;">Past medical history</td>
							<td style="width: 5%;">:</td>
							<td style="width: 55%;">
								{find_old_visit?.subjective?.past_medical_history ?? ''}
							</td>
						</tr>
					{/if}
					{#if find_old_visit?.subjective?.allesgy_medicine}
						<tr>
							<td style="width: 40%;">Allergy medicine</td>
							<td style="width: 5%;">:</td>
							<td style="width: 55%;">
								{find_old_visit?.subjective?.allesgy_medicine ?? ''}
							</td>
						</tr>
					{/if}
					{#if find_old_visit?.subjective?.surgical_history}
						<tr>
							<td style="width: 40%;">Surgical history</td>
							<td style="width: 5%;">:</td>
							<td style="width: 55%;">
								{find_old_visit?.subjective?.surgical_history ?? ''}
							</td>
						</tr>
					{/if}
					{#if find_old_visit?.subjective?.family_and_social_history}
						<tr>
							<td style="width: 40%;">Family and social history</td>
							<td style="width: 5%;">:</td>
							<td style="width: 55%;">
								{find_old_visit?.subjective?.family_and_social_history ?? ''}
							</td>
						</tr>
					{/if}
				</thead>
			</table>
		</div>
	{/if}
	{#if find_old_visit?.physicalExam.length}
		<button class="btn btn-success btn-sm mb-2 py-0">Physical Exam</button>
	{/if}
	{#each get_exams || [] as exam (exam.id)}
		{@const physicals = exam.physical}
		{#if find_old_visit?.physicalExam.some((e) => e.physical?.exam_id === exam.id)}
			<div class="border rounded border-1 p-2 mb-2">
				<span class="fs-6 text-decoration-underline text-primary">{exam.examination ?? ''}</span>
				<table class="table table-sm table-light">
					<thead>
						{#each physicals as physical (physical.id)}
							{#each find_old_visit?.physicalExam || [] as physical_exam (physical_exam.id)}
								{#if physical_exam.physical_id === physical.id}
									{#if physical_exam.result}
										<tr>
											<td style="width: 40%;"> {physical.physical}</td>
											<td style="width: 5%;">:</td>
											<td style="width: 55%;">
												{physical_exam.result ?? ''}
											</td>
										</tr>
									{/if}
								{/if}
							{/each}
						{/each}
					</thead>
				</table>
			</div>
		{/if}
	{/each}
	{#if find_old_visit?.subjective?.pre_diagnosis}
		<div class="border rounded border-1 p-2 mb-2">
			<span class="btn btn-success btn-sm mb-2 py-0">Pre-Diagnosis</span>
			<p>
				{find_old_visit?.subjective?.pre_diagnosis ?? ''}
			</p>
		</div>
	{/if}
	{#if find_old_visit?.accessment}
		<div class="border rounded border-1 p-2 mb-2">
			<span class="btn btn-success btn-sm mb-2 py-0">Diagnosis</span>
			<p>
				{find_old_visit?.accessment.diagnosis_or_problem ?? ''}
			</p>
		</div>
		<div class="border rounded border-1 p-2 mb-2">
			<span class="btn btn-success btn-sm mb-2 py-0">Differential Diagnosis</span>
			<p>
				{find_old_visit?.accessment.differential_diagnosis ?? ''}
			</p>
		</div>
	{/if}
	{#if find_old_visit?.accessment?.assessment_process}
		<div class="border rounded border-1 p-2 mb-2">
			<span class="btn btn-success btn-sm mb-2 py-0">Assessment Process</span>
			<div class="text-break">
				{find_old_visit?.accessment?.assessment_process ?? ''}
			</div>
		</div>
	{/if}
	{#if find_old_visit?.service.length}
		<button class="btn btn-success btn-sm mb-2 py-0">Service</button>
	{/if}
	{#if find_old_visit?.service.length}
		{#each find_old_visit.service || [] as item (item.id)}
			<div class="border rounded border-1 p-2 mb-2">
				<span class="fs-6 text-decoration-underline text-primary"
					>{item.product?.products ?? ''}</span
				>
				<table class="table table-light">
					<thead>
						<tr>
							<td style="width: 40%;">
								<span>Surgeon</span>
							</td>
							<td style="width: 5%;">:</td>
							<td style="width:50%;">
								<span>{item?.operationProtocol?.surgeon ?? ''}</span>
							</td>
						</tr>
						<tr>
							<td style="width: 40%;">
								<span>Assistant Surgeon</span>
							</td>
							<td style="width: 5%;">:</td>
							<td style="width:50%;">
								<span>{item?.operationProtocol?.assistant_surgeon ?? ''}</span>
							</td>
						</tr>
						<tr>
							<td style="width: 40%;">
								<span>Anesthetist</span>
							</td>
							<td style="width: 5%;">:</td>
							<td style="width:50%;">
								<span>{item?.operationProtocol?.anesthetist ?? ''}</span>
							</td>
						</tr>
						<tr>
							<td style="width: 40%;">
								<span>Assistant Anesthetist</span>
							</td>
							<td style="width: 5%;">:</td>
							<td style="width:50%;">
								<span>{item?.operationProtocol?.assistant_anesthetist ?? ''}</span>
							</td>
						</tr>
						<tr>
							<td style="width: 40%;">
								<span>Scrub Nurse</span>
							</td>
							<td style="width: 5%;">:</td>
							<td style="width:50%;">
								<span>{item?.operationProtocol?.scrub_nurse ?? ''}</span>
							</td>
						</tr>
						<tr>
							<td style="width: 40%;">
								<span>Circulation / Nurse block</span>
							</td>
							<td style="width: 5%;">:</td>
							<td style="width:50%;">
								<span>{item?.operationProtocol?.cirulating_nurse_block ?? ''}</span>
							</td>
						</tr>
						<tr>
							<td style="width: 40%;">
								<span>Midwife</span>
							</td>
							<td style="width: 5%;">:</td>
							<td style="width:50%;">
								<span>{item?.operationProtocol?.midwife ?? ''}</span>
							</td>
						</tr>
						{#if item?.operationProtocol?.date}
							<tr>
								<td style="width: 40%;">
									<span>Dates</span>
								</td>
								<td style="width: 5%;">:</td>
								<td style="width:50%;">
									<span>
										<DDMMYYYYFormat style="date" date={item?.operationProtocol.date} />
									</span>
								</td>
							</tr>
						{/if}
						{#if item?.operationProtocol?.start_time}
							<tr>
								<td style="width: 40%;">
									<span>StartTime</span>
								</td>
								<td style="width: 5%;">:</td>
								<td style="width:50%;">
									<TimeFormat time={item?.operationProtocol?.start_time} />
								</td>
							</tr>
						{/if}
						{#if item?.operationProtocol?.finish_time}
							<tr>
								<td style="width: 40%;">
									<span>FinishTime</span>
								</td>
								<td style="width: 5%;">:</td>
								<td style="width:50%;">
									<TimeFormat time={item?.operationProtocol?.finish_time} />
								</td>
							</tr>
						{/if}
						<tr>
							<td style="width: 40%;">
								<span>Pre-Diagnosis</span>
							</td>
							<td style="width: 5%;">:</td>
							<td style="width:50%;">
								<span>{item?.operationProtocol?.pre_diagnosis ?? ''}</span>
							</td>
						</tr>
						<tr>
							<td style="width: 40%;">
								<span>Post Diagnosis</span>
							</td>
							<td style="width: 5%;">:</td>
							<td style="width:50%;">
								<span>{item?.operationProtocol?.post_diagnosis ?? ''}</span>
							</td>
						</tr>
						<tr>
							<td style="width: 40%;">
								<span>Type Anesthesia</span>
							</td>
							<td style="width: 5%;">:</td>
							<td style="width:50%;">
								<span>{item?.operationProtocol?.type_anesthesia ?? ''}</span>
							</td>
						</tr>
						<tr>
							<td colspan="3" class="text-wrap" style="width: 100%;">
								<div>
									<span>Opertive Technique</span>
									<div class="text-break">
										{item?.operationProtocol?.opertive_technique ?? ''}
									</div>
								</div>
							</td>
						</tr>
						<tr>
							<td style="width: 40%;">
								<span>Blood Less </span>
							</td>
							<td style="width: 5%;">:</td>
							<td style="width:50%;">
								<span>{item?.operationProtocol?.blood_less ?? ''}</span>
							</td>
						</tr>
						<tr>
							<td colspan="3" class="text-wrap" style="min-width: 100%;">
								<div>
									<span>Notes</span>
									<div class="text-break">
										{item?.operationProtocol?.notes ?? ''}
									</div>
								</div>
							</td>
						</tr>
					</thead>
				</table>
			</div>
		{/each}
	{/if}
</div>
