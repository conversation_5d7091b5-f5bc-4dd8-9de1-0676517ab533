<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	import { store } from '$lib/store/store.svelte';
	interface Props {
		action: string;
	}
	let { action }: Props = $props();
</script>

{#if store.copyPrescription?.length}
	<Form {action} showToast={false} fnSuccess={() => (store.copyPrescription = [])} method="post">
		<input value="paste_prescription" type="hidden" name="paste_prescription" />
		{#each store.copyPrescription || [] as item}
			<input value={item?.product_id ?? ''} type="hidden" name="product_id" />
			<input value={item?.unit_id ?? ''} type="hidden" name="unit_id" />
			<input value={item?.use ?? ''} type="hidden" name="use" />
			<input value={item?.amount ?? ''} type="hidden" name="amount" />
			<input value={item?.duration ?? ''} type="hidden" name="duration" />
			<input value={item?.morning ?? ''} type="hidden" name="morning" />
			<input value={item?.noon ?? ''} type="hidden" name="noon" />
			<input value={item?.afternoon ?? ''} type="hidden" name="afternoon" />
			<input value={item?.evening ?? ''} type="hidden" name="evening" />
			<input value={item?.night ?? ''} type="hidden" name="night" />
		{/each}
		<button aria-label="submit" class="btn btn-sm btn-primary" type="submit"
			><i class="fa-solid fa-paste"></i></button
		>
	</Form>
{/if}
