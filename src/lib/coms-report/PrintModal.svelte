<script lang="ts">
	interface Props {
		class?: string;
		id: string | number;
		children?: import('svelte').Snippet;
	}

	let { class: className = 'btn btn-primary btn-sm', id, children }: Props = $props();
	function print() {
		window.print();
		document.getElementById('close-modal')?.click();
	}
</script>

<!-- Button trigger modal -->
<button
	aria-label="printmodal"
	type="button"
	class={className}
	data-bs-toggle="modal"
	data-bs-target={`#id${id.toString()}`}
>
	<i class="fa-solid fa-print"></i>
</button>

<!-- Modal -->
<div
	class="modal"
	id={`id${id.toString()}`}
	data-bs-backdrop="static"
	data-bs-keyboard="false"
	tabindex="-1"
	aria-labelledby="staticBackdropLabel"
	aria-hidden="true"
>
	<div class="modal-dialog modal-fullscreen">
		<div class="modal-content">
			<div class="modal-body">
				{@render children?.()}
			</div>
			<div class="modal-footer d-print-none">
				<button id="close-modal" type="button" class="btn btn-secondary" data-bs-dismiss="modal"
					>Close</button
				>
				<button onclick={print} type="button" class="btn btn-primary">Print</button>
			</div>
		</div>
	</div>
</div>
