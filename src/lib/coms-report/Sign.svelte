<script lang="ts">
	import Athtml from '$lib/coms/Athtml.svelte';
	import GenQRcode from '$lib/coms/GenQRcode.svelte';
	import { khmerDate } from '$lib/helper';
	let {
		qr,
		left,
		right
	}: {
		qr?: string | null | undefined;
		left?: {
			date?: string | null | undefined;
			img?: string | null | undefined;
			name?: string | null | undefined;
			title?: string | null | undefined;
			role?: string | null | undefined;
		};
		right?: {
			date?: string | null | undefined;
			img?: string | null | undefined;
			name?: string | null | undefined;
			title?: string | null | undefined;
			role?: string | null | undefined;
		};
	} = $props();
</script>

<div class="en_font_times_new_roman row justify-content-between pt-3">
	{#if left}
		<div class={!qr && !right ? 'col-12 fs-5 text-center pb-0' : 'col-4 fs-5 text-center pb-0'}>
			<Athtml html={left.title} />
			<span style="font-size: 18px;" class="kh_font_battambang">
				{khmerDate(left?.date, 'date')} <br />
				{left.role}<br />
			</span>
			{#if left.img}
				<div style="min-height: 115px;">
					<img class="my-0 py-0" style="height: 115px;" src={left.img} alt="" />
				</div>
			{:else}
				<br />
				<br />
				<br />
				<br />
			{/if}
			<p style="font-size: 18px;margin-top: 10px;">{left?.name ?? ''}</p>
		</div>
	{/if}
	{#if qr}
		<div style="width: 110px;" class={left ? 'col-4 text-center ' : 'col-8 '}>
			<br />
			<br />
			<br />
			<br />
			<br />
			<GenQRcode data={{ text: qr }} />
		</div>
	{/if}
	{#if right}
		<div class="col-4 text-center fs-5 pb-0">
			<Athtml html={right.title} />
			<span style="font-size: 18px;" class="kh_font_battambang">
				{khmerDate(right?.date, 'date')} <br />
				{right.role} <br />
			</span>
			{#if right.img}
				<div style="min-height: 115px;">
					<img class="my-0 py-0" style="height: 115px;" src={right.img} alt="" />
				</div>
			{:else}
				<br />
				<br />
				<br />
				<br />
				<br />
			{/if}
			<p style="font-size: 18px;margin-top: 10px;">{right?.name ?? ''}</p>
		</div>
	{/if}
</div>
