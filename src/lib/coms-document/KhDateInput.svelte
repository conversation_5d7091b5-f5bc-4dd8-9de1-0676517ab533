<script lang="ts">
	import { khmerDate } from '$lib/helper';
	interface Prope {
		date: string;
		name: string;
		type?: 'date' | 'time' | 'datetime-local';
		class?: string;
		style?: string;
		width?: string;
	}
	let {
		date = $bindable(new Date().toISOString()),
		name,
		type = 'date',
		width,
		style,
		class: className
	}: Prope = $props();
	let showDateInput = $state(false);
	$effect(() => {
		if (date === '') {
			showDateInput = true;
		}
	});
	function handleDateChange(event: Event) {
		const input = event.target as HTMLInputElement;
		date = input.value;
		showDateInput = false;
	}
</script>

{#if !showDateInput}
	<button
		style={width ? `height: 38px;width: ${width};${style}` : style}
		class={width ? 'input_document'.concat(' ').concat(className ?? '') : 'border-0 bg-transparent'}
		type="button"
		onclick={() => (showDateInput = true)}
	>
		{#if type === 'datetime-local'}
			{khmerDate(date ? date : new Date().toISOString(), 'datetime')}
		{:else if type === 'time'}
			{khmerDate(date ? date : new Date().toISOString(), 'time')}
		{:else if type === 'date'}
			{khmerDate(date ? date : new Date().toISOString(), 'date')}
		{/if}
	</button>
{:else}
	<input
		style={width ? `height: 38px;width: ${width};${style}` : style}
		class={'input_document'.concat(' ').concat(className ?? '')}
		{type}
		onchange={handleDateChange}
	/>
{/if}
<input type="hidden" {name} value={date} />
