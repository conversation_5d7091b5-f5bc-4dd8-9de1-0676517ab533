<script lang="ts">
	import InputDocument from '$lib/coms-form/InputDocument.svelte';
	import type { TAddress, TDocumentSetting, TFields } from '$lib/type';
	import KhDateTime from '$lib/coms-document/KhDateInput.svelte';
	import { khmerDate } from '$lib/helper';
	import Header from '$lib/coms-document/Header.svelte';
	interface Prop {
		p_name_khmer: string;
		p_name_latin: string;
		p_nation: string;
		p_gender: string;
		p_dob: string;
		p_occupation: string;
		p_phone: string;
		p_id_card_passport: string;
		p_address?: TAddress;
		fields: TFields[];
		title_khm: string;
		title_eng: string;
		logo: string;
		get_document_setting?: TDocumentSetting;
	}
	let {
		p_name_khmer,
		get_document_setting,
		p_name_latin,
		p_occupation,
		p_id_card_passport,
		p_dob,
		p_phone,
		p_nation,
		p_gender,
		p_address: address,
		fields,
		logo,
		title_eng,
		title_khm
	}: Prop = $props();
	let p_address = $derived(
		`${address?.village?.type ?? ''} ${address?.village?.name_khmer ?? ''} ${address?.commune?.type ?? ''} ${address?.commune?.name_khmer ?? ''} ${address?.district?.type ?? ''} ${address?.district?.name_khmer ?? ''} ${address?.provice?.type ?? ''} ${address?.provice?.name_khmer ?? ''}`
	);
	let n = $derived(fields.find((e) => e.name === 'n')?.result ?? '');
	let date_1 = $derived(fields.find((e) => e.name === 'date_1')?.result ?? '');
	let date_2 = $derived(fields.find((e) => e.name === 'date_2')?.result ?? '');
	let height = $derived(fields.find((e) => e.name === 'height')?.result ?? '');
	let weight = $derived(fields.find((e) => e.name === 'weight')?.result ?? '');
	let r_trung = $derived(fields.find((e) => e.name === 'r_trung')?.result ?? '');
	let history = $derived(fields.find((e) => e.name === 'history')?.result ?? '');
	let eye_left = $derived(fields.find((e) => e.name === 'eye_left')?.result ?? '');
	let eye_right = $derived(fields.find((e) => e.name === 'eye_right')?.result ?? '');
	let ear = $derived(fields.find((e) => e.name === 'ear')?.result ?? '');
	let nose = $derived(fields.find((e) => e.name === 'nose')?.result ?? '');
	let throat = $derived(fields.find((e) => e.name === 'throat')?.result ?? '');
	let heart = $derived(fields.find((e) => e.name === 'heart')?.result ?? '');
	let blood_pressure = $derived(fields.find((e) => e.name === 'blood_pressure')?.result ?? '');
	let pulse = $derived(fields.find((e) => e.name === 'pulse')?.result ?? '');
	let reproductive = $derived(fields.find((e) => e.name === 'reproductive')?.result ?? '');
	let nerve = $derived(fields.find((e) => e.name === 'nerve')?.result ?? '');
	let bone = $derived(fields.find((e) => e.name === 'bone')?.result ?? '');
	let clinic = $derived(fields.find((e) => e.name === 'clinic')?.result ?? '');
	let p_place_of_birth = $derived(fields.find((e) => e.name === 'p_place_of_birth')?.result ?? '');
</script>

<input type="hidden" name="title" value="result_checking" />
<main style="max-width: 1200px;">
	<Header {get_document_setting} {logo} {n} {title_eng} {title_khm} />
	<div class="text-center">
		<h4
			style="color: {get_document_setting?.title_color}"
			class="kh_font_muol_light text-decoration-underline"
		>
			លទ្ធផលពិនិត្យសុខភាព
		</h4>
	</div>
	<br />
	<div class="section fs-5">
		<div class="mb-2">
			<table class="table table-bordered" border="1" cellspacing="0" cellpadding="5">
				<tbody>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">ឈ្មោះជាអក្សរខ្មែរ</td>
						<td colspan="2">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								readonly
								name="p_name_khmer"
								value={p_name_khmer}
								class="border-0"
								width="100%"
							/>
						</td>
						<td style="color: {get_document_setting?.text_body_color}">ជាអក្សរឡាតាំង</td>
						<td colspan="2">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								readonly
								name="p_name_latin"
								value={p_name_latin}
								class="border-0"
								width="100%"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">ភេទ </td>
						<td colspan="2">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								readonly
								value={p_gender}
								class="border-0"
								width="100%"
							/>
						</td>
						<td style="color: {get_document_setting?.text_body_color}">សញ្ជាតិ</td>
						<td colspan="2">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								readonly
								value={p_nation}
								class="border-0"
								width="100%"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">ថ្ងៃខែឆ្នាំកំណើត</td>
						<td colspan="2">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								readonly
								value={khmerDate(p_dob, 'date')}
								class="border-0"
								width="100%"
							/>
						</td>
						<td style="color: {get_document_setting?.text_body_color}">លេខទូរស័ព្ទ</td>
						<td colspan="2">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								readonly
								value={p_phone}
								class="border-0"
								width="100%"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}" colspan="3"
							>លេខអត្តសញ្ញាណប័ណ្ណ/សលាកបត្រស្នាក់នៅ/លិខិតឆ្លងដែន</td
						>
						<td colspan="3">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								readonly
								value={p_id_card_passport}
								class="border-0"
								width="100%"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">មុខរបរ</td>
						<td colspan="2">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								readonly
								value={p_occupation}
								class="border-0"
								width="100%"
							/>
						</td>
						<td style="color: {get_document_setting?.text_body_color}">ទីកន្លែងកំណើត</td>
						<td colspan="2">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								value={p_place_of_birth}
								name="p_place_of_birth"
								class="border-0"
								width="100%"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">អាសយដ្ឋានបច្ចុប្បន្ន</td>
						<td colspan="5">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								readonly
								value={p_address}
								class="border-0"
								width="100%"
							/>
						</td>
					</tr>
					<tr>
						<td
							style="color: {get_document_setting?.title_color}"
							class="text-center kh_font_muol_light"
							colspan="6">ការពិនិត្យ</td
						>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">១. កម្ពស់</td>
						<td class="text-center">
							<div>
								<InputDocument
									style="color: {get_document_setting?.text_input_color}"
									name="height"
									value={height}
									class="border-0 "
									width="100%"
								/>
							</div>
							<div style="color: {get_document_setting?.text_body_color}">សង់ទីម៉ែត្រ</div>
						</td>
						<td style="color: {get_document_setting?.text_body_color}" class="text-center"
							>ទម្ងន់</td
						>
						<td style="color: {get_document_setting?.text_body_color}" class="text-center">
							<div>
								<InputDocument
									style="color: {get_document_setting?.text_input_color}"
									name="weight"
									value={weight}
									class="border-0"
									width="100%"
								/>
							</div>
							<div>គីឡូក្រាម</div>
						</td>
						<td style="color: {get_document_setting?.text_body_color}" class="text-center"
							>រង្វង់ទ្រូង</td
						>
						<td style="color: {get_document_setting?.text_body_color}" class="text-center">
							<div>
								<InputDocument
									style="color: {get_document_setting?.text_input_color}"
									name="r_trung"
									value={r_trung}
									class="border-0"
									width="100%"
								/>
							</div>
							<div>សង់ទីម៉ែត្រ</div>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">២. ប្រវត្តិជំងឺ</td>
						<td colspan="5"
							><InputDocument
								style="color: {get_document_setting?.text_input_color}"
								name="history"
								value={history}
								class="border-0"
								width="100%"
							/></td
						>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">៣.ភ្នែកស្តាំង</td>
						<td colspan="2">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								name="eye_right"
								value={eye_right}
								class="border-0 "
								width="100%"
							/>
						</td>
						<td
							style="color: {get_document_setting?.text_body_color}"
							class="text-center"
							colspan="2"
							>ភ្នែកឆ្វេង
						</td>
						<td>
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								name="eye_left"
								value={eye_left}
								class="border-0 "
								width="100%"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}" colspan="6"
							>៤. ត្រចៀក ច្រមុះ បំពង់ករ</td
						>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color};width:20%">ត្រចៀក</td>
						<td style="width: 15%;">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								name="ear"
								value={ear}
								class="border-0 "
								width="100%"
							/>
						</td>
						<td style="color: {get_document_setting?.text_body_color};width:15%" class="text-center"
							>ច្រមុះ</td
						>
						<td style="width: 15%;">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								name="nose"
								value={nose}
								class="border-0 "
								width="100%"
							/>
						</td>
						<td
							style="color: {get_document_setting?.text_body_color};width:15%"
							class="text-center"
						>
							បំពង់ករ</td
						>
						<td style="width: 20%;">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								name="throat"
								value={throat}
								class="border-0 "
								width="100%"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}">៥.បេះដូង</td>
						<td>
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								name="heart"
								value={heart}
								class="border-0 "
								width="100%"
							/>
						</td>
						<td style="color: {get_document_setting?.text_body_color}" class="text-center"
							>សម្ពាធឈាម</td
						>
						<td style="color: {get_document_setting?.text_body_color}" class="text-end">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								value={blood_pressure}
								name="blood_pressure"
								class="border-0"
								width="75px"
							/> mmHg</td
						>
						<td style="color: {get_document_setting?.text_body_color}" class="text-center">ជីពចរ</td
						>
						<td style="color: {get_document_setting?.text_body_color}" class="text-end">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								value={pulse}
								name="pulse"
								width="100px"
							/>
							/នាទី</td
						>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}" colspan="2"
							>៧.ប្រដាប់បន្តពូជ និង ប្រព័ន្ធទឹកម៉ូត្រ</td
						>
						<td colspan="4">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								value={reproductive}
								name="reproductive"
								width="100%"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}" colspan="2"
							>៨.ប្រព័ន្ធសរសៃប្រសាទ</td
						>
						<td colspan="4">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								value={nerve}
								name="nerve"
								width="100%"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}" colspan="2"
							>៩.ប្រព័ន្ធឆ្អឹង</td
						>
						<td colspan="4">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								value={bone}
								name="bone"
								width="100%"
							/>
						</td>
					</tr>
					<tr>
						<td style="color: {get_document_setting?.text_body_color}" colspan="2"
							>១០.ការពិនិត្យអមគ្លីនិក</td
						>
						<td colspan="4">
							<InputDocument
								style="color: {get_document_setting?.text_input_color}"
								class="border-0"
								value={clinic}
								name="history"
								width="100%"
							/>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>

	<br />
	<div class="text-end fs-5" style="margin-right: 5em;">
		<KhDateTime date={date_1} name="date_1" />
		<div style="padding-right: 55px;font-weight: bold;">គ្រូពេទ្យព្យាបាល</div>
	</div>
	<div style="margin-left: 5em;" class="fs-5">
		<KhDateTime date={date_2} name="date_2" />
		<div style="padding-left: 45px;">បានឃើញ និងឯកភាព</div>
		<div style="padding-left: 20px;font-weight: bold;">ប្រធានគ្លីនិក/ប្រធានមន្ទីរពេទ្យ</div>
	</div>
</main>
