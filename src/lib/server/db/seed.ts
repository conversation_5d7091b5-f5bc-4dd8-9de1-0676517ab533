// import { generateIdFromEntropySize } from 'lucia';
// import { eq, like } from 'drizzle-orm';
// import { eq } from 'drizzle-orm';
// import { db } from '.';
// import { user } from '../schemas';
// import { category, inventory, parameter, patient, product, unit } from '../schemas';
// import { YYYYMMDD_Format } from '../utils';
// import { inventory, user } from './schema';
// import { generateIdFromEntropySize } from 'lucia';
// import { hash } from '@node-rs/argon2';
// import { eq } from 'drizzle-orm';
// import { db } from '.';
// import { patient, words } from '../schemas';

// import { eq } from 'drizzle-orm';
async function main() {
	// for (const e of patients) {
	// 	console.log(`patient_id: ${e.id}`)
	// 	await db.insert(patient)
	// 		.values({
	// 			id:Number(e.id),
	// 			age: Number(e.age),
	// 			dob:  now_datetime().slice(0,10),
	// 			gender: String(e.gender),
	// 			name_khmer: String(e.name_khmer),
	// 			name_latin: String(e.name_latin),
	// 			telephone: String(e.telephone),
	// 			province_id: Number(e.province_id) || null,
	// 			district_id: Number(e.district_id) || null,
	// 			commune_id: Number(e.commune_id) || null,
	// 			village_id: Number(e.village_id) || null,
	// 			other: String(e.other),
	// 			created_at: e.created_at || now_datetime()

	// 		})
	// 		.catch((e) => console.log(e))
	// }
	// const get_groups = await db.query.productGroupType.findMany({});
	// for (const e of get_groups) {
	// 	await db.insert(category).values({
	// 		id: e.id,
	// 		name: e.group_type
	// 	});
	// }
	// const products = await db.query.product.findMany({});
	// await db
	// 	.update(words)
	// 	.set({
	// 		category: 'objective'
	// 	})
	// 	.where(eq(words.category, 'common'));
	// console.log(products.length);
	// const get_units = await db.query.unit.findMany();
	// for (const e of get_units) {
	// 	if (e.group_id === null) {
	// 		await db.delete(unit).where(eq(unit.id, e.id));
	// 	}
	// }
	// await db.insert(setting).values({});
	// for(const  e of parameters){
	// 	await db.update(parameter).set({
	// 		para_unit_id:e.para_unit_id ? e.para_unit_id : null
	// 	})
	// 	.where(eq(parameter.id,e.id))
	// }
	// console.log('Done');
	process.exit(0);
}

main();
