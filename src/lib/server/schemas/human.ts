import {
	date,
	datetime,
	decimal,
	float,
	int,
	mysqlTable,
	text,
	varchar
} from 'drizzle-orm/mysql-core';
import { staff } from './staff';
import { relations } from 'drizzle-orm';
import { paymentType } from './payment';
export const salary = mysqlTable('salary', {
	id: int().primaryKey().autoincrement(),
	base_salary: decimal({ precision: 18, scale: 2 }).$type<number>(),
	allowance: decimal({ precision: 18, scale: 2 }).$type<number>(),
	deduction: decimal({ precision: 18, scale: 2 }).$type<number>(),
	bonus: decimal({ precision: 18, scale: 2 }).$type<number>(),
	total_salary: decimal({ precision: 18, scale: 2 }).$type<number>(),
	staff_id: int().references(() => staff.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	effective_date: varchar({ length: 20 }).notNull()
});

export const payroll = mysqlTable('payroll', {
	id: int().primaryKey().autoincrement(),
	salary_id: int()
		.references(() => salary.id, {
			onDelete: 'cascade',
			onUpdate: 'cascade'
		})
		.unique()
		.notNull(),
	payment_type_id: int().references(() => paymentType.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	payment_date: date({ mode: 'string' }).notNull(),
	amount: decimal({ precision: 18, scale: 2 }).$type<number>(),
	note: text()
});

export const salaryRelations = relations(salary, ({ one, many }) => ({
	staff: one(staff, {
		fields: [salary.staff_id],
		references: [staff.id]
	}),
	payroll: one(payroll),
	leave: many(leave)
}));

export const payrollRelations = relations(payroll, ({ one }) => ({
	salary: one(salary, {
		fields: [payroll.salary_id],
		references: [salary.id]
	}),
	paymentType: one(paymentType, {
		fields: [payroll.payment_type_id],
		references: [paymentType.id]
	})
}));

export const leave = mysqlTable('leave', {
	id: int().primaryKey().autoincrement(),
	staff_id: int().references(() => staff.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	start_date: datetime({ mode: 'string' }).notNull(),
	end_date: datetime({ mode: 'string' }).notNull(),
	days: float().notNull(),
	reason: text(),
	salary_id: int().references(() => salary.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	status: varchar({ length: 20 }).$type<'PENDING' | 'APPROVED' | 'REJECTED'>().default('PENDING')
});

export const leaseRelations = relations(leave, ({ one }) => ({
	staff: one(staff, {
		fields: [leave.staff_id],
		references: [staff.id]
	}),
	salary: one(salary, {
		fields: [leave.salary_id],
		references: [salary.id]
	})
}));
