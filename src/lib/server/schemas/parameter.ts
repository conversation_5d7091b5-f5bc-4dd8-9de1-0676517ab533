import { relations } from 'drizzle-orm';
import { int, float, mysqlTable, varchar, longtext } from 'drizzle-orm/mysql-core';
import { product } from './product';
import { laboratoryResult } from './laboratory';

export const parameter = mysqlTable('parameter', {
	id: int().primaryKey().autoincrement(),
	parameter: varchar({ length: 255 }),
	description: longtext(),
	gender: varchar({ length: 10 }),
	sign: varchar({ length: 10 }).default('-').notNull(),
	para_unit_id: int().references(() => paraUnit.id),
	mini: float(),
	maxi: float(),
	product_id: int().references(() => product.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	})
});
export const parameterRelations = relations(parameter, ({ one, many }) => ({
	product: one(product, {
		fields: [parameter.product_id],
		references: [product.id]
	}),

	paraUnit: one(paraUnit, {
		fields: [parameter.para_unit_id],
		references: [paraUnit.id]
	}),
	laboratoryResult: many(laboratoryResult)
}));

export const paraUnit = mysqlTable('para_unit', {
	id: int().primaryKey().autoincrement(),
	unit: varchar({ length: 255 })
});
export const paraUnitRelations = relations(paraUnit, ({ many }) => ({
	parameter: many(parameter)
}));
