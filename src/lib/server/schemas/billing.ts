import {
	boolean,
	datetime,
	decimal,
	float,
	int,
	mysqlTable,
	text,
	varchar
} from 'drizzle-orm/mysql-core';
import { progressNote, visit } from './visit';
import { relations } from 'drizzle-orm';
import { productOrder } from './product';
import { payment, paymentService, serviceType } from './payment';
import { patient } from './patient';
import { staff } from './staff';
export type TBillingStatus = 'paid' | 'partial' | 'debt' | 'checking' | 'paying';
export type TBillingType = 'IPD' | 'OPD' | 'POS' | 'CHECKING';
export type TChargeOn =
	| 'imagerie'
	| 'laboratory'
	| 'service'
	| 'prescription'
	| 'general'
	| 'vaccine'
	| 'bed';

export const billing = mysqlTable('billing', {
	id: int().primaryKey().autoincrement(),
	visit_id: int().references(() => visit.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	patient_id: int().references(() => patient.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	progress_note_id: int().references(() => progressNote.id, {
		onDelete: 'cascade'
	}),
	discount: varchar({ length: 50 }).notNull().default('0'),
	amount: decimal({ precision: 18, scale: 2 }).$type<number>(),
	total: decimal({ precision: 18, scale: 2 }).$type<number>(),
	total_after_tax: decimal({ precision: 18, scale: 2 }).$type<number>(),
	total_after_vat: decimal({ precision: 18, scale: 2 }).$type<number>(),
	paid: decimal({ precision: 18, scale: 2 }).$type<number>(),
	tax: float().default(0).notNull(),
	vat: float().default(0).notNull(),
	balance: decimal({ precision: 18, scale: 2 }).$type<number>(),
	return: decimal({ precision: 18, scale: 2 }).$type<number>(),
	status: varchar({ length: 10 }).$type<TBillingStatus>().default('checking').notNull(),
	billing_type: varchar({ length: 10 }).$type<TBillingType>(),
	created_at: datetime({ mode: 'string' }),
	hold: boolean('hold').default(false).notNull(),
	note: text('note'),
	staff_id: int().references(() => staff.id),
	service_type_id: int().references(() => serviceType.id)
});

export const charge = mysqlTable('charge', {
	id: int().primaryKey().autoincrement(),
	created_at: datetime({ mode: 'string' }),
	price: decimal({ precision: 18, scale: 2 }).$type<number>(),
	charge_on: varchar({ length: 20 }).$type<TChargeOn>(),
	billing_id: int()
		.references(() => billing.id, { onDelete: 'cascade' })
		.notNull()
});

export const chargeRelations = relations(charge, ({ one, many }) => ({
	billing: one(billing, {
		references: [billing.id],
		fields: [charge.billing_id]
	}),
	productOrder: many(productOrder)
}));

export const billingRelations = relations(billing, ({ one, many }) => ({
	visit: one(visit, {
		references: [visit.id],
		fields: [billing.visit_id]
	}),
	patient: one(patient, {
		references: [patient.id],
		fields: [billing.patient_id]
	}),
	progressNote: one(progressNote, {
		references: [progressNote.id],
		fields: [billing.progress_note_id]
	}),
	charge: many(charge),
	payment: many(payment),
	staff: one(staff, {
		references: [staff.id],
		fields: [billing.staff_id]
	}),
	serviceType: one(serviceType, {
		references: [serviceType.id],
		fields: [billing.service_type_id]
	}),
	paymentService: one(paymentService)
}));

export const tax = mysqlTable('tax', {
	id: int().primaryKey().autoincrement(),
	created_at: datetime({ mode: 'string' }),
	value: float().default(0).notNull()
});
