import { relations } from 'drizzle-orm';
import { boolean, datetime, int, mysqlTable, varchar } from 'drizzle-orm/mysql-core';
import { visit } from './visit';
import { product, unit } from './product';
import { patient } from './patient';
import { staff } from './staff';

export const vaccine = mysqlTable('vaccine', {
	id: int().primaryKey().autoincrement(),
	visit_id: int().references(() => visit.id, { onDelete: 'cascade' }),
	injection_id: int().references(() => injection.id, { onDelete: 'cascade' }),
	product_id: int().references(() => product.id, { onDelete: 'set null' }),
	discription: varchar({ length: 255 })
});

export const vaccineRelations = relations(vaccine, ({ one }) => ({
	visit: one(visit, {
		fields: [vaccine.visit_id],
		references: [visit.id]
	}),
	injection: one(injection, {
		fields: [vaccine.injection_id],
		references: [injection.id]
	}),
	product: one(product, {
		fields: [vaccine.product_id],
		references: [product.id]
	})
}));

export const injection = mysqlTable('injection', {
	id: int().primaryKey().autoincrement(),
	patient_id: int().references(() => patient.id),
	datetime: datetime({ mode: 'string' }).notNull(),
	unit_id: int().references(() => unit.id, { onDelete: 'set null' }),
	status: boolean().default(false),
	discription: varchar({ length: 255 })
});

export const injectionRelations = relations(injection, ({ many, one }) => ({
	appointmentInjection: many(appointmentInjection),
	vaccine: many(vaccine),
	patient: one(patient, {
		fields: [injection.patient_id],
		references: [patient.id]
	}),
	unit: one(unit, {
		fields: [injection.unit_id],
		references: [unit.id]
	})
}));

export const appointmentInjection = mysqlTable('appointment_injection', {
	id: int().primaryKey().autoincrement(),
	appointment: datetime({ mode: 'string' }).notNull(),
	requester_id: int().references(() => staff.id),
	injecter_id: int().references(() => staff.id),
	datetime_inject: datetime({ mode: 'string' }),
	status: boolean().default(false),
	discription: varchar({ length: 255 }),
	times: int().notNull().default(1),
	injection_id: int().references(() => injection.id, { onDelete: 'cascade' })
});

export const appointmentInjectionRelations = relations(appointmentInjection, ({ one }) => ({
	injection: one(injection, {
		fields: [appointmentInjection.injection_id],
		references: [injection.id]
	}),
	requester: one(staff, {
		fields: [appointmentInjection.requester_id],
		references: [staff.id]
	}),
	injecter: one(staff, {
		fields: [appointmentInjection.injecter_id],
		references: [staff.id]
	})
}));
