import { relations } from 'drizzle-orm';
import { int, mysqlTable, text, varchar, datetime, date } from 'drizzle-orm/mysql-core';
import { village, commune, district, provice } from './address';
import { visit } from './visit';
// @_Patient
export const patient = mysqlTable('patient', {
	id: int().primaryKey().autoincrement(),
	name_khmer: varchar({ length: 50 }).notNull(),
	name_latin: varchar({ length: 50 }),
	gender: varchar({ length: 10 }).notNull(),
	dob: date({ mode: 'string' }),
	id_cart_passport: varchar({ length: 255 }),
	education: varchar({ length: 255 }),
	nation: varchar({ length: 255 }),
	material_status: varchar({ length: 255 }),
	work_place: varchar({ length: 255 }),
	occupation: varchar({ length: 255 }),
	telephone: varchar({ length: 50 }),
	blood_group: varchar({ length: 50 }),
	other: varchar({ length: 255 }),
	village_id: int().references(() => village.id, { onDelete: 'set null' }),
	commune_id: int().references(() => commune.id, { onDelete: 'set null' }),
	district_id: int().references(() => district.id, { onDelete: 'set null' }),
	province_id: int().references(() => provice.id, { onDelete: 'set null' }),
	created_at: datetime({ mode: 'string' }).notNull(),
	f_name_khmer: varchar({ length: 50 }),
	f_name_latin: varchar({ length: 50 }),
	f_telephone: varchar({ length: 50 }),
	f_occupation: varchar({ length: 255 }),
	m_name_khmer: varchar({ length: 50 }),
	m_name_latin: varchar({ length: 50 }),
	m_telephone: varchar({ length: 50 }),
	m_occupation: varchar({ length: 255 }),
	c_name_khmer: varchar({ length: 50 }),
	c_name_latin: varchar({ length: 50 }),
	c_telephone: varchar({ length: 50 }),
	c_occupation: varchar({ length: 255 })
});
export const patientRelations = relations(patient, ({ one, many }) => ({
	provice: one(provice, {
		fields: [patient.province_id],
		references: [provice.id]
	}),
	district: one(district, {
		fields: [patient.district_id],
		references: [district.id]
	}),
	commune: one(commune, {
		fields: [patient.commune_id],
		references: [commune.id]
	}),
	village: one(village, {
		fields: [patient.village_id],
		references: [village.id]
	}),
	visit: many(visit)
}));

export const occupation_list = mysqlTable('occupation_list', {
	id: int().primaryKey().autoincrement(),
	occupation: text()
});
