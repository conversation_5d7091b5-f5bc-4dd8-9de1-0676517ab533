import { relations } from 'drizzle-orm';
import { datetime, decimal, int, mysqlTable, text, varchar } from 'drizzle-orm/mysql-core';
import { inventory } from './product';
import { staff } from './staff';
import { payment } from './payment';
export const supplier = mysqlTable('supplier', {
	id: int().primaryKey().autoincrement(),
	name: varchar({ length: 100 }),
	contact: varchar({ length: 100 }),
	address: varchar({ length: 255 }),
	company_name: varchar({ length: 100 })
});
export const exspend = mysqlTable('exspend', {
	id: int().primaryKey().autoincrement(),
	amount: decimal({ precision: 18, scale: 2 }).$type<number>(),
	credit: decimal({ precision: 18, scale: 2 }).$type<number>(),
	paid: decimal({ precision: 18, scale: 2 }).$type<number>(),
	supplier_id: int().references(() => supplier.id, {
		onDelete: 'set null',
		onUpdate: 'set null'
	}),
	recorder_id: int().references(() => staff.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	exspend_type_id: int().references(() => exspendType.id, {
		onUpdate: 'set null',
		onDelete: 'set null'
	}),
	invoice_no: varchar({ length: 255 }),
	datetime_invoice: datetime({ mode: 'string' }).notNull(),
	description: text()
});
export const exspendType = mysqlTable('exspend_type', {
	id: int().primaryKey().autoincrement(),
	type: varchar({ length: 255 }).unique().notNull()
});
export const supplierRelations = relations(supplier, ({ many }) => ({
	inventory: many(inventory)
}));
export const exspendRelations = relations(exspend, ({ one, many }) => ({
	recorder: one(staff, {
		references: [staff.id],
		fields: [exspend.recorder_id]
	}),
	exspendType: one(exspendType, {
		references: [exspendType.id],
		fields: [exspend.exspend_type_id]
	}),
	supplier: one(supplier, {
		references: [supplier.id],
		fields: [exspend.supplier_id]
	}),
	inventory: many(inventory),
	payment: many(payment)
}));
