import { boolean, datetime, int, mysqlTable, text } from 'drizzle-orm/mysql-core';
import { progressNote } from './visit';
import { bed } from './wardRoomBed';
import { relations } from 'drizzle-orm';
import { product } from './product';
import { staff } from './staff';
import { vitalSign } from './vitalSign';

export const activeDepartment = mysqlTable('active_department', {
	id: int().primaryKey().autoincrement(),
	datetime_in: datetime({ mode: 'string' }).notNull(),
	datetime_out: datetime({ mode: 'string' }),
	sender_id: int().references(() => staff.id),
	getter_id: int().references(() => staff.id),
	progress_note_id: int().references(() => progressNote.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	department_id: int()
		.notNull()
		.references(() => product.id),
	active: boolean().default(true).notNull(),
	remark: text()
});

export const activeBed = mysqlTable('active_bed', {
	id: int().primaryKey().autoincrement(),
	datetime_in: datetime({ mode: 'string' }).notNull(),
	datetime_out: datetime({ mode: 'string' }),
	day_stay: int().notNull().default(0),
	progress_note_id: int().references(() => progressNote.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	active_department_id: int().references(() => activeDepartment.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	active: boolean().default(true).notNull(),
	bed_id: int().references(() => bed.id)
});

export type TActiveBed = typeof activeBed.$inferSelect;

export const activeDepartmentBedRelations = relations(activeDepartment, ({ one, many }) => ({
	progressNote: one(progressNote, {
		references: [progressNote.id],
		fields: [activeDepartment.progress_note_id]
	}),
	department: one(product, {
		references: [product.id],
		fields: [activeDepartment.department_id]
	}),
	sender: one(staff, {
		references: [staff.id],
		fields: [activeDepartment.sender_id]
	}),
	getter: one(staff, {
		references: [staff.id],
		fields: [activeDepartment.getter_id]
	}),
	activeBed: many(activeBed),
	vitalSign: many(vitalSign)
}));
export const activeBedRelations = relations(activeBed, ({ one }) => ({
	bed: one(bed, {
		references: [bed.id],
		fields: [activeBed.bed_id]
	}),
	activeDepartment: one(activeDepartment, {
		references: [activeDepartment.id],
		fields: [activeBed.active_department_id]
	}),
	progressNote: one(progressNote, {
		references: [progressNote.id],
		fields: [activeBed.progress_note_id]
	})
}));
