import { relations } from 'drizzle-orm';
import { int, mysqlTable, boolean, text, varchar, datetime } from 'drizzle-orm/mysql-core';
import { visit } from './visit';
import { product } from './product';
import { parameter } from './parameter';
import { staff } from './staff';
import { patient } from './patient';

export const laboratoryGroup = mysqlTable('laboratory_group', {
	id: int().primaryKey().autoincrement(),
	laboratory_group: varchar({ length: 255 }).notNull()
});
export const laboratoryGroupRelations = relations(laboratoryGroup, ({ many }) => ({
	product: many(product)
}));

export const laboratoryRequest = mysqlTable('laboratory_request', {
	id: int().primaryKey().autoincrement(),
	product_id: int().references(() => product.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	visit_id: int().references(() => visit.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	})
	// created_at: datetime('created_at', { mode: 'string' }).notNull()
});

export const laboratory = mysqlTable('laboratory', {
	id: int().primaryKey().autoincrement(),
	doctor_comment: text(),
	input_by_id: int().references(() => staff.id),
	patient_id: int().references(() => patient.id),
	request_datetime: datetime({ mode: 'string' }),
	finish_datetime: datetime({ mode: 'string' }),
	sample: varchar({ length: 255 }),
	status: boolean().default(false),
	visit_id: int().references(() => visit.id, { onDelete: 'cascade', onUpdate: 'cascade' })
});

export const laboratoryRelations = relations(laboratory, ({ one }) => ({
	visit: one(visit, {
		fields: [laboratory.visit_id],
		references: [visit.id]
	}),
	inputBy: one(staff, {
		fields: [laboratory.input_by_id],
		references: [staff.id]
	}),
	patient: one(patient, {
		fields: [laboratory.patient_id],
		references: [patient.id]
	})
}));
export const laboratoryRequestRelations = relations(laboratoryRequest, ({ one, many }) => ({
	visit: one(visit, {
		fields: [laboratoryRequest.visit_id],
		references: [visit.id]
	}),
	product: one(product, {
		fields: [laboratoryRequest.product_id],
		references: [product.id]
	}),
	laboratoryResult: many(laboratoryResult)
}));

export const laboratoryResult = mysqlTable('laboratory_result', {
	id: int().primaryKey().autoincrement(),
	// created_at: datetime('created_at', { mode: 'string' }).notNull(),
	laboratory_request_id: int().references(() => laboratoryRequest.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	parameter_id: int().references(() => parameter.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	result: varchar('result', { length: 255 })
});

export const laboratoryResultRelations = relations(laboratoryResult, ({ one }) => ({
	laboratoryRequest: one(laboratoryRequest, {
		fields: [laboratoryResult.laboratory_request_id],
		references: [laboratoryRequest.id]
	}),
	parameter: one(parameter, {
		fields: [laboratoryResult.parameter_id],
		references: [parameter.id]
	})
}));
