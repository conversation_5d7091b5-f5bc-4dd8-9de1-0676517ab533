import { int, mysqlTable, text, longtext } from 'drizzle-orm/mysql-core';
import { relations } from 'drizzle-orm';
import { group } from './product';

// @_Template
export const template = mysqlTable('template', {
	id: int().primaryKey().autoincrement(),
	diagnosis: text(),
	template: longtext(),
	group_id: int().references(() => group.id)
});

export const templateRelations = relations(template, ({ one }) => ({
	group: one(group, {
		references: [group.id],
		fields: [template.group_id]
	})
}));
