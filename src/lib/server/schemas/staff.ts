import { relations } from 'drizzle-orm';
import {
	date,
	datetime,
	decimal,
	int,
	mysqlTable,
	primaryKey,
	varchar
} from 'drizzle-orm/mysql-core';
import { visit } from './visit';
import { vitalSign } from './vitalSign';
import { user } from './auth';
import { commune, district, provice, village } from './address';
import { product } from './product';
import { salary } from './human';

export const staff = mysqlTable('staff', {
	id: int().primaryKey().autoincrement(),
	gender: varchar({ length: 10 }),
	specialist: varchar({ length: 255 }),
	designation_id: int().references(() => designation.id, { onUpdate: 'cascade' }),
	title_id: int().references(() => title.id, { onUpdate: 'cascade' }),
	id_staff: varchar({ length: 100 }),
	blood_group: varchar({ length: 20 }),
	name_khmer: varchar({ length: 50 }),
	base_salary: decimal({ precision: 18, scale: 2 }).$type<number>(),
	name_latin: varchar({ length: 50 }),
	dob: date({ mode: 'string' }),
	datetime_start: datetime({ mode: 'string' }),
	datetime_stop: datetime({ mode: 'string' }),
	telephone: varchar({ length: 50 }),
	village_id: int().references(() => village.id),
	commune_id: int().references(() => commune.id),
	district_id: int().references(() => district.id),
	province_id: int().references(() => provice.id)
});

export const designation = mysqlTable('designation', {
	id: int().primaryKey().autoincrement(),
	kh: varchar({ length: 100 }),
	eng: varchar({ length: 100 })
});

export const title = mysqlTable('title', {
	id: int().primaryKey().autoincrement(),
	kh: varchar({ length: 100 }),
	eng: varchar({ length: 100 })
});

export type Staff = typeof staff.$inferSelect;
export const staffRelations = relations(staff, ({ one, many }) => ({
	provice: one(provice, {
		fields: [staff.province_id],
		references: [provice.id]
	}),
	district: one(district, {
		fields: [staff.district_id],
		references: [district.id]
	}),
	commune: one(commune, {
		fields: [staff.commune_id],
		references: [commune.id]
	}),
	village: one(village, {
		fields: [staff.village_id],
		references: [village.id]
	}),
	title: one(title, {
		fields: [staff.title_id],
		references: [title.id]
	}),
	visit: many(visit),
	vitalSign: many(vitalSign),
	user: one(user),
	staffToRole: many(staffToRole),
	staffToDemartment: many(staffToDemartment),
	salary: many(salary)
}));

export const staffToRole = mysqlTable(
	'staff_to_role',
	{
		staff_id: int()
			.references(() => staff.id)
			.notNull(),
		role_id: int()
			.references(() => role.id)
			.notNull()
	},
	(t) => [primaryKey({ columns: [t.staff_id, t.role_id] })]
);

export const staffToskillRelations = relations(staffToRole, ({ one }) => ({
	role: one(role, {
		fields: [staffToRole.role_id],
		references: [role.id]
	}),
	staff: one(staff, {
		fields: [staffToRole.staff_id],
		references: [staff.id]
	})
}));

export const role = mysqlTable('role', {
	id: int().primaryKey().autoincrement(),
	role: varchar({ length: 255 }).notNull().unique()
});

export const roleRelations = relations(role, ({ many }) => ({
	staffToRole: many(staffToRole)
}));

export const staffToDemartment = mysqlTable(
	'staff_to_department',
	{
		staff_id: int()
			.references(() => staff.id)
			.notNull(),
		department_id: int()
			.references(() => product.id)
			.notNull()
	},
	(t) => [primaryKey({ columns: [t.staff_id, t.department_id] })]
);

export const staffToDemartmentRelations = relations(staffToDemartment, ({ one }) => ({
	department: one(product, {
		fields: [staffToDemartment.department_id],
		references: [product.id]
	}),
	staff: one(staff, {
		fields: [staffToDemartment.staff_id],
		references: [staff.id]
	})
}));
