import { relations } from 'drizzle-orm';
import { int, mysqlTable, datetime, boolean, varchar, text } from 'drizzle-orm/mysql-core';
import { visit } from './visit';
import { product } from './product';
import { staff } from './staff';
import { patient } from './patient';
export type TType = 'text' | 'number' | 'datetime-local' | 'option' | 'textarea';

export const imagerieRequest = mysqlTable('imagerie_request', {
	id: int().primaryKey().autoincrement(),
	product_id: int().references(() => product.id),
	input_by_id: int().references(() => staff.id),
	scan_by_id: int().references(() => staff.id),
	patient_id: int().references(() => patient.id),
	visit_id: int().references(() => visit.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	result: text('text'),
	is_ob_form: boolean().default(false).notNull(),
	status: boolean().default(false),
	request_datetime: datetime({ mode: 'string' }),
	finish_datetime: datetime({ mode: 'string' }),
	note: text()
});

export const imagerieRequestRelations = relations(imagerieRequest, ({ one, many }) => ({
	visit: one(visit, {
		fields: [imagerieRequest.visit_id],
		references: [visit.id]
	}),
	product: one(product, {
		fields: [imagerieRequest.product_id],
		references: [product.id]
	}),
	inputBy: one(staff, {
		fields: [imagerieRequest.input_by_id],
		references: [staff.id]
	}),
	scanBy: one(staff, {
		fields: [imagerieRequest.scan_by_id],
		references: [staff.id]
	}),
	patient: one(patient, {
		fields: [imagerieRequest.patient_id],
		references: [patient.id]
	}),
	resultImagerie: many(resultImagerie)
}));

export const resultForm = mysqlTable('result_form', {
	id: int().primaryKey().autoincrement(),
	name: varchar({ length: 100 }).unique(),
	index: int().default(0).notNull(),
	type: varchar({ length: 20 }).$type<TType>().default('text').notNull()
});
export const resultFormRelations = relations(resultForm, ({ many }) => ({
	options: many(options)
}));

export const options = mysqlTable('options', {
	id: int().primaryKey().autoincrement(),
	name: varchar({ length: 100 }),
	result_form_id: int().references(() => resultForm.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	})
});
export const optionsRelations = relations(options, ({ one }) => ({
	resultForm: one(resultForm, {
		fields: [options.result_form_id],
		references: [resultForm.id]
	})
}));

export const resultImagerie = mysqlTable('result_imagerie', {
	id: int().primaryKey().autoincrement(),
	result: text(),
	imagerie_request_id: int().references(() => imagerieRequest.id, {
		onDelete: 'cascade',
		onUpdate: 'cascade'
	}),
	result_form_id: int().references(() => resultForm.id)
});

export const resultImagerieRelations = relations(resultImagerie, ({ one }) => ({
	imagerieRequest: one(imagerieRequest, {
		fields: [resultImagerie.imagerie_request_id],
		references: [imagerieRequest.id]
	}),
	resultForm: one(resultForm, {
		fields: [resultImagerie.result_form_id],
		references: [resultForm.id]
	})
}));
