import * as fs from 'node:fs/promises';
import path from 'path';
import sharp from 'sharp';
import { encodeBase32LowerCase } from '@oslojs/encoding';
import { db } from '../db';
import { uploads } from '../schemas';
import { and, eq, isNull, or } from 'drizzle-orm';
import { type related_type } from '../schemas';
const location = 'uploads';
function generateRandomId(bytes: number): string {
	const randomBytes = crypto.getRandomValues(new Uint8Array(bytes));
	return encodeBase32LowerCase(randomBytes);
}
export const fileHandle = {
	async insert(file: File, related_id: number, related_type: related_type, mimeType?: string) {
		if (!file?.size || file.size > 31457280) return;
		try {
			const uniqueId = generateRandomId(20);
			const fileExtension = file.type.split('/')[1];
			if (!['jpeg', 'png', 'jpg'].includes(fileExtension)) return;
			// Create folder name based on current year and month
			const date = new Date();
			const folderName = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
			// Create the folder if it doesn't exist
			const folderPath = path.join(process.cwd(), location, folderName);
			await fs.mkdir(folderPath, { recursive: true });
			const filePath = path.join(folderPath, `${uniqueId}.${fileExtension}`);
			const arrayBuffer = await file.arrayBuffer(); // orignal size can use this is down need compress file
			const optimizedBuffer = await sharp(Buffer.from(arrayBuffer))
				.resize(1000)
				.png({ progressive: true, force: false, quality: 50 })
				.jpeg({ progressive: true, force: false, quality: 50 })
				.toBuffer();
			await fs.writeFile(filePath, optimizedBuffer);
			await db.insert(uploads).values({
				filename: `/${location}/${folderName}/${uniqueId}.${fileExtension}`,
				mimeType: mimeType ? mimeType : file.type,
				related_id: related_id,
				related_type: related_type
			});

			return `/${location}/${folderName}/${uniqueId}.${fileExtension}`;
		} catch (e) {
			console.error('Error uploading file:', e);
		}
	},
	async update(
		file: File,
		oldFileName: string,
		related_id: number,
		related_type: related_type,
		mimeType?: string
	) {
		// Checking if mimeType avallable
		if (mimeType && !mimeType.includes('logo')) {
			const get_upload = await db.query.uploads.findFirst({
				where: eq(uploads.mimeType, mimeType)
			});
			if (!get_upload) return;
		}

		const filename = await this.insert(file, related_id, related_type, mimeType);
		await this.drop(oldFileName);
		return filename;
	},
	// drop is done
	async drop(fileName: string | null) {
		if (!fileName || fileName.length < 10 || fileName === 'image/png' || fileName === 'image/jpeg')
			return;
		await db
			.delete(uploads)
			.where(eq(uploads.filename, fileName))
			.catch((e) => {
				console.log(e);
			});
		const filePath = path.join(process.cwd(), fileName);
		await fs.unlink(filePath).catch((e) => {
			console.log(e);
		});

		// Remove empty folder if it exists
		const folderPath = path.dirname(filePath);
		const file = await fs.readdir(folderPath);
		if (file.length === 0) {
			await fs.rmdir(folderPath).catch((e) => {
				console.log(e);
			});
		}
	},
	async auto(body: FormData) {
		const files = body.getAll('file') as File[];
		const related_ids = body.getAll('related_id') as string[];
		const old_filenames = body.getAll('old_filename') as string[];
		const uuids = body.getAll('uuid') as string[];
		const related_types = body.getAll('related_type') as related_type[];
		if (files.length) {
			for (let index = 0; index < files.length; index++) {
				const file = files[index];
				const related_id = related_ids[index];
				const old_filename = old_filenames[index];
				const uuid = uuids[index];
				const related_type = related_types[index] as related_type;
				const old = old_filename ? old_filename : uuid;
				if (file.size) {
					const get_uploads = await db.query.uploads.findMany({
						where: or(
							eq(uploads.filename, old),
							eq(uploads.mimeType, old),
							and(eq(uploads.related_id, +related_id), eq(uploads.related_type, related_type))
						)
					});
					if (get_uploads?.length) {
						for (let index = 0; index < get_uploads.length; index++) {
							const element = get_uploads[index];
							if (!element.filename) {
								await db
									.delete(uploads)
									.where(or(isNull(uploads.filename), eq(uploads.filename, '')));
							}
							await this.drop(element.filename);
						}
						await this.insert(file, +related_id, related_type as related_type);
					} else {
						await this.insert(file, +related_id, related_type as related_type);
					}
				}
			}
		}
	}
};
