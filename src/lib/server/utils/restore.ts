import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import unzipper from 'unzipper';
import 'dotenv/config';
import { env } from '$env/dynamic/private';

const BACKUP_DIR = path.join(process.cwd(), 'backups');
const UPLOADS_DIR = path.join(process.cwd(), 'uploads');

const DB_NAME = env.DB_NAME;
const DB_USER = env.DB_USER;
const DB_PASSWORD = env.DB_PASSWORD;
const DB_HOST = env.DB_HOST;

export async function restoreBackup(zipFilePath: string) {
	if (!fs.existsSync(zipFilePath)) {
		throw new Error(`Backup file not found: ${zipFilePath}`);
	}

	const tempRestoreDir = path.join(BACKUP_DIR, 'restore-temp');

	// Step 1: Extract the zip archive
	console.log('📂 Extracting backup zip...');
	await fs
		.createReadStream(zipFilePath)
		.pipe(unzipper.Extract({ path: tempRestoreDir }))
		.promise();

	const sqlFilePath = path.join(tempRestoreDir, 'mysql_dump.sql');
	const extractedUploadsDir = path.join(tempRestoreDir, 'uploads');

	if (!fs.existsSync(sqlFilePath)) {
		throw new Error('❌ SQL dump not found in backup archive.');
	}

	// Step 2: Restore the MySQL database
	console.log('🔄 Restoring MySQL database...');
	const restoreCommand = `mysql -u${DB_USER} -p${DB_PASSWORD} -h${DB_HOST} ${DB_NAME} < "${sqlFilePath}"`;

	await new Promise<void>((resolve, reject) => {
		exec(restoreCommand, (error, _stdout, stderr) => {
			if (error) {
				console.error('❌ Database restore failed:', stderr);
				reject(error);
			} else {
				console.log('✅ Database restored.');
				resolve();
			}
		});
	});

	// Step 3: Replace uploads folder
	if (fs.existsSync(extractedUploadsDir)) {
		console.log('📁 Replacing uploads folder...');
		if (fs.existsSync(UPLOADS_DIR)) {
			fs.rmSync(UPLOADS_DIR, { recursive: true, force: true });
		}
		fs.renameSync(extractedUploadsDir, UPLOADS_DIR);
		console.log('✅ Uploads folder replaced.');
	} else {
		console.warn('⚠️ Uploads folder not found in the archive.');
	}

	// Step 4: Clean up
	fs.rmSync(tempRestoreDir, { recursive: true, force: true });
	console.log('🧹 Cleanup completed.');
}

export async function restoreFromExtractedFolder(extractedFolderPath: string) {
	if (!fs.existsSync(extractedFolderPath)) {
		throw new Error(`Extracted folder not found: ${extractedFolderPath}`);
	}

	const sqlFilePath = path.join(extractedFolderPath, 'mysql_backup.sql');
	const extractedUploadsDir = path.join(extractedFolderPath, 'uploads');

	if (!fs.existsSync(sqlFilePath)) {
		throw new Error('❌ mysql_backup.sql not found in extracted folder.');
	}

	// Step 1: Restore the MySQL database
	console.log('🔄 Restoring MySQL database...');
	const restoreCommand = `mysql -u${DB_USER} -p${DB_PASSWORD} -h${DB_HOST} ${DB_NAME} < "${sqlFilePath}"`;

	await new Promise<void>((resolve, reject) => {
		exec(restoreCommand, (error, _stdout, stderr) => {
			if (error) {
				console.error('❌ Database restore failed:', stderr);
				reject(error);
			} else {
				console.log('✅ Database restored.');
				resolve();
			}
		});
	});

	// Step 2: Replace uploads folder
	if (fs.existsSync(extractedUploadsDir)) {
		console.log('📁 Replacing uploads folder...');
		if (fs.existsSync(UPLOADS_DIR)) {
			fs.rmSync(UPLOADS_DIR, { recursive: true, force: true });
		}
		fs.renameSync(extractedUploadsDir, UPLOADS_DIR);
		console.log('✅ Uploads folder replaced.');
	} else {
		console.warn('⚠️ Uploads folder not found in the extracted folder.');
	}

	// Step 3: Clean up - delete all files and folder
	fs.rmSync(extractedFolderPath, { recursive: true, force: true });
	console.log('🧹 Cleanup completed - extracted folder deleted.');
}
