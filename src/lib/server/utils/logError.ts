const bot_token = '7489978998:AAFDjxR71l9EQgyJo8cBaUnc0nH5jM8A2PE';
const chat_id = '338746948';
import { DDMMYYYY_Format, YYYYMMDD_Format } from '.';
import { db } from '../db';
import { errorHandler } from '../schemas';
async function logError({ url, body, err }: { url: URL; body: FormData; err: string | unknown }) {
	const arry_body = Array.from(body).map((e) => e);
	const formatted = arry_body
		.map(([key, value]) => {
			const displayValue =
				value === '' ? '(empty)' : typeof value === 'object' ? JSON.stringify(value) : value;
			return `${key.padEnd(15)}: ${displayValue}`;
		})
		.join('\n');

	try {
		await db.insert(errorHandler).values({
			log_datetime: YYYYMMDD_Format.datetime(new Date()),
			url: JSON.stringify(url),
			body: formatted,
			log: JSON.stringify(err)
		});
		const khmer_date = DDMMYYYY_Format(new Date().toISOString(), 'datetime');
		const msg = khmer_date
			.concat('%0A')
			.concat('URL:')
			.concat(url.href)
			.concat('%0A')
			.concat('.......................................')
			.concat('%0A')
			.concat('Body:')
			.concat('%0A')
			.concat(formatted.replaceAll('\n', '%0A'))
			.concat('%0A')
			.concat('.......................................')
			.concat('%0A')
			.concat('Error:')
			.concat('%0A')
			.concat(JSON.stringify(err));

		const req = await fetch(
			`https://api.telegram.org/bot${bot_token}/sendMessage?chat_id=${chat_id}&text=${msg}`,
			{
				method: 'GET',
				redirect: 'follow'
			}
		);
		const res = await req.json();
		if (!res.ok) {
			console.log(res);
		}
		if (res.ok) {
			console.error(res.result.text);
		}
	} catch (e) {
		console.log(e);
	}
}

export default logError;
