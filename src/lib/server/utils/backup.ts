import { exec } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

// Promisify exec to work with async/await
const execPromise = promisify(exec);

// MySQL backup function
export async function backupMysql() {
	const backupFolder = 'backup';
	const timestamp = new Date().toISOString().replace(/[:.-]/g, '_'); // Create timestamp for uniqueness
	const backupFileName = `mysql_backup_${timestamp}.sql`;
	const zipFileName = `mysql_backup_${timestamp}.zip`;

	// Create the backup folder if it doesn't exist
	if (!fs.existsSync(backupFolder)) {
		fs.mkdirSync(backupFolder);
	}

	const backupFilePath = path.join(backupFolder, backupFileName);
	const zipFilePath = path.join(backupFolder, zipFileName);

	try {
		// Command to run mysqldump (adjust MySQL username, password, and database name)
		const mysqldumpCommand = `mysqldump -u YOUR_USERNAME -pYOUR_PASSWORD YOUR_DATABASE > ${backupFilePath}`;

		// Run the mysqldump command
		await execPromise(mysqldumpCommand);

		// Now, zip the backup
		const zipCommand = `zip -r ${zipFilePath} ${backupFilePath}`;

		// Run the zip command
		await execPromise(zipCommand);

		// Optional: Remove the raw SQL file after zipping
		fs.unlinkSync(backupFilePath);
	} catch (e) {
		console.error('Error during MySQL backup process:', e);
	}
}

// Run the backup function
