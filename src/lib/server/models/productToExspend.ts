import { eq } from 'drizzle-orm';
import { db } from '../db';
import { exspend, inventory } from '../schemas';
import logError from '../utils/logError';
import { YYYYMMDD_Format } from '../utils';
import { fail, redirect } from '@sveltejs/kit';
interface ProductToExspend {
	product_id: number;
	exspend_id: number | null;
	url: URL;
	body: FormData;
}
export async function pushProductToExspend({
	exspend_id,
	product_id,
	url,
	body
}: ProductToExspend) {
	if (!exspend_id || !product_id) return fail(400, { push_product_to_exspend: true });

	// Find for validation product are already added
	const get_exspend = await db.query.exspend.findFirst({
		where: eq(exspend.id, +exspend_id),
		with: {
			inventory: true
		}
	});
	if (get_exspend?.inventory.some((e) => e.product_id === +product_id)) {
		return fail(400, { productAdded: true });
	}
	const create_inventory: { id: number }[] = await db
		.insert(inventory)
		.values({
			product_id: +product_id,
			exspend_id: +exspend_id,
			datetime_buy: YYYYMMDD_Format.datetime(new Date())
		})
		.$returningId()
		.catch((e) => {
			logError({ url, body, err: e });
			return [];
		});

	await totalExspend(+exspend_id, url, body);
	redirect(
		303,
		`/product/purchase/create?exspend_id=${exspend_id}&inventory_id=${create_inventory[0].id}`
	);
}

async function totalExspend(id: number, url: URL, body: FormData) {
	const get_amount = await db.query.exspend.findFirst({
		where: eq(exspend.id, id),
		with: { inventory: true, payment: true }
	});
	const total_payment = get_amount?.payment.reduce((s, e) => s + Number(e.value), 0);
	const amount = get_amount?.inventory?.reduce((s, e) => s + Number(e.total_expense), 0);
	await db
		.update(exspend)
		.set({ amount: amount, paid: total_payment, credit: Number(amount) - Number(total_payment) })
		.where(eq(exspend.id, id))
		.catch((e) => {
			logError({ url, body, err: e });
			return [];
		});
}
