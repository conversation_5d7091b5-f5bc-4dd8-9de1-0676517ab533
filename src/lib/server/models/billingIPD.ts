import { db } from '../db';
import { billing, charge } from '../schemas';
import logError from '../utils/logError';
import { YYYYMMDD_Format } from '../utils';

type TPreBilling = {
	progress_id: number | null;
	patient_id: number;
	body: FormData;
	url: URL;
};
export async function billingIPD({ progress_id, patient_id, body, url }: TPreBilling) {
	const created_at = YYYYMMDD_Format.datetime(new Date());
	return await db.transaction(async (tx) => {
		const get_tax = await tx.query.tax.findFirst();
		// doing billing
		const create_billing: { id: number }[] = await tx
			.insert(billing)
			.values({
				created_at: created_at,
				progress_note_id: progress_id,
				billing_type: 'IPD',
				patient_id: patient_id,
				status: 'checking',
				tax: get_tax?.value || 0,
				amount: 0,
				total: 0
			})
			.$returningId()
			.catch((e) => {
				logError({ url, body, err: e });
				return [];
			});
		await tx
			.insert(charge)
			.values({
				billing_id: create_billing[0].id,
				charge_on: 'general',
				created_at: created_at
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await tx
			.insert(charge)
			.values({
				billing_id: create_billing[0].id,
				charge_on: 'prescription',
				created_at: created_at
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await tx
			.insert(charge)
			.values({
				billing_id: create_billing[0].id,
				charge_on: 'bed',
				created_at: created_at
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
		return create_billing[0].id;
	});
}
