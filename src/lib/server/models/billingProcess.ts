import { eq, like } from 'drizzle-orm';
import { db } from '../db';
import { billing, payment, paymentType } from '../schemas';
import { YYYYMMDD_Format } from '../utils';
import { isPaidService } from './isPaidService';
import logError from '../utils/logError';
import { fail, redirect, type Actions } from '@sveltejs/kit';
import { updateProductOrder, createProductOrder, deleteProductOrder } from '.';
import { pushAmountOPDToIPD, totalIPD } from './calulatorBillingIPD';
import { fileHandle } from '../upload';
import { setChargePrice } from './setChargePrice';
// import type { Actions } from '../../../routes/(dash)/billing/opd/[id]/checkout/$types';
type TBillingProcess = {
	billing_id: number;
	tax: number;
	disc: string;
	note: string;
	body: FormData;
	url: URL;
};
export async function billingProcess({ billing_id, tax, disc, note, body, url }: TBillingProcess) {
	const get_billing = await db.query.billing.findFirst({
		where: eq(billing.id, billing_id),
		with: {
			payment: true,
			charge: {
				with: {
					productOrder: true
				}
			}
		}
	});
	const sub_total = Number(get_billing?.amount);
	const total = disc.includes('%')
		? sub_total - (sub_total * Number(disc.replace('%', ''))) / 100
		: sub_total - Number(disc);
	const total_payment = get_billing?.payment.reduce((s, e) => s + Number(e.value), 0) || 0;
	const total_after_tax = total - (total * tax) / 100;
	const return_ = total_payment - total;
	if (total_payment === 0) {
		await db
			.update(billing)
			.set({
				total: total,
				discount: disc,
				balance: total,
				paid: 0,
				tax: tax,
				status: 'debt',
				return: 0,
				note: note,
				total_after_tax: total_after_tax
			})
			.where(eq(billing.id, billing_id))
			.catch((e) => logError({ url, body, err: e }));
	}
	if (total_payment >= total) {
		await db
			.update(billing)
			.set({
				total: total,
				discount: disc,
				balance: 0,
				paid: total,
				tax: tax,
				status: 'paid',
				note: note,
				return: return_,
				total_after_tax: total_after_tax
			})
			.where(eq(billing.id, billing_id))
			.catch((e) => logError({ url, body, err: e }));
	}
	if (total_payment < total && total_payment > 0) {
		await db
			.update(billing)
			.set({
				total: total,
				discount: disc,
				balance: total - total_payment,
				paid: total_payment,
				tax: tax,
				status: 'partial',
				return: 0,
				total_after_tax: total_after_tax,
				note: note
			})
			.where(eq(billing.id, billing_id))
			.catch((e) => logError({ url, body, err: e }));
	}
	if (get_billing?.billing_type === 'IPD') {
		await isPaidService(billing_id);
	}
}

export const checkout: Actions = {
	create_product_order: async (e) => {
		const { request, locals, url } = e;
		if (!locals.user) return fail(401, { err: 'unauthenticated' });
		const body = await request.formData();
		const { product_id, price, billing_id } = Object.fromEntries(body) as Record<string, string>;
		if (!billing_id) return fail(400, { err: 'billing_id' });
		const validErr = {
			product_id: false,
			billing_id: false,
			same_product: false
		};
		const get_billing = async () =>
			await db.query.billing.findFirst({
				where: eq(billing.id, +billing_id || 0),
				with: {
					patient: true,
					visit: {
						with: {
							presrciption: true
						}
					},
					charge: {
						with: {
							productOrder: {
								with: {
									product: true
								}
							}
						}
					}
				}
			});
		const billing_data = await get_billing();
		const charge_on_general = billing_data?.charge.find((e) => e.charge_on === 'general');
		if (!product_id.trim()) validErr.product_id = true;
		if (!billing_id.trim()) validErr.billing_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		await createProductOrder({
			charge_id: Number(charge_on_general?.id),
			price: +price,
			product_id: +product_id,
			qty: 1,
			body: body,
			url: url
		}).catch((e) => {
			logError({ url, body, err: e });
		});
	},
	remove_product_order: async (e) => {
		const { request, locals, url } = e;
		if (!locals.user) return fail(401, { err: 'unauthenticated' });
		const body = await request.formData();
		const { product_order_id } = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			product_order_id: false
		};
		if (!product_order_id.trim()) validErr.product_order_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		await deleteProductOrder(+product_order_id).catch((e) => {
			logError({ url, body, err: e });
		});
	},
	update_product_order: async (e) => {
		const { request, locals, url } = e;
		if (!locals.user) return fail(401, { err: 'unauthenticated' });
		const body = await request.formData();
		const product_order_id = body.getAll('product_order_id');
		const qty = body.getAll('qty');
		const disc = body.getAll('disc');
		const price = body.getAll('price');
		const unit_id = body.getAll('unit_id');
		const charge_id = body.get('charge_id') ?? '';
		const charge_on_laboratory = body.get('charge_on_laboratory') ?? '';
		if (!product_order_id.length && !charge_id) return fail(400, { errProductOrder: true });
		for (let index = 0; index < product_order_id.length; index++) {
			const product_order_id_ = product_order_id[index];
			const unit_id_ = unit_id[index];
			const qty_ = qty[index];
			const disc_ = disc[index].toString();
			const price_ = price[index];
			if (isNaN(+disc_) && !disc_.toString().includes('%')) return fail(400, { disc: true });
			await updateProductOrder({
				disc: disc_,
				price: +price_,
				product_order_id: +product_order_id_,
				qty: +qty_,
				unit_id: +unit_id_,
				body: body,
				url: url
			}).catch((e) => {
				logError({ url, body, err: e });
			});
		}
		if (charge_id) {
			await setChargePrice(+charge_id, +charge_on_laboratory).catch((e) => {
				logError({ url, body, err: e });
			});
		}
	},
	// discrout_prouct_order also use for upate single billing
	discount_product_order: async (e) => {
		const { request, locals, url } = e;
		if (!locals.user) return fail(401, { err: 'unauthenticated' });
		const body = await request.formData();
		const { product_order_id, qty, disc, charge_id, price, unit_id } = Object.fromEntries(
			body
		) as Record<string, string>;
		if (!product_order_id || !charge_id) return fail(400, { errProductOrder: true });
		if (isNaN(+disc) && !disc.toString().includes('%')) return fail(400, { disc: true });
		await updateProductOrder({
			disc: disc,
			price: +price,
			product_order_id: +product_order_id,
			qty: +qty,
			unit_id: +unit_id,
			body: body,
			url: url
		}).catch((e) => {
			logError({ url, body, err: e });
		});
		// await pushAmountOPDToIPD(+progress_note_id)
		// await pushPriceIPD(url)
	},
	process_billing: async (e) => {
		const { request, locals, url } = e;
		const billing_id = url.searchParams.get('billing_id') ?? '';
		if (!locals.user) return fail(401, { err: 'unauthenticated' });
		if (!billing_id) return fail(400, { err: 'billing_id' });
		const staff_id = locals.user?.staff_id;
		const progress_note_id = url.searchParams.get('ipd') ?? '';
		const visit_id = url.searchParams.get('opd') ?? '';

		const get_payment_type = await db.query.paymentType.findFirst({
			where: like(paymentType.by, '%CASH%')
		});
		const get_setting = await db.query.setting.findFirst();
		const body = await request.formData();
		const { bank_pay, cash_pay, payment_type_id, disc, note, tax } = Object.fromEntries(
			body
		) as Record<string, string>;
		const validErr = {
			payment: false,
			billing_disc: false,
			staff_id: false
		};
		if (isNaN(+disc) && !disc.includes('%')) validErr.billing_disc = true;
		if (!bank_pay && !cash_pay) validErr.payment = true;
		if (!staff_id) validErr.staff_id = true;
		if (!bank_pay && !payment_type_id) validErr.payment = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		if (progress_note_id) {
			await totalIPD(+progress_note_id);
			await pushAmountOPDToIPD(+progress_note_id);
		}
		if (Number(bank_pay) > 0 && payment_type_id) {
			const date_time = YYYYMMDD_Format.datetime(new Date());
			await db
				.insert(payment)
				.values({
					billing_id: +billing_id,
					datetime: date_time,
					payment_type_id: +payment_type_id,
					value: +bank_pay,
					staff_id: Number(staff_id)
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		if (Number(cash_pay) > 0) {
			await db
				.insert(payment)
				.values({
					billing_id: +billing_id,
					datetime: YYYYMMDD_Format.datetime(new Date()),
					payment_type_id: get_payment_type?.id,
					value: +cash_pay,
					staff_id: Number(staff_id)
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		await fileHandle.auto(body);
		await billingProcess({
			billing_id: +billing_id,
			disc: disc,
			tax: +tax || 0,
			note: note.toString(),
			body: body,
			url: url
		});
		await db
			.update(billing)
			.set({
				staff_id: Number(staff_id),
				hold: false
			})
			.where(eq(billing.id, +billing_id));
		if (get_setting?.print_bill) {
			if (progress_note_id) redirect(303, `/report/${progress_note_id}/billing/ipd`);
			if (!progress_note_id && visit_id) redirect(303, `/report/${visit_id}/billing/opd`);
			if (!progress_note_id && !visit_id) redirect(300, `/report/${billing_id}/billing/pos`);
		} else {
			redirect(303, `/billing/report`);
		}
	},
	hold: async (e) => {
		const { params, locals } = e;
		if (!locals.user) return fail(401, { err: 'unauthenticated' });
		const { id: billing_id } = params;
		const body = await e.request.formData();
		const { note } = Object.fromEntries(body) as Record<string, string>;
		if (!billing_id) return fail(400, { err: 'billing_id' });
		await db
			.update(billing)
			.set({
				hold: true,
				note: note
			})
			.where(eq(billing.id, +billing_id));
		redirect(303, `/billing/pos`);
	},
	delete_payment: async (e) => {
		const { url, locals, request } = e;
		const body = await request.formData();
		if (!locals.user) return fail(401, { err: 'unauthenticated' });
		const id = url.searchParams.get('payment_id') ?? '';
		if (!id) return fail(400, { err: 'payment_id' });
		await db
			.delete(payment)
			.where(eq(payment.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
	// add_patient: async (e) => {
	// 	const body = await request.formData();
	// 	const { billing_id } = Object.fromEntries(body) as Record<string, string>;
	// }
};
