import { eq } from 'drizzle-orm';
import { db } from '../db';
import { billing, charge, product, visit } from '../schemas';
import logError from '../utils/logError';
import { genVisitID, YYYYMMDD_Format } from '../utils';
import { createProductOrder } from '.';
import { fail } from '@sveltejs/kit';

type TPreBilling = {
	progress_id: number | null;
	patient_id: number;
	product_id: number;
	url: URL;
	body: FormData;
};
export async function billingService({
	progress_id,
	patient_id,
	product_id,
	url,
	body
}: TPreBilling) {
	const created_at = YYYYMMDD_Format.datetime(new Date());
	let charge_id: number | null = null;
	try {
		await db.transaction(async (tx) => {
			const visitID = await genVisitID('OPD');
			const get_tax = await tx.query.tax.findFirst();
			// doing billing
			const create_visit: { id: number }[] = await tx
				.insert(visit)
				.values({
					id: +visitID,
					checkin_type: 'SERVICE',
					patient_id: patient_id,
					date_checkup: created_at,
					progress_note_id: progress_id,
					etiology: 'Service',
					department_id: null
				})
				.$returningId();
			const billing_id: { id: number }[] = await tx
				.insert(billing)
				.values({
					created_at: created_at,
					visit_id: create_visit[0].id,
					billing_type: 'CHECKING',
					patient_id: patient_id,
					status: 'checking',
					tax: get_tax?.value || 0,
					amount: 0,
					total: 0
				})
				.$returningId();
			await tx.insert(charge).values({
				billing_id: billing_id[0].id,
				charge_on: 'general',
				created_at: created_at
			});

			const create_charge: { id: number }[] = await tx
				.insert(charge)
				.values({
					billing_id: billing_id[0].id,
					charge_on: 'service',
					created_at: created_at
				})
				.$returningId();

			charge_id = create_charge[0].id;
		});
		const get_product = await db.query.product.findFirst({
			where: eq(product.id, product_id)
		});
		if (get_product && charge_id) {
			await createProductOrder({
				charge_id: charge_id,
				product_id: get_product?.id,
				price: get_product?.price ?? 0,
				qty: 1,
				body: body,
				url: url
			});
		}
	} catch (e) {
		logError({ url, body, err: e });
		return fail(500, { message: '500 Server Error' });
	}
}
