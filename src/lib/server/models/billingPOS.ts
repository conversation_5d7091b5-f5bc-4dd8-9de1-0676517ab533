import { db } from '../db';
import { billing, charge } from '../schemas';
import { YYYYMMDD_Format } from '../utils';

export async function BillingPOS(staff_id: number): Promise<number> {
	const get_tax = await db.query.tax.findFirst();
	const created_at = YYYYMMDD_Format.datetime(new Date());
	// doing billing
	const get_billing: { id: number }[] = await db
		.insert(billing)
		.values({
			created_at: created_at,
			status: 'paying',
			tax: get_tax?.value || 0,
			amount: 0,
			total: 0,
			staff_id: staff_id,
			billing_type: 'POS',
			service_type_id: 1
		})
		.$returningId()
		.catch((e) => {
			console.log(e);
			return [];
		});
	await db
		.insert(charge)
		.values({
			billing_id: Number(get_billing[0].id),
			charge_on: 'general',
			created_at: created_at
		})
		.catch((e) => {
			console.log(e);
		});

	return get_billing[0].id;
}
