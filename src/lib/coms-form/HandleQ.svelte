<script lang="ts">
	import { page } from '$app/state';
	import { locale } from '$lib/translations/locales.svelte';
	import type { EventHandler } from 'svelte/elements';
	let { q_name = 'q' }: { q_name?: string } = $props();
	let timeout: number | NodeJS.Timeout | null = $state(null);
	const handleQ: EventHandler<Event, HTMLInputElement> = ({ currentTarget }) => {
		clearTimeout(timeout!);
		const form = currentTarget?.form;
		if (!form) return;
		timeout = setTimeout(() => {
			form.requestSubmit();
		}, 400);
	};
</script>

<input
	autocomplete="off"
	value={page.url.searchParams.get(q_name)}
	oninput={handleQ}
	type="search"
	name={q_name}
	class="form-control"
	placeholder={locale.T('search')}
/>
