<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	import type { EventHandler } from 'svelte/elements';
	interface Props {
		items: { name: any; id: any }[];
		name?: string;
		placeholder?: string;
		value?: { name: any; id: any }[];
		height?: string;
	}
	let { items, name = '', placeholder = 'Select', value = [], height = '300' }: Props = $props();
	let timeout: number | NodeJS.Timeout | null = $state(null);
	let q = $state('');
	const handleQ: EventHandler<Event, HTMLInputElement> = ({ currentTarget }) => {
		clearTimeout(timeout!);
		timeout = setTimeout(() => {
			q = currentTarget.value;
		}, 400);
	};
	let selected: { name: any; id: any }[] = $state([]);
	let data = $derived(items?.filter((el) => el.name?.toLowerCase().includes(q.toLowerCase())));
	$effect(() => {
		if (value) {
			selected = value;
		}
	});
	function addArray(id: any, name: any) {
		if (selected?.some((e) => e.id === id)) {
			selected = selected.filter((e) => e.id !== id);
		} else {
			selected?.push({ id: id, name: name });
		}
	}
</script>

<!-- svelte-ignore a11y_click_events_have_key_events -->
<!-- svelte-ignore a11y_no_static_element_interactions -->
<div
	onclick={() => {
		document.getElementById(name)?.focus();
	}}
	class="dropdown form-control m-0 p-0 shadow-none border-0"
>
	{#each selected as item (item.id)}
		<input type="hidden" {name} value={item.id} />
	{/each}
	<button
		type="button"
		onclick={() => (q = '')}
		class="form-control"
		data-bs-toggle="dropdown"
		aria-expanded="false"
		data-bs-auto-close="outsite"
	>
		<div class="col-10 text-start text-truncate" style="float:left;">
			{#if selected?.length}
				{#each selected as item (item.id)}
					<span class="text-bg-primary rounded px-2">
						{item?.name}
					</span>
					&nbsp;
				{/each}
			{:else}
				{placeholder}
			{/if}
		</div>
	</button>
	<div style="width: 100%;" class="dropdown-menu">
		<div class="px-2 pb-2">
			<input id={name} oninput={handleQ} class="form-control" type="search" />
		</div>
		<div style=" max-height: {height.concat('px')}; overflow-y: auto;">
			<div class="text-decoration-none">
				{#each data as item (item.id)}
					<button
						type="button"
						onclick={(e) => {
							e.stopPropagation();
							addArray(item?.id, item?.name);
						}}
						class={selected?.some((e) => e?.id === item?.id)
							? 'dropdown-item active'
							: 'dropdown-item'}
					>
						{#if selected?.some((e) => e?.id === item?.id)}
							<i class="fa-solid fa-square-check"></i>
						{:else}
							<i class="fa-regular fa-square"></i>
						{/if}
						&nbsp; {item?.name}
					</button>
				{/each}
				{#if data.length === 0}
					<button type="button" class="dropdown-item">
						<i class="fa-solid fa-magnifying-glass"></i>
						{locale.T('none_data')}
					</button>
				{/if}
			</div>
		</div>
	</div>
</div>
