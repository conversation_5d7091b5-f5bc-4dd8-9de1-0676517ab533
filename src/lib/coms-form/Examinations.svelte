<script lang="ts">
	import type { PageServerData } from '../../routes/(dash)/opd/[id]/objective/$types';
	import { slide } from 'svelte/transition';
	import Words from '$lib/coms-cu/Words.svelte';
	import Form from './Form.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	type Data = Pick<PageServerData, 'get_physicals' | 'get_words' | 'get_visit' | 'get_exams'>;
	interface Props {
		data: Data;
		action?: string;
	}

	let { data, action }: Props = $props();
	let { get_exams, get_visit, get_words } = $derived(data);
	let get_physicals = $state(data.get_physicals);
	let physicalExam_name = $state('');
	let obj: { [key: number]: string } = $state({});
	$effect(() => {
		for (const element of get_physicals) {
			const result = get_visit?.physicalExam.find((e) => e.physical_id === element.id);
			obj[element.id] = result?.result || '';
		}
	});
	let element_ = $state<{ id: number; physical: string | null; exam_id: number | null }>();
	let loading = $state(false);
	let type = $derived(
		(element_?.physical ?? '')
			.replaceAll(' ', '_')
			.replaceAll('/', '_')
			.replaceAll("'", '_')
			.concat(element_?.id.toString() ?? 'default')
	);
	let worlds = $derived(get_words?.filter((e) => e.type === type));
</script>

<Words
	category="objective"
	bind:value={obj[element_?.id as number]}
	name={element_?.physical ?? ''}
	words={worlds}
	modal_name_type={type}
/>
<div class="row">
	<div class="col-sm-12">
		{#each get_exams as item, index (item.id)}
			<button
				onclick={() => {
					if (physicalExam_name === item.examination) {
						physicalExam_name = '';
					} else {
						physicalExam_name = item.examination ?? '';
					}
				}}
				type="button"
				class={physicalExam_name === item.examination
					? 'btn w-100 btn-primary  d-flex align-items-start '
					: 'btn w-100 btn-light my-2  d-flex align-items-start'}
			>
				&nbsp;{index + 1}&nbsp;
				{#if physicalExam_name === item.examination}
					<span><i class="fas fa-angle-down"></i></span>
				{/if}
				{#if physicalExam_name !== item.examination}
					<span><i class="fas fa-angle-right"></i></span>
				{/if}
				&nbsp;{item?.examination}</button
			>

			{#if physicalExam_name === item.examination}
				<div class="pb-2" in:slide={{ duration: 300 }} out:slide={{ duration: 300 }}>
					{#if item.physical.length}
						<Form
							id={`id${item.id}`}
							data_sveltekit_keepfocus={true}
							data_sveltekit_noscroll={true}
							method="post"
							action={action ? action : '?/exam_result'}
							class="bg-primary-subtle rounded py-3 mt-2"
							reset={false}
							bind:loading
						>
							<div class="row px-4">
								{#each item.physical as element (element.id)}
									{@const exam_result = get_visit?.physicalExam.find(
										(e) => e.physical_id === element.id
									)}
									<input type="hidden" name="physical_id" value={element?.id ?? ''} />
									<input type="hidden" name="physical_exam_id" value={exam_result?.id ?? ''} />
									<div class="col-6">
										<button
											onclick={() => (element_ = element)}
											data-bs-toggle="modal"
											data-bs-target={`#${type}`}
											type="button"
											class="btn btn-link"
											>{element.physical ?? ''}
										</button>

										<!-- <p class="pb-0 mb-0">{element?.physical ?? ''}</p> -->
										<input
											autocomplete="off"
											bind:value={obj[element.id]}
											name="physical"
											class="form-control"
											type="text"
										/>
									</div>
								{/each}
								<div
									class="text-end pt-2
								"
								>
									<SubmitButton {loading} />
								</div>
							</div>
						</Form>
					{/if}
				</div>
			{/if}
		{/each}
	</div>
</div>
