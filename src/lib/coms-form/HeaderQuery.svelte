<script lang="ts">
	import { page } from '$app/state';
	interface Props {
		children?: import('svelte').Snippet;
		class?: string;
	}
	let { children, class: className = 'row' }: Props = $props();

	let page_: number | string = $derived(page.url.searchParams.get('page') || '');
	let limit: number | string = $derived(page.url.searchParams.get('limit') || '');
	function handleChange(event: Event) {
		const target = event.target as HTMLInputElement;
		const currentTarget = event.currentTarget as HTMLFormElement;
		if (!target.id.toString().includes('except_submit')) {
			currentTarget.requestSubmit();
		}
	}
</script>

<form data-sveltekit-keepfocus onchange={handleChange} class={className}>
	{#if page_}
		<input type="hidden" name="page" value={page_} />
	{/if}
	{#if limit}
		<input type="hidden" name="limit" value={limit} />
	{/if}

	{@render children?.()}
</form>
