<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	import { page } from '$app/state';
	import GenQRcode from '$lib/coms/GenQRcode.svelte';
	import type { related_type } from '$lib/type';
	import Confirm from '$lib/coms/Confirm.svelte';
	import { invalidateAll } from '$app/navigation';
	import { ShapeCropper } from '$lib/helper/cropper';
	let sid = $state(`id${Math.random().toString(36).substring(2, 9)}`);
	let fileInput: HTMLInputElement = $state() as HTMLInputElement;
	let originalFileName: string | null = $state(null);
	let {
		name,
		submit = false,
		default_image,
		related_type_,
		related_id,
		label,
		aspect_ratio = false
	}: {
		name: string;
		submit?: boolean;
		label?: string;
		default_image?: string | null;
		related_type_?: related_type;
		related_id?: string | number;
		aspect_ratio?: boolean;
	} = $props();
	let loading = $state(false);

	async function createQrUploads() {
		loadingQr = true;
		const formData = new FormData();
		formData.append('related_id', (related_id as string) ?? '');
		formData.append('related_type', (related_type_ as string) ?? '');
		formData.append('old_filename', default_image ?? '');
		const response = await fetch('/setting', {
			method: 'POST',
			body: formData
		});
		const result: {
			status: number;
			message: string;
			uuid: string;
			related_id: string;
			related_type: string;
		} = await response.json();
		uploads = {
			related_id: result.related_id,
			related_type: result.related_type,
			uuid: result.uuid
		};
		message = result.message;
		loadingQr = false;
	}
	let loadingQr = $state(false);
	let uploads = $state({ related_id: '', related_type: '', uuid: '' });
	let message = $state('');
	let canvas = $state<HTMLCanvasElement>() as HTMLCanvasElement;
	let cropper = $state<ShapeCropper>() as ShapeCropper;
	let croppedImageUrl: string | null = $state(null);
	let errorMessage: string | null = $state(null);
	let isLoading = $state(false);
	async function handleFileUpload(e: Event) {
		const files = (e.target as HTMLInputElement)?.files;
		if (!files || files.length === 0) return;
		document.getElementById(sid.concat('modal_crop'))?.click();
		try {
			isLoading = true;
			errorMessage = null;
			croppedImageUrl = null;
			await cropper.loadImage(files[0]);
		} catch (err) {
			errorMessage = err instanceof Error ? err.message : 'Failed to load image';
		} finally {
			isLoading = false;
		}
	}

	function cropImage() {
		try {
			if (!cropper) throw new Error('Cropper not initialized');
			const croppedCanvas = cropper.cropImage();
			croppedImageUrl = croppedCanvas.toDataURL('image/png');
			default_image = croppedImageUrl;

			const byteString = atob(croppedImageUrl.split(',')[1]);
			const mimeString = croppedImageUrl.split(',')[0].split(':')[1].split(';')[0];
			const ab = new ArrayBuffer(byteString.length);
			const ia = new Uint8Array(ab);
			for (let i = 0; i < byteString.length; i++) {
				ia[i] = byteString.charCodeAt(i);
			}
			// Create a Blob and then a File from the ArrayBuffer
			const blob = new Blob([ab], { type: mimeString });
			const file = new File([blob], originalFileName || 'croppedImage.png', { type: mimeString });
			const dataTransfer = new DataTransfer();
			dataTransfer.items.add(file);
			if (fileInput) fileInput.files = dataTransfer.files;
			errorMessage = null;
		} catch (err) {
			errorMessage = err instanceof Error ? err.message : 'Cropping failed';
			croppedImageUrl = null;
		}
	}

	function reset() {
		cropper?.reset();
		croppedImageUrl = null;
		errorMessage = null;
		fileInput.value = '';
	}

	$effect(() => {
		cropper = new ShapeCropper(canvas);

		return () => {
			cropper.destroy();
		};
	});
	$effect(() => {
		if (aspect_ratio) {
			cropper.setAspectRatio(null, true);
		} else {
			cropper.setAspectRatio(1, true);
		}
	});
</script>

<Confirm
	method="DELETE"
	action="/setting"
	body={[{ name: 'filename', value: default_image ?? '' }]}
	modal_id={'delete_modal'.concat(sid)}
/>

<!-- Generate QR  -->
<div class="input-group">
	{#if related_id}
		<button
			onclick={createQrUploads}
			data-bs-toggle="modal"
			data-bs-target={'#'.concat(sid.concat('qr_uploads'))}
			aria-label="qr-gen"
			type="button"
			class="btn btn-primary"><i class="fa-solid fa-qrcode"></i></button
		>
	{/if}
	<div
		class="modal fade"
		id={sid.concat('qr_uploads')}
		data-bs-keyboard="false"
		tabindex="-1"
		aria-hidden="true"
		aria-labelledby="staticBackdropLabel"
	>
		<div class="modal-dialog modal-sm">
			<div class="modal-content rounded-3 shadow">
				<div class="modal-header py-2 justify-content-center text-bg-warning">
					<span class="fs-5">
						<i class="fa-solid fa-mobile-screen-button fa-bounce"></i>
						ស្កែន QR ដើម្បីបញ្ជូលរូបភាព
					</span>
				</div>
				<div class="modal-body">
					<div class="w-100 h-100">
						{#if loadingQr}
							<img class="img-fluid" width="100%" src="/loading.gif" alt="" />
						{:else if message === 'success'}
							<GenQRcode
								data={{
									text: page.url.origin.concat(
										`/fileupload?uuid=${uploads.uuid}&related_id=${uploads.related_id}&related_type=${uploads.related_type}`
									)
								}}
							/>
						{:else}
							<h3 class="text-center text-danger" style="height: 258px;padding-top: 90px;">
								សូមព្យាយាមម្ដងទៀត!
							</h3>
						{/if}
					</div>
				</div>
			</div>
		</div>
	</div>
	<label for="" class="input-group-text">
		{#if label}
			{label}
		{:else}
			{locale.T('select')}
		{/if}
	</label>
	<button
		onclick={() => {
			document.getElementById(sid.concat('img_input'))?.click();
		}}
		aria-label="selectimage"
		class="form-control text-start"
		type="button"
	>
		{originalFileName}
	</button>
	{#if default_image}
		<!-- svelte-ignore a11y_no_noninteractive_element_to_interactive_role -->
		<!-- svelte-ignore a11y_click_events_have_key_events -->
		<img
			onclick={() => document.getElementById(sid.concat('btn_view_img'))?.click()}
			role="button"
			class="input-group-text"
			height="38px"
			src={default_image}
			alt=""
		/>
	{:else}
		<button
			onclick={() => invalidateAll()}
			aria-label="btn refresh"
			style="max-width: 40px;"
			type="button"
			class="form-control"><i class="fa-solid fa-rotate-right"></i></button
		>
	{/if}
</div>

<!-- Button trigger modal -->
<!-- Modal crope image -->
<button
	aria-label="selectimage"
	class="d-none"
	type="button"
	data-bs-toggle="modal"
	data-bs-target={`#${sid}`}
	id={sid.concat('modal_crop')}
>
</button>
<div
	class="modal"
	id={sid}
	data-bs-keyboard="false"
	tabindex="-1"
	aria-hidden="true"
	aria-labelledby="staticBackdropLabel_1"
>
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-body">
				{#if isLoading}
					<img style="height: auto;" class="img-thumbnail text-center" src="/loading.gif" alt="" />
					<br />
				{:else if errorMessage}
					<div class="error">{errorMessage}</div>
				{/if}
				<!-- svelte-ignore element_invalid_self_closing_tag -->
				<canvas
					style="height: auto;"
					id={sid.concat('canvas')}
					bind:this={canvas}
					class="img-thumbnail text-center"
				/>
				<div class="row">
					<div class="col-6">
						<button
							id={sid.concat('cropBtn')}
							aria-label="cropperimage"
							onclick={(e) => {
								cropImage();
								if (submit) {
									e.currentTarget.form?.requestSubmit();
								}
							}}
							disabled={loading}
							type="button"
							class:btn-light={loading}
							class="btn btn-primary w-100"
							data-bs-dismiss="modal"
						>
							{#if loading}
								<i class="fa-solid fa-spinner fa-spin"></i>
							{:else}
								<i class="fa-solid fa-scissors">...</i>
							{/if}
						</button>
					</div>
					<div class="col-6">
						<button
							aria-label="resetcropper"
							class="btn btn-warning w-100"
							type="button"
							onclick={reset}><i class="fa-solid fa-repeat"></i></button
						>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- Button view image  -->
<button
	onclick={() => invalidateAll()}
	type="button"
	class="btn btn-primary d-none"
	data-bs-toggle="modal"
	data-bs-target={'#'.concat(sid.concat('view_img'))}
	id={sid.concat('btn_view_img')}
>
	Launch static backdrop modal
</button>
<input type="hidden" name="old_filename" value={default_image} />
<input type="hidden" name="related_id" value={related_id} />
<input type="hidden" name="related_type" value={related_type_} />
<input type="hidden" name="data" value={page.url.searchParams.get('data')} />
<input
	{name}
	bind:this={fileInput}
	onchange={handleFileUpload}
	type="file"
	accept="image/*"
	class="d-none"
	id={sid.concat('img_input')}
/>
<!-- Modal Delete image -->
<div
	class="modal fade"
	id={sid.concat('view_img')}
	data-bs-keyboard="false"
	tabindex="-1"
	aria-hidden="true"
	aria-labelledby="staticBackdropLabel_2"
>
	<div class="modal-dialog">
		<div class="modal-content rounded-3 shadow">
			<div class="modal-body">
				<div class="w-100 h-100">
					<img src={default_image} class="img-thumbnail" alt="" />
				</div>
			</div>
			<div class="modal-footer flex-nowrap p-0">
				<button
					id="close_delete_modal"
					type="button"
					class=" btn btn-lg btn-link fs-6 text-decoration-none col-6 py-3 m-0 rounded-0 border-end"
					data-bs-dismiss="modal">{locale.T('close')}</button
				>
				<button
					data-bs-target={`#delete_modal${sid}`}
					data-bs-toggle="modal"
					disabled={loading}
					type="button"
					class="btn btn-lg btn-link fs-6 text-decoration-none text-danger col-6 py-3 m-0 rounded-0"
				>
					<strong>{locale.T('delete_')}</strong>
				</button>
			</div>
		</div>
	</div>
</div>
