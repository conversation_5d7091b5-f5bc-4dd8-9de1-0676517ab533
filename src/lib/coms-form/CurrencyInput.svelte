<script lang="ts">
	interface Props {
		amount: number | null | undefined;
		symbol: string | null | undefined;
		name?: string;
		class?: string;
		style?: string;
	}

	let {
		amount = $bindable(),
		symbol,
		name = '',
		class: className = 'input-group',
		style
	}: Props = $props();
</script>

<div class={className}>
	<span {style} class="input-group-text">{symbol ?? ''} </span>
	<input min="0" {name} bind:value={amount} step="any" type="number" class="form-control" />
</div>
