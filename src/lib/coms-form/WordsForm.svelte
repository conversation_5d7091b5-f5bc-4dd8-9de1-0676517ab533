<script lang="ts">
	import type { Words } from '$lib/type';
	interface Props {
		get_words: Words[];
		default_value: string;
		form_name: string;
		words_form: string;
		class?: string;
		onclick: () => void;
	}
	let {
		default_value,
		form_name,
		class: className = 'input-group mb-3 input-group-sm',
		get_words,
		onclick,
		words_form
	}: Props = $props();
</script>

<div class={className}>
	<button
		{onclick}
		data-bs-toggle="modal"
		aria-label="modaladdmedicine"
		data-bs-target="#patient_words"
		class="btn btn-primary"
		type="button"><i class="fa-regular fa-bookmark"></i></button
	>

	<datalist id={`list${words_form}`}>
		{#each get_words || [] as world_}
			<option value={world_?.text}></option>
		{/each}
	</datalist>
	<input
		value={default_value}
		autocomplete="off"
		type="text"
		class="form-control"
		list={`list${words_form}`}
		name={form_name}
	/>
</div>
