<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from '../../routes/(dash)/product/create/$types';
	import CurrencyInput from '$lib/coms-form/CurrencyInput.svelte';
	import SelectParam from './SelectParam.svelte';
	type Data = Pick<PageServerData, 'get_units' | 'get_currency'>;
	interface Props {
		data: Data;
		id: number | string;
		price: number | null;
		qty_per_unit: number;
		unit_id: number;
		main_unit_id: number | null;
	}
	let { data, id, price, qty_per_unit, unit_id, main_unit_id }: Props = $props();
	let { get_units, get_currency } = $derived(data);
	let find_unit = $derived(get_units.find((e) => e.id === unit_id));
	let find_main_unit = $derived(get_units.find((e) => e.id === main_unit_id));
</script>

<input type="hidden" name="sub_unit_id" value={id} />
<div class="col-3">
	<div class=" pb-3">
		<label for="unit_id_forSubUnit">{locale.T('select_new_unit')} </label>
		<div class="input-group">
			<SelectParam
				bind:value={unit_id}
				name="unit_id_forSubUnit"
				items={get_units?.map((e) => ({ id: e.id, name: e.unit }))}
			/>
		</div>
	</div>
</div>
<div class="col-3">
	<div class=" pb-3">
		<label for="sub_unit_qty">{locale.T('qty_in')} {find_unit?.unit ?? ''} </label>
		<div class="input-group">
			<span class="input-group-text">{find_main_unit?.unit ?? ''}</span>
			<input
				class="form-control"
				bind:value={qty_per_unit}
				name="sub_unit_qty"
				type="number"
				id="sub_unit_qty"
			/>
		</div>
	</div>
</div>
<div class="col-3">
	<div class=" pb-3">
		<label for="sub_unit_price"> {locale.T('price_for')} 1 {find_unit?.unit ?? ''} </label>
		<CurrencyInput name="sub_unit_price" bind:amount={price} symbol={get_currency?.currency} />
	</div>
</div>
