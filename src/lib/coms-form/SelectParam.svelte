<script lang="ts">
	import { goto } from '$app/navigation';
	import { navigating, page } from '$app/state';
	import Athtml from '$lib/coms/Athtml.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { EventHandler } from 'svelte/elements';
	interface Props {
		items: { name: any; id: any }[];
		name?: string;
		value?: string | number | null | undefined;
		fn?: () => void;
		placeholder?: string;
		height?: string;
		q_name?: string;
		submit?: boolean;
	}
	let {
		items,
		name = '',
		placeholder = 'Select',
		q_name = '',
		height = '300',
		submit = false,
		value = $bindable(''),
		fn
	}: Props = $props();
	let timeout: number | NodeJS.Timeout | null = $state(null);
	let q = $state('');
	let select_name = $derived(items.find((e) => e.id === value)?.name);
	const handleQ: EventHandler<Event, HTMLInputElement> = ({ currentTarget }) => {
		clearTimeout(timeout!);
		if (!value) q = '';
		timeout = setTimeout(() => {
			if (q_name) {
				const newUrl = new URL(page.url);
				newUrl?.searchParams?.set(q_name, currentTarget?.value);
				goto(newUrl, { keepFocus: true, noScroll: true });
			} else {
				q = currentTarget.value;
			}
		}, 400);
	};
	let data = $derived.by(() => {
		if (q_name) {
			return items.slice(0, 200);
		}
		if (!q_name) {
			return items.filter((el) => el.name?.toLowerCase().includes(q.toLowerCase())).slice(0, 200);
		}
	});
	function pushParam(e: string) {
		if (!name) return;
		const newUrl = new URL(page.url);
		newUrl?.searchParams?.set(name, e);
		goto(newUrl, { keepFocus: true, noScroll: true });
	}
</script>

<!-- svelte-ignore a11y_click_events_have_key_events -->
<!-- svelte-ignore a11y_no_static_element_interactions -->
<div
	onclick={() => document.getElementById(name.concat('except_submit'))?.focus()}
	class="dropdown form-control m-0 p-0 shadow-none border-0"
>
	{#if name}
		<input {value} type="hidden" {name} />
	{/if}
	<button
		type="button"
		onclick={() => (q = '')}
		class="form-control"
		data-bs-toggle="dropdown"
		aria-expanded="false"
	>
		<span class="col-10 text-start text-truncate" style="float:left;">
			<Athtml html={select_name ? select_name : placeholder} />
		</span>
		<!-- svelte-ignore a11y_click_events_have_key_events -->
		<!-- svelte-ignore a11y_no_static_element_interactions -->
		<span
			onclick={(e) => {
				e.stopPropagation();
				document.getElementById(name.concat('except_submit'))?.focus();
				value = '';
				select_name = '';
				pushParam('');
			}}
			class="float-end"
		>
			<i class="fa-solid fa-xmark"></i></span
		>
	</button>
	<div style="width: 100%;" class="dropdown-menu">
		<div class="px-2 pb-2">
			<input
				id={name.concat('except_submit')}
				oninput={handleQ}
				class="form-control"
				type="search"
			/>
		</div>

		<div style=" max-height: {height.concat('px')}; overflow-y: auto;">
			<div class="text-decoration-none">
				{#if ['form', 'goto', 'leave', 'popstate'].includes(navigating?.type ?? '')}
					<button type="button" class="dropdown-item">
						<i class="fa-solid fa-spinner fa-spin"></i>
						{locale.T('loading_')}
					</button>
				{:else}
					{#each data || [] as item (item.id)}
						<button
							type={submit ? 'submit' : 'button'}
							class:active={item.id === items.find((e) => e.id === value)?.id}
							onclick={() => {
								value = item.id;
								select_name = item.name;
								if (submit) return;
								pushParam(item.id);
								if (fn) {
									fn?.();
								}
							}}
							class="dropdown-item"
						>
							<Athtml html={item.name} />
						</button>
					{/each}
					{#if data?.length === 0}
						<button type="button" class="dropdown-item">
							<i class="fa-solid fa-magnifying-glass"></i>
							{locale.T('none_data')}
						</button>
					{/if}
				{/if}
			</div>
		</div>
	</div>
</div>
