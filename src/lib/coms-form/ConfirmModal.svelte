<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	import Form from './Form.svelte';
	interface Props {
		action?: string;
		slug?: string;
		id?: number | null;
		children?: import('svelte').Snippet;
	}

	let { action = '', slug = '', id = null, children }: Props = $props();
	let loading = $state(false);
</script>

<div class="modal fade" id="confirm_modal" data-bs-backdrop="static">
	<Form
		method="post"
		bind:loading
		fnSuccess={() => document.getElementById('close_confirme_modal')?.click()}
		{action}
		class="modal-dialog"
	>
		<input value={id} type="hidden" name="id" />
		<input value={slug} type="hidden" name="slug" />
		<div class="modal-content rounded-3 shadow">
			<div class="modal-header py-2 justify-content-center text-bg-primary">
				<span class="fs-3">
					<i class="fa-solid fa-flag"></i>
					{locale.T('verify')}
				</span>
			</div>
			<div class="modal-body">
				{#if children}
					{@render children?.()}
				{:else}
					<ul class="list-group">
						<li class="list-group-item">
							<i class="fa-solid fa-circle-exclamation"></i>
							{locale.T('confirm_yes')}
						</li>
					</ul>
				{/if}
			</div>
			<div class="modal-footer flex-nowrap p-0">
				<button
					id="close_confirme_modal"
					type="button"
					class=" btn btn-lg btn-link fs-6 text-decoration-none col-6 py-3 m-0 rounded-0 border-end"
					data-bs-dismiss="modal">{locale.T('no')}</button
				>
				<button
					data-bs-dismiss="modal"
					disabled={loading || !id}
					type="submit"
					class="btn btn-lg btn-link fs-6 text-decoration-none text-danger col-6 py-3 m-0 rounded-0"
				>
					<strong>{locale.T('yes')}</strong>
				</button>
			</div>
		</div>
	</Form>
</div>
