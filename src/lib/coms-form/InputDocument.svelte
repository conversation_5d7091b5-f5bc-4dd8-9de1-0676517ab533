<script lang="ts">
	interface Prop {
		name?: string;
		class?: string;
		value: string;
		width: string;
		readonly?: boolean;
		disabled?: boolean;
		placeholder?: string;
		data_list?: string[];
		style?: string;
		type?: 'text' | 'number' | 'date' | 'time' | 'datetime-local';
	}
	let {
		name,
		class: className,
		value = $bindable(),
		width,
		type = 'text',
		placeholder,
		data_list,
		style,
		disabled = false,
		readonly = false
	}: Prop = $props();
</script>

<input
	{readonly}
	{disabled}
	autocomplete="off"
	list={name?.concat('_list')}
	{placeholder}
	{name}
	bind:value
	class={'input_document '.concat(className ?? '')}
	style="height: 38px;width: {width};{style}"
	{type}
/>

<datalist id={name?.concat('_list')}>
	{#each data_list || [] as item}
		<option value={item}></option>
	{/each}
</datalist>
