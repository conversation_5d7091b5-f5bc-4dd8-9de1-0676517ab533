<script lang="ts">
	import Athtml from '$lib/coms/Athtml.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Form from './Form.svelte';
	let loading = $state(false);
	interface Props {
		class?: string;
		action: string;
		name: string;
		header: string;
		children?: import('svelte').Snippet;
		disabled?: boolean;
	}
	let {
		class: className = 'btn btn-primary',
		children,
		action,
		name,
		header,
		disabled
	}: Props = $props();
	let id = $state(`id${Math.random().toString(36).substring(2, 9)}`);
</script>

<button
	{disabled}
	type="button"
	data-bs-toggle="modal"
	data-bs-target={'#'.concat(id?.toString() ?? '')}
	class={className}
>
	<Athtml html={name} />
</button>

<div class="modal fade" tabindex="-1" role="dialog" {id} data-bs-backdrop="static">
	<Form
		{action}
		bind:loading
		fnSuccess={() => document.getElementById('close_confirm_submit')?.click()}
		method="post"
		class="modal-dialog modal-md"
	>
		<div class="modal-content rounded-3 shadow">
			<div class="modal-header py-2 justify-content-center text-bg-warning">
				<span class="fs-3">
					{header}
				</span>
			</div>
			<div class="modal-body">
				{@render children?.()}
			</div>
			<div class="modal-footer flex-nowrap p-0">
				<button
					id="close_delete_modal"
					type="button"
					class=" btn btn-lg btn-link fs-6 text-decoration-none col-6 py-3 m-0 rounded-0 border-end"
					data-bs-dismiss="modal">{locale.T('no')}</button
				>
				<button
					data-bs-dismiss="modal"
					disabled={loading}
					type="submit"
					class="btn btn-lg btn-link fs-6 text-decoration-none text-danger col-6 py-3 m-0 rounded-0"
				>
					<strong>{locale.T('yes')}</strong>
				</button>
			</div>
		</div>
	</Form>
</div>
