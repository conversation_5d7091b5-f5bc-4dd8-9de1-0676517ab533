<script lang="ts">
	import Currency from '$lib/coms/Currency.svelte';
	import type { PageServerData } from '../../routes/(dash)/billing/opd/[id]/$types';
	import { locale } from '$lib/translations/locales.svelte';
	import DiscountModal from './DiscountModal.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	type Data = Pick<PageServerData, 'charge_on_prescription' | 'get_billing' | 'get_currency'>;
	interface Props {
		data: Data;
	}

	let { data }: Props = $props();
	let { charge_on_prescription, get_billing, get_currency } = $derived(data);
	let presrciption = $derived(
		get_billing?.visit_id
			? get_billing.visit?.presrciption
			: get_billing?.progressNote?.presrciption
	);
</script>

<!-- Prescription  -->
{#if charge_on_prescription?.productOrder.length}
	<tr class="table-active table-warning fs-6">
		<td colspan="6">
			{#if get_billing?.billing_type === 'CHECKING'}
				{locale.T('daily_treatment_prescription')}
			{:else}
				{locale.T('presrciption')}
			{/if}
			{charge_on_prescription.productOrder.length}
			{locale.T('items')}</td
		>
	</tr>
	{#each charge_on_prescription.productOrder as item (item.id)}
		<tr>
			<td class="text-start">
				<DiscountModal
					currency={get_currency}
					discount={item?.discount}
					product_order_name={item.product?.products}
					charge_id={item.charge_id}
					product_order_id={item.id}
					product_order_price={item.price}
					product_price={item.product?.price}
					qty={item.qty}
					unit_id={item.unit_id}
					unit={item.product.unit}
					sub_units={item.product.subUnit}
				/>
				{#if get_billing?.billing_type !== 'CHECKING'}
					{#each presrciption || [] as item_1}
						{#if item_1.product_id === item.product_id}
							<span class="badge text-bg-primary">{item_1.product?.generic_name ?? ''}</span>
							<br />
							<span class="badge text-bg-success">{item_1.use}</span>
							<span class="badge text-bg-warning">
								{#if item_1.morning !== 0}
									{locale.T('morning')} {item_1.morning}
								{/if}
							</span>
							<span class="badge text-bg-warning">
								{#if item_1.noon !== 0}
									{locale.T('noon')} {item_1.noon}
								{/if}
							</span>
							<span class="badge text-bg-warning">
								{#if item_1.afternoon !== 0}
									{locale.T('afternoon')} {item_1.afternoon}
								{/if}
							</span>
							<span class="badge text-bg-warning">
								{#if item_1.evening !== 0}
									{locale.T('evening')} {item_1.evening}
								{/if}
							</span>
							<span class="badge text-bg-warning">
								{#if item_1.night !== 0}
									{locale.T('night')} {item_1.night}
								{/if}
							</span>
						{/if}
					{/each}
				{/if}
			</td>
			<td class="text-center">
				<div>
					<Currency class="fs-6" amount={item?.price} symbol={get_currency?.currency} />
					<input type="hidden" value={item?.price} name="item" />
					<span class="text-success"> / {item.unit?.unit}</span>
				</div>
			</td>
			<td class="text-center">
				<div>
					<Form
						reset={false}
						onchange={(e) => e.currentTarget.requestSubmit()}
						method="post"
						action="?/discount_product_order"
					>
						<input type="hidden" name="product_order_id" value={item.id} />
						<input type="hidden" name="disc" value={item.discount} />
						<input type="hidden" name="charge_id" value={charge_on_prescription.id} />
						<input type="hidden" name="price" value={item.price} />
						<input
							style="height: 35px;"
							class="form-control bg-light text-center fs-6"
							type="number"
							min="1"
							step="any"
							name="qty"
							value={item?.qty}
						/>
					</Form>
				</div>
			</td>
			<!-- <td>
				<div>
					<span>{item?.discount === '0' ? '' : item?.discount}</span>
					<input type="hidden" pattern="[0-9]+%?" name="disc" value={item?.discount} />
				</div>
			</td> -->
			<td class="text-center">
				<Currency class="fs-6" amount={item.total} symbol={get_currency?.currency} />
			</td>
			<td> </td>
		</tr>
	{/each}
{/if}
