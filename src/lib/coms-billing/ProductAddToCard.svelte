<script lang="ts">
	import { enhance } from '$app/forms';
	import { page } from '$app/state';
	import Currency from '$lib/coms/Currency.svelte';
	import { store } from '$lib/store/store.svelte';
	import type { EventHandler } from 'svelte/elements';
	import type { PageServerData } from '../../routes/(dash)/billing/pos/[id]/$types';
	import { browser } from '$app/environment';
	import type { SubmitFunction } from '@sveltejs/kit';
	import { locale } from '$lib/translations/locales.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	type Data = Pick<
		PageServerData,
		'get_categories' | 'get_products' | 'get_currency' | 'get_groups'
	>;

	let timeout: number | NodeJS.Timeout | null = $state(null);
	const handleQ: EventHandler<Event, HTMLInputElement> = ({ currentTarget }) => {
		clearTimeout(timeout!);
		const form = currentTarget?.form;
		if (!form) return;
		timeout = setTimeout(() => {
			form.requestSubmit();
		}, 400);
	};
	let group_id: number = $state(0);
	let category_id: number = $state(0);
	let inerHight: string = $derived(
		(Number(store.inerHight.replace('px', '')) + 100).toString().concat('px')
	);
	interface Props {
		data: Data;
		billing_id: number;
	}

	let { data, billing_id }: Props = $props();
	$effect(() => {
		if (browser) {
			if (window.innerWidth > 990) {
				localStorage.setItem('sb|sidebar-toggle', 'true');
				const sidebarToggle = localStorage.getItem('sb|sidebar-toggle');
				if (sidebarToggle !== 'false') {
					document.getElementById('sidebarToggle')?.click();
				}
			}
		}
		return () => {
			if (browser) {
				if (window.innerWidth > 990) {
					const sidebarToggle = localStorage.getItem('sb|sidebar-toggle');
					if (sidebarToggle !== 'true') {
						document.getElementById('sidebarToggle')?.click();
					}
				}
			}
		};
	});

	let { get_categories, get_products, get_currency, get_groups } = $derived(data);
	const onSubmit: SubmitFunction = () => {
		store.globalLoading = true;
		return async ({ update }) => {
			await update();
			store.globalLoading = false;
		};
	};
</script>

<div class="card bg-light">
	<form
		onchange={(e) => e.currentTarget.requestSubmit()}
		data-sveltekit-keepfocus
		class="card-header"
	>
		<div class="row g-1">
			<div class="col">
				<SelectParam
					name="category_id"
					placeholder={locale.T('category')}
					q_name="category_id"
					bind:value={category_id}
					items={get_categories.map((e) => ({ id: e.id, name: e.name }))}
				/>
			</div>
			<div class="col">
				<SelectParam
					name="group_id"
					placeholder={locale.T('group')}
					q_name="group_id"
					bind:value={group_id}
					items={get_groups.map((e) => ({ id: e.id, name: e.name }))}
				/>
			</div>

			<div class="col">
				<input type="hidden" name="billing_id" value={page.url.searchParams.get('billing_id')} />
				<input
					oninput={handleQ}
					name="q"
					value={page.url.searchParams.get('q')}
					autocomplete="off"
					type="search"
					class="form-control"
					placeholder={locale.T('search')}
					aria-label="Filter Products"
					aria-describedby="Filter Products"
				/>
			</div>
		</div>
	</form>

	<div style="height: {inerHight};" class=" overflow-auto justify-content-start">
		{#each get_products as item}
			<!-- svelte-ignore a11y_click_events_have_key_events -->
			<!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
			<form
				onclick={(e) => e.currentTarget.requestSubmit()}
				method="post"
				style="width: 140px;"
				use:enhance={onSubmit}
				action="?/create_product_order"
				class="col-xs-12 col-sm-5 col-md-4 col-lg-3 col-xl-2 border m-2 p-2 btn btn-light"
			>
				<input type="hidden" name="product_id" value={item.id} />
				<input type="hidden" name="price" value={item.price} />
				<input type="hidden" name="billing_id" value={billing_id} />

				{#if item.uploads?.filename}
					<img class="img-thumbnail" src={item.uploads.filename} alt="" />
				{:else}
					<img class="img-thumbnail" src="/no-image.jpg" alt="" />
					<!-- <enhanced:img src="/no-image.jpg" alt="An alt text" /> -->
				{/if}

				<span style="max-width: 150px;font-size: 13px;" class="d-inline-block">{item.products}</span
				>
				<br />

				<Currency
					class=" btn btn-warning btn-sm py-0 text-center w-100"
					amount={item.price}
					symbol={get_currency?.currency}
				/>
				<Currency
					class="btn btn-sm btn-warning py-0 text-center w-100"
					{get_currency}
					amount={item?.price}
					symbol={get_currency?.exchang_to}
				/>
				<button class="btn btn-sm btn-success py-0 text-center text-truncate w-100"
					>{item?.unit?.unit}</button
				>
			</form>
		{/each}
	</div>
</div>
