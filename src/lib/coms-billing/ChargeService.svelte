<script lang="ts">
	import Currency from '$lib/coms/Currency.svelte';
	import type { PageServerData } from '../../routes/(dash)/billing/opd/[id]/$types';
	import { locale } from '$lib/translations/locales.svelte';
	import DiscountModal from './DiscountModal.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	type Data = Pick<PageServerData, 'charge_on_service' | 'get_currency'>;
	interface Props {
		data: Data;
	}

	let { data }: Props = $props();
	let { charge_on_service, get_currency } = $derived(data);
</script>

<!-- Service -->
{#if charge_on_service?.productOrder.length}
	<tr class="table-active table-warning fs-6">
		<td colspan="6"
			>{locale.T('service')} {charge_on_service.productOrder.length} {locale.T('items')}
		</td>
	</tr>
	{#each charge_on_service.productOrder as item (item.id)}
		<tr>
			<td class="text-start">
				<DiscountModal
					currency={get_currency}
					discount={item?.discount}
					product_order_name={item.product?.products}
					charge_id={item.charge_id}
					product_order_id={item.id}
					product_order_price={item.price}
					product_price={item.product?.price}
					qty={item.qty}
					unit_id={item.unit_id}
					unit={item.product.unit}
					sub_units={item.product.subUnit}
				/>
				<br />
			</td>
			<td class="text-center">
				<fieldset>
					<input value={item?.price} type="hidden" name="price" />
					<Currency class="fs-6" amount={item?.price} symbol={get_currency?.currency} />
					<span class="text-success"> / {item.unit?.unit}</span>
				</fieldset>
			</td>
			<td class="text-center">
				<div>
					<Form
						reset={false}
						onchange={(e) => e.currentTarget.requestSubmit()}
						method="post"
						action="?/discount_product_order"
					>
						<input type="hidden" name="product_order_id" value={item.id} />
						<input type="hidden" name="disc" value={item.discount} />
						<input type="hidden" name="charge_id" value={charge_on_service.id} />
						<input type="hidden" name="price" value={item.price} />
						<input
							style="height: 35px;"
							class="form-control bg-light text-center fs-6"
							type="number"
							min="1"
							step="any"
							name="qty"
							value={item?.qty}
						/>
					</Form>
				</div>
			</td>

			<td class="text-center">
				<Currency class="fs-6" amount={item.total} symbol={get_currency?.currency} />
			</td>
			<td> </td>
		</tr>
	{/each}
{/if}
