<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	let loading = $state(false);
	interface Props {
		billing_id: number | undefined;
		status?: any;
		class?: string;
		children?: import('svelte').Snippet;
	}

	let {
		billing_id,
		status = 'active' as 'paid' | 'partial' | 'debt' | 'checking' | 'paying',
		class: className = 'btn btn-primary',
		children
	}: Props = $props();

	let id = $state(`id${Math.random().toString(36).substring(2, 9)}`);
</script>

<button
	disabled={status === 'paid' || status === 'partial' || status === 'debt'}
	type="button"
	data-bs-toggle="modal"
	data-bs-target={'#'.concat(id?.toString() ?? '')}
	class={className}
>
	{#if status === 'checking'}
		{@render children?.()}
	{/if}
	{#if status === 'paying'}
		<span>
			Process <i class="fa-solid fa-spinner fa-spin"></i>
		</span>
	{/if}
	{#if status !== 'checking' && status !== 'paying'}
		Paid
	{/if}
</button>

<div class="modal fade" tabindex="-1" role="dialog" {id} data-bs-backdrop="static">
	<div class="modal-dialog modal-sm" role="document">
		<div class="modal-content rounded-3 shadow">
			<Form
				bind:loading
				action="/patient/opd/?/process_billing"
				fnSuccess={() => document.getElementById('close_confirm_submit')?.click()}
				method="post"
			>
				<input value={billing_id} type="hidden" name="id" />
				<div class="modal-body p-4 text-center">
					<h5 class="mb-0">{locale.T('confirm_yes')}</h5>
				</div>
				<div class="modal-footer flex-nowrap p-0">
					<button
						id="close_confirm_submit"
						type="button"
						class="btn btn-lg btn-link fs-6 text-decoration-none col-6 py-3 m-0 rounded-0 border-end"
						data-bs-dismiss="modal">{locale.T('no')}</button
					>
					<button
						data-bs-dismiss="modal"
						disabled={loading}
						type="submit"
						class="btn btn-lg btn-link fs-6 text-decoration-none text-danger col-6 py-3 m-0 rounded-0"
					>
						<strong>{locale.T('yes')}</strong>
					</button>
				</div>
			</Form>
		</div>
	</div>
</div>
