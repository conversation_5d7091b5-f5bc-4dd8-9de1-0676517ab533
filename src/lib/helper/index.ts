import { locale } from '$lib/translations/locales.svelte';
import type { TFields } from '$lib/type';
export const YYYYMMDD_Format = {
	datetime: <T>(date: T) => {
		if (!date) return '';
		const dateObj = new Date(date as string);
		return new Intl.DateTimeFormat('en-GB', {
			dateStyle: 'short'
		})
			.format(dateObj)
			.split('/')
			.reverse()
			.join('-')
			.concat(' ')
			.concat(new Intl.DateTimeFormat('en-GB', { timeStyle: 'medium' }).format(dateObj));
	},
	date: <T>(date: T) => {
		if (!date) return '';
		const dateObj = new Date(date as string);
		return new Intl.DateTimeFormat('en-GB', {
			dateStyle: 'short'
		})
			.format(dateObj)
			.split('/')
			.reverse()
			.join('-');
	},
	year: <T>(date: T) => {
		if (!date) return '';
		const dateObj = new Date(date as string);
		return YYYYMMDD_Format.date(dateObj).slice(0, 4);
	},
	month: <T>(date: T) => {
		if (!date) return '';
		const dateObj = new Date(date as string);
		return YYYYMMDD_Format.date(dateObj).slice(5, 7);
	},
	day: <T>(date: T) => {
		if (!date) return '';
		const dateObj = new Date(date as string);
		return YYYYMMDD_Format.date(dateObj).slice(8, 10);
	},
	time: <T>(date: T) => {
		if (!date) return '';
		const dateObj = new Date(date as string);
		return new Intl.DateTimeFormat('en-GB', {
			timeStyle: 'medium'
		}).format(dateObj);
	}
};
const MS_IN_MINUTE = 1000 * 60;
const MS_IN_HOUR = MS_IN_MINUTE * 60;
const MS_IN_DAY = MS_IN_HOUR * 24;
export function calculateDifference(
	startDate: string | undefined | Date | null,
	endDate: string | undefined | Date | null
): { days: number; hours: number; minutes: number } | undefined {
	if (!startDate || !endDate) {
		return undefined;
	}

	const startDateTime = new Date(startDate).getTime();
	const endDateTime = new Date(endDate).getTime();
	const timeDifference = endDateTime - startDateTime;

	const days = Math.floor(timeDifference / MS_IN_DAY);
	const hours = Math.floor((timeDifference % MS_IN_DAY) / MS_IN_HOUR);
	const minutes = Math.floor((timeDifference % MS_IN_HOUR) / MS_IN_MINUTE);

	return { days, hours, minutes };
}

export function addDays(date: Date, days: number): Date {
	const result = new Date(date);
	result.setDate(result.getDate() + days);
	return result;
}

export function calculateAge(dob: string | undefined | null): number {
	if (!dob) return 0;
	const birthDate = new Date(dob);
	const today = new Date();
	let age = today.getFullYear() - birthDate.getFullYear();
	const monthDiff = today.getMonth() - birthDate.getMonth();
	if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
		age--;
	}
	return age;
}
export function DDMMYYYY_Format(
	datetime: string | undefined | null,
	opt: 'time' | 'date' | 'datetime'
): string {
	if (!datetime) {
		return '';
	}
	const date = new Intl.DateTimeFormat('en-GB', { dateStyle: 'short' })
		.format(new Date(datetime))
		.replaceAll('/', '-');
	const time = new Intl.DateTimeFormat('en-GB', { timeStyle: 'short', hour12: true })
		.format(new Date(datetime))
		.replaceAll('/', '-');
	if (opt === 'date') {
		return date;
	}
	if (opt === 'time') {
		return time;
	}
	if (opt === 'datetime') {
		return `${date} ${time}`;
	}
	return '';
}

export function dobToAge<T, U>(dob: T, today_: U) {
	if (!dob) return;
	const birthDate = new Date(String(dob));
	const today = new Date(today_ ? String(today_) : new Date());
	let years = today.getFullYear() - birthDate.getFullYear();
	let months = today.getMonth() - birthDate.getMonth();
	let days = today.getDate() - birthDate.getDate();

	if (days < 0) {
		months--;
		days += new Date(today.getFullYear(), today.getMonth(), 0).getDate();
	}

	if (months < 0) {
		years--;
		months += 12;
	}
	let age;
	if (years === 0 && months === 0) {
		age = `${days} ${locale.T('day')} `;
	} else if (years === 0) {
		age = `${months} ${locale.T('month')}`;
	} else {
		age = `${years} ${locale.T('year')}`;
	}
	return age;
}
export function khmerDate(
	date: string | undefined | null,
	opt: 'datetime' | 'date' | 'time' = 'datetime'
): string {
	if (!date) return '';

	const a = DDMMYYYY_Format(date, 'date'); // Expected: "2025-04-30"
	const aa = DDMMYYYY_Format(date, 'time'); // Expected: "08:15am"

	const [dd, mm, yyyy] = a.split('-'); // Correctly extract year, month, day

	const khmerNumbers = ['០', '១', '២', '៣', '៤', '៥', '៦', '៧', '៨', '៩'];
	const khmerMonths = [
		'មករា',
		'កុម្ភៈ',
		'មីនា',
		'មេសា',
		'ឧសភា',
		'មិថុនា',
		'កក្កដា',
		'សីហា',
		'កញ្ញា',
		'តុលា',
		'វិច្ឆិកា',
		'ធ្នូ'
	];

	const khmerMonthName = khmerMonths[+mm - 1]; // Convert "04" -> index 3 -> "មេសា"

	// Construct each part
	const formattedDate = `ថ្ងៃទី ${dd} ខែ ${khmerMonthName} ឆ្នាំ ${yyyy}`;
	const formattedTime = aa.replace('am', 'ព្រឹក').replace('pm', 'ល្ងាច');
	const formattedDateTime = `${formattedDate} ម៉ោង ${formattedTime}`;

	// Pick result based on option
	const result =
		opt === 'datetime' ? formattedDateTime : opt === 'date' ? formattedDate : formattedTime;

	// Convert all English digits to Khmer digits
	const resultInKhmer = result.replace(/\d/g, (d) => khmerNumbers[+d]);

	return resultInKhmer;
}
export function getValueField(fields: TFields[], name: string) {
	return fields.find((e) => e.name === name)?.result ?? '';
}
