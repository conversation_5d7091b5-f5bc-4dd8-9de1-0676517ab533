type QRCodeOptions = {
	size?: number;
	margin?: number;
	darkColor?: string;
	lightColor?: string;
};

type QRCodeModule = boolean[][];

class QRCodeGenerator {
	private static readonly ALPHANUMERIC_CHARS = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:';

	public static generate(text: string, options: QRCodeOptions = {}): HTMLCanvasElement {
		const { size = 200, margin = 4, darkColor = '#000000', lightColor = '#ffffff' } = options;

		// Create modules (simplified version - real QR has more complex encoding)
		const modules = this.createModules(text);

		// Create canvas
		const canvas = document.createElement('canvas');
		const ctx = canvas.getContext('2d');
		if (!ctx) throw new Error('Could not get canvas context');

		canvas.width = size;
		canvas.height = size;

		// Calculate module size
		const moduleCount = modules.length;
		const moduleSize = (size - margin * 2) / moduleCount;

		// Draw background
		ctx.fillStyle = lightColor;
		ctx.fillRect(0, 0, size, size);

		// Draw modules
		ctx.fillStyle = darkColor;
		for (let y = 0; y < moduleCount; y++) {
			for (let x = 0; x < moduleCount; x++) {
				if (modules[y][x]) {
					const px = margin + x * moduleSize;
					const py = margin + y * moduleSize;
					ctx.fillRect(px, py, moduleSize, moduleSize);
				}
			}
		}

		return canvas;
	}

	private static createModules(text: string): QRCodeModule {
		// Simplified module creation - real QR codes use much more complex encoding
		const length = text.length;
		const size = Math.max(21, Math.ceil(Math.sqrt(length * 8))) + 4; // Minimum size 21x21 for QR

		// Create empty module grid
		const modules: QRCodeModule = Array(size)
			.fill(null)
			.map(() => Array(size).fill(false));

		// Add position detection patterns (big squares in corners)
		this.addPositionPattern(modules, 0, 0);
		this.addPositionPattern(modules, size - 7, 0);
		this.addPositionPattern(modules, 0, size - 7);

		// Add timing patterns (lines between position patterns)
		for (let i = 8; i < size - 8; i++) {
			modules[6][i] = i % 2 === 0;
			modules[i][6] = i % 2 === 0;
		}

		// Add alignment pattern (small square)
		this.addAlignmentPattern(modules, size - 9, size - 9);

		// Simple data encoding (not real QR encoding)
		let x = size - 2;
		let y = size - 2;
		let goingUp = true;

		for (let i = 0; i < length; i++) {
			const charCode = text.charCodeAt(i);
			for (let j = 0; j < 8; j++) {
				if (x < 0) break;

				// Skip timing pattern columns
				if (x === 6) x--;

				const bit = (charCode >> j) & 1;
				modules[y][x] = bit === 1;

				// Move to next position (zig-zag pattern)
				if (goingUp) {
					y--;
					if (y < 0) {
						y = 0;
						x -= 2;
						goingUp = false;
					}
				} else {
					y++;
					if (y >= size) {
						y = size - 1;
						x -= 2;
						goingUp = true;
					}
				}
			}
		}

		return modules;
	}

	private static addPositionPattern(modules: QRCodeModule, x: number, y: number): void {
		// Outer black square
		for (let i = 0; i < 7; i++) {
			for (let j = 0; j < 7; j++) {
				modules[y + i][x + j] =
					i === 0 || i === 6 || j === 0 || j === 6 || (i >= 2 && i <= 4 && j >= 2 && j <= 4);
			}
		}
	}

	private static addAlignmentPattern(modules: QRCodeModule, x: number, y: number): void {
		// Small alignment pattern
		for (let i = 0; i < 5; i++) {
			for (let j = 0; j < 5; j++) {
				modules[y + i][x + j] = i === 0 || i === 4 || j === 0 || j === 4 || (i === 2 && j === 2);
			}
		}
	}
}

export class QRCodeApp {
	private textInput: HTMLInputElement;
	private generateBtn: HTMLButtonElement;
	private downloadBtn: HTMLButtonElement;
	private qrCodeContainer: HTMLDivElement;
	private currentQRCode: HTMLCanvasElement | null = null;

	constructor() {
		this.textInput = document.getElementById('text-input') as HTMLInputElement;
		this.generateBtn = document.getElementById('generate-btn') as HTMLButtonElement;
		this.downloadBtn = document.getElementById('download-btn') as HTMLButtonElement;
		this.qrCodeContainer = document.getElementById('qrcode-container') as HTMLDivElement;

		this.generateBtn.addEventListener('click', () => this.generateQRCode());
		this.downloadBtn.addEventListener('click', () => this.downloadQRCode());
	}

	private generateQRCode(): void {
		const text = this.textInput.value.trim();

		if (!text) {
			alert('Please enter some text or URL');
			return;
		}

		// Clear previous QR code
		this.qrCodeContainer.innerHTML = '';

		// Generate new QR code
		try {
			this.currentQRCode = QRCodeGenerator.generate(text, {
				size: 200,
				margin: 4,
				darkColor: '#000000',
				lightColor: '#ffffff'
			});

			this.qrCodeContainer.appendChild(this.currentQRCode);
		} catch (e) {
			console.error('Error generating QR code:', e);
			alert('Error generating QR code');
		}
	}

	private downloadQRCode(): void {
		if (!this.currentQRCode) {
			alert('Please generate a QR code first');
			return;
		}

		const link = document.createElement('a');
		link.download = 'qrcode.png';
		link.href = this.currentQRCode.toDataURL('image/png');
		link.click();
	}
}
// Export for use in other files
