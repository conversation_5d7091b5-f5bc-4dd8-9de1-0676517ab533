import {
	paymentService,
	words,
	document,
	fields,
	village,
	commune,
	district,
	provice,
	uploads,
	clinicinfo,
	documentSetting,
	patient,
	product,
	subUnit,
	title,
	unit
} from '$lib/server/schemas';
export { type Staff } from '$lib/server/schemas/staff';
export { type User } from '$lib/server/schemas/auth';
export type { related_type } from '$lib/server/schemas/uploads';
export { type TBillingStatus } from '$lib/server/schemas/billing';
export { type TActiveBed } from '$lib/server/schemas/departmentBed';
export {
	type ActivePresrciption,
	type Presrciption as TPrescription
} from '$lib/server/schemas/presrciption';
export { type TCurrency } from '$lib/server/schemas/setting';
export type PaymentService = typeof paymentService.$inferSelect;
export type Words = typeof words.$inferSelect;
export type TDocument = typeof document.$inferSelect;
export type TFields = typeof fields.$inferSelect;
export type TFile = typeof uploads.$inferSelect;
export type TClinicInfo = typeof clinicinfo.$inferSelect;
export type TDocumentSetting = typeof documentSetting.$inferSelect;
type TVillage = typeof village.$inferSelect;
type TCommune = typeof commune.$inferSelect;
type TDistrict = typeof district.$inferSelect;
type TProvice = typeof provice.$inferSelect;
export type TPatient = typeof patient.$inferSelect;
export type TTitle = typeof title.$inferSelect;
export type TProduct = typeof product.$inferSelect;
export type TSubUnit = typeof subUnit.$inferSelect;
export type TUnit = typeof unit.$inferSelect;

export type TAddress = {
	village: TVillage | null | undefined;
	commune: TCommune | null | undefined;
	district: TDistrict | null | undefined;
	provice: TProvice | null | undefined;
};

export interface TPatientFull extends TPatient {
	village: TVillage | null | undefined;
	commune: TCommune | null | undefined;
	district: TDistrict | null | undefined;
	provice: TProvice | null | undefined;
	uploads: TFile | null | undefined;
}
