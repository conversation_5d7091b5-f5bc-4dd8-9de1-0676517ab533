{"assign_result": "Assign result", "scanner": "Scanner", "death_date": "Death date", "requester": "Requester", "checker": "Checker", "diagnosis_of_death": "Diagnosis of death", "birth_report": "Birth reprot", "death_report": "Death report", "date_of_death": "Date of death", "birth": "Birth", "child_name": "Child's name", "child_dob": "Child's DOB", "child_gender": "Child's gender", "delivery_type": "Delivery type", "delivery_difficulty": "Delivery difficulty", "delivery_section": "Delivery section", "delivery_normal": "Delivery normal", "father_name": "Father's name", "mother_name": "Mother's name", "death": "Death", "today_money": "Today's money", "total_expense": "Total expense", "total_income": "Total income", "total_profit": "Total profit", "profit": "Profit", "present_history": "Present history", "human_hesource_dashboard": "Human Resource Dashboard", "total_employeeses": "Total employees", "alert": "<PERSON><PERSON>", "payout_payroll": "Payout payroll", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "daily": "Daily", "monthly": "Monthly", "daily_report": "Daily report", "monthly_report": "Monthly report", "employees": "Employees", "payroll_list": "Payroll list", "active_employees": "Active employees", "inactive_employees": "Inactive employees", "payroll": "Payroll", "people": "People", "salary": "Salary", "duration_of_work": "Duration of work", "total_base_salary": "Total base salary", "total_allowance": "Total allowance", "total_deduction": "Total deduction", "total_bonus": "Total bonus", "base_salary": "Base salary", "allowance": "Allowance", "deduction": "Deduction", "bonus": "Bonus", "total_salary": "Total Salary", "effective_date": "Effective date", "leave": "Leave", "role_title": "Role title", "cart_saved": "<PERSON><PERSON> saved", "activity": "Activity", "security": "Security", "personal_info": "Personal information", "customer": "Customer", "type_visit": "Type Visit", "type_payment": "Type of Payment", "full_info": "Full Information", "clear": "Clear", "hold": "Hold", "not_expired": "Not expired", "near_expired": "Near expired", "expired": "Expired", "list_units": "List units", "catch_out": "Catch out", "catch_out_medicine_expire": "Catch out medicine expire", "infomations_medicine_expire": "Infomations medicine expire", "duration_of_expire": "Duration of expire", "exchange_to": "Exchange to", "exchange_rate": "Exchange rate", "currency_rate": "Currency rate", "less_than": "Less than", "more_than": "More than", "between": "Between", "setting": "Setting", "amount_expire": "Amount expire", "text_input_color": "Text input color", "text_color": "Text color", "document_setting": "Document setting", "logo_size": "Logo size", "clinic_title_en_size": "Clinic title en size", "clinic_title_en_color": "Clinic title en color", "clinic_title_kh_color": "Clinic title kh color", "clinic_title_kh_size": "Clinic title kh size", "header_size": "Header size", "header_color": "Header color", "footer_size": "Footer size", "footer_color": "Footer color", "title_size": "Title size", "title_color": "Title color", "currency": "<PERSON><PERSON><PERSON><PERSON>", "all": "All", "qty_expire": "qty expire", "pleas_valiidate_stock_before_catch_out": "Please validate current medicine stock before catch out", "qty_bought": "qty bought", "qty_catch_out": "qty catch out", "will_delete": "will delete", "verify": "Verification", "partial": "Partial", "pdf": "PDF", "mother": "Mother", "father": "Father", "carer": "Carer", "guardian": "Guardian", "unknown": "Unknown", "hr": "Human resurces", "id_card_or_passport": "ID card or passport", "education": "Education", "material": "Material", "nationality": "Nationality", "work_place": "Work place", "occupation": "occupation", "amount_use": "Amount of use", "send_back": "Send back", "input_price": "Input price", "submit_successfully": "Submit successfully", "submit_was_not_successfully": "Submit was not successfully", "dialy_visi_consult": "Daily consult", "daily_treatment_prescription": "Daily treatment prescription", "quantity_of_goods": "Quantity of goods", "discount_value_or_percent": "Discount Ex.(10 or 10%)", "advice_or_teaching": "Advice or teaching", "signature": "Signature", "blood_group": "Blood group", "title_person": "Title", "dorlar": "Dollar", "real": "Riel", "close": "Close", "take": "Take", "general": "General", "reject": "Reject", "came": "Came", "next": "Next", "previous": "Previous", "up_down": "Up down", "print": "Print", "left_right": "Left right", "ecg": "Xray", "finish": "Finish", "option": "Option", "number": "Number", "text": "Text", "textarea": "Textarea", "working": "Working", "see_more": "See more", "see_less": "See less", "start_working": "Start Working", "remark": "Remark", "stop_working": "Stop working", "evolation": "Evolation", "nursing_sign": "Nursing Sign", "sender": "Sender", "getter": "Receiver", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "change_password": "Change password", "wrong_password": "Wrong password", "invalid_username": "Invalid username", "new_password": "New password", "change": "Change", "change_username": "Change username", "forget_password": "Forget password", "register_account": "Register account", "go_to_visit": "Go to visit", "chang_bed": "Change bed", "move_to": "Move to", "send_back_to_checking": "Send back to checking", "after_done_of_pay_you_can_not_edit_infomations": "After done of pay can not edit infomations", "please_verify_before_sending": "Please verify before sending", "paying": "Paying", "accessment": "Accessment", "action": "Action", "actions": "Actions", "add": "Add", "add_examination": "Add examination", "add_imagerie": "Add imagerie", "add_lab_group": "Add lab group", "add_parameter_roup": "Add parameter group", "register_patient": "Register patient", "add_patient": "Add patient", "add_product": "Add product", "add_template": "Add template", "add_unit": "Add unit", "address": "Address", "adjust_stock": "Adjust stock", "admitted_to_ipd": "Admitted to IPD", "afternoon": "Afternoon", "age": "Age", "already_add": "Already added", "already_charged": "Already charged", "already_paid": "Already paid", "am": "am", "amount": "Amount", "amount_get": "Amount", "amount_payment": "Amount payment", "appintment": "Appointment", "available": "Available", "back": "Back", "backup": "Backup", "backup_list": "List backup", "balance": "Balance", "barcode": "Barcode", "bed": "Bed", "bill": "Bill", "billing": "Billing", "billing_ipd": "Billing_IPD", "billing_opd": "Billing_OPD", "browse": "Browse", "category": "Category", "charge_on_service": "Charge on service", "check_for_count": "Check  for count stock", "checking": "Checking", "checkout": "Checkout", "clinic_info": "Clinic infomation", "comment": "Comment", "commune": "<PERSON><PERSON><PERSON>", "company": "Company", "confirm_delete": "Noted after deleted you can't restore!", "confirm_yes": "Click YES to confirm!", "contact": "Contact", "copied": "<PERSON>pied", "copy": "Copy", "cost": "Cost", "credit": "Credit", "daily_payment": "Daily payment", "dashboard": "Dashboard", "date": "Date", "date_bought": "Date bought", "date_checkin": "Date checkin", "date_checkout": "Date checkout", "dates": "Dates", "day": "day", "days": "Day", "day_stay": "Stay", "debt": "debt", "delete_": "Delete", "delete_backup": "Delete backup", "delete_q": "Delete ?", "delete_unit": "Delete unit", "department": "Department", "description": "Description", "diagnosis": "Diagnosis", "diagnosis_khmer": "Diagnosis khmer", "discharge": "Discharge", "discharge_and_go_home": "Discharge and go home", "discount": "Discount", "district": "District", "dob": "DOB", "doc": "Doc", "doctor": "Doctor", "documents": "Documents", "done": "Done", "download": "Download", "duration": "Duration", "duration_of_treatment": "Duration of treatment", "echo_ob": "Echo OB", "edit": "Edit", "emr": "Emergency", "end": "End", "etiology": "Etiology", "evalution": "Evalution", "evening": "Evening", "evolution": "Evolution", "examination": "Examination", "expires_date": "Expires date", "exspend": "Exspend", "exspend_type": "Exspend type", "exspender": "Exspender", "female": "Female", "fill": "Fill", "form_doc": "Form document", "from_date": "From", "gender": "Gender", "generic_name": "Generic name", "go_to_payment": "Go to payment", "goods": "Goods", "grand_total": "Grand total", "group": "Group", "health_problems": "Health problems", "home": "Home", "hour": "Hour", "amount_total": "Amount total", "amount_used": "Amount used", "amount_available": "Amount available", "id": "ID", "imagerie": "Imagerie", "imagerie_group": "Imagerie group", "imagerie_list": "Imagerie list", "injection": "Injection", "input_data": "Input data", "inventory": "Inventory", "invoice": "Invoice", "invoice_daily": "Invoice daily", "invoice_no": "Invoice_no", "ipd": "IPD", "is_count_stock": "Count stock", "items": "Items", "items_goods": "Items", "lab_group": "Lab group", "laboratory": "Laboratory", "language": "Khmer", "list_category": "List category", "exspire_list": "Exspire list", "drug_list": "Drug list", "list_medicine": "Medicine list", "list_products": "List products", "loading": "Loading", "loading_": "Loading...", "male": "Male", "maxi": "<PERSON><PERSON>", "medicine": "Medicine", "pharmacy": "Pharmacy", "medicine_type": "Medicine Type", "mini": "Mini", "more_fill": "More fill", "morning": "Morning", "mouth": "Mouth", "n": "N", "name": "Name", "name_khmer": "Name khmer", "name_latin": "Name latin", "new_doc": "New Docs", "new_invoice": "New invoice", "new_medicine": "New medicine", "new_medicine_type": "New medicine type", "new_product": "New product", "new_purchase": "New purchase", "new_supplier": "New supplier", "new_world": "New word", "night": "Night", "no": "No", "none_data": "None data", "none": "None", "noon": "<PERSON>on", "not_selected": "Not selected", "not_yet_paid": "Not yet paid", "note": "Note", "nursing_process": "Nursing process", "of": "Of", "opd": "OPD", "or": "Or", "original_price": "Original price", "other": "Other", "page": "Page", "paid": "Paid", "para_unit": "Para unit", "parameter": "Parameter", "password": "Password", "history": "History", "patient_info": "Patient info", "past_history": "Past history", "patient": "Patient", "patient_name": "Patinet name", "patients": "Patients", "patients_emr": "Patients EMR", "patients_ipd": "Patients IPD", "patients_opd": "Patients OPD", "pay": "Pay", "payment": "Payment", "payment_by_bank": "Payment by bank", "payment_history": "Payment history", "physical": "Physical", "physical_exam": "Physical-Exam", "picture": "Picture", "pm": "pm", "pos": "POS", "prescription": "Prescription", "presrciption": "Presrciption", "previous_debt": "Previous debt", "price": "Price", "price_for": "Price for", "processing": "Processing", "product_group": "Product group", "product_name": "Product name", "productgroup": "Product group", "products": "Products", "province": "Province", "purchase": "Purchase", "purchase_date": "Purchase date", "purchase_list": "Purchase list", "purchase_qty": "Purchase qty", "qty": "Qty", "qty_in": "Qty in", "range_normal": "Range normal", "recorder": "Recorder", "references": "References", "report": "Report", "request_check": "Request check", "restore": "Rest<PERSON>", "result": "Result", "return_money": "Return", "role": "Role", "room": "Room", "sale_report": "Sale report", "sale_reprot": "Sale report", "sales_price": "Sales price", "sample": "<PERSON><PERSON>", "save": "Save", "designation": "Designation", "schedule": "Schedule", "search": "Search...", "select": "Select", "select_new_unit": "Select new unit", "seller": "<PERSON><PERSON>", "send_to_payment": "Send to payment", "sended_to_payment": "Send to payment", "service": "Service", "settup": "Setup", "sign": "Sign", "signature_physician": "Physician's Signature", "specific": "Specialist", "staff": "Staff", "staff_type": "Staff type", "start": "Start", "status": "Status", "stock": "Stock", "sub_total": "Sub total", "supplier": "Supplier", "symptoms": "Symptoms", "tax": "Tax", "telephone": "Telephone", "template": "Template", "template_imagerie": "Template imagerie", "time": "Time", "time_1": "1st time", "time_10": "10th time", "time_2": "2nd time", "time_3": "3rd time", "time_4": "4th time", "time_5": "5th time", "time_6": "6th time", "time_7": "7th time", "time_8": "8th time", "time_9": "9th time", "time_to_use": "Time to use", "times": "Times", "times_": "Times", "title": "Title", "to_date": "To", "total": "Total", "total_after_disc": "After discount", "total_all": "Total all", "total_daily": "Total daily", "transfer_to_ipd": "Transfer to IPD", "treatment": "Treatment", "type": "Type", "type_diagnosis": "Type diagnosis", "unit": "Unit", "update": "Update", "use": "Use", "user": "User", "username": "Username", "vaccine": "Vaccine", "vaccine_list": "Vaccine list", "vaccine_type": "Vaccine type", "view": "View", "view_per_page": "View per page", "village": "Vallage", "visit": "Visit", "visit_type": "Visit type", "vital_sign": "Vital sign", "ward": "Ward", "words": "Words", "year": "Year", "month": "Month", "yes": "Yes", "your_location": "From your location"}