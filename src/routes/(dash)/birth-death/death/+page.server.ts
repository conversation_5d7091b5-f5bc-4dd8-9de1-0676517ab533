import { db } from '$lib/server/db';
import { and, like, eq, or, inArray } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { document, fields, patient, progressNote } from '$lib/server/schemas';
import { betweenHelper, pagination } from '$lib/server/utils';
export const load = (async ({ url }) => {
	const q = url.searchParams.get('q') ?? '';
	const patient_id = Number(url.searchParams.get('patient_id'));
	const get_patients = await db.query.patient.findMany({
		where: or(
			like(patient.name_latin, `%${q}%`),
			like(patient.name_khmer, `%${q}%`),
			like(patient.telephone, `%${q}%`)
		),
		limit: 200
	});
	const get_progress_notes = await db.query.progressNote.findMany({
		where: eq(progressNote.patient_id, patient_id),
		columns: {
			id: true
		}
	});
	const get_fields = await db.query.fields.findMany({
		where: and(like(fields.result, '%death%')),
		columns: {
			document_id: true
		}
	});
	const get_documents = await db.query.document.findMany({
		where: and(
			eq(document.title, 'accept_leaving'),
			inArray(
				document.id,
				get_fields.map((item) => Number(item.document_id))
			),
			patient_id
				? inArray(
						document.progress_note_id,
						get_progress_notes.map((item) => item.id)
					)
				: undefined,
			betweenHelper(url, document.datetime)
		),
		with: {
			progressNote: {
				with: {
					patient: true
				}
			},
			fields: true
		},
		...pagination(url)
	});
	const count = await db.$count(
		document,
		and(
			eq(document.title, 'accept_leaving'),
			inArray(
				document.id,
				get_fields.map((item) => Number(item.document_id))
			),
			patient_id
				? inArray(
						document.progress_note_id,
						get_progress_notes.map((item) => item.id)
					)
				: undefined,
			betweenHelper(url, document.datetime)
		)
	);
	return {
		get_documents,
		items: count,
		get_patients
	};
}) satisfies PageServerLoad;
