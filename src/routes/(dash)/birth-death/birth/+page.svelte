<script lang="ts">
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import DdmmyyyyFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import { getValueField } from '$lib/helper';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_documents, items, get_patients } = $derived(data);

	let total_male = $derived(
		get_documents.filter((e) => {
			if (
				getValueField(e.fields, 'child_gender').toLowerCase() === 'male' ||
				getValueField(e.fields, 'child_gender') === 'ប្រុស'
			) {
				return e;
			}
		})
	);
	let n = $state(1);
</script>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('birth_report')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/patient/all" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-restroom"></i>
					{locale.T('patient')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/patient/all" class="btn btn-link p-0 text-secondary">
					<i class="fa-regular fa-file-lines"></i>
					{locale.T('birth')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<div class="row gap-1 g-0">
			<div class="col-11">
				<HeaderQuery>
					<div class="col-sm-2">
						<div class="input-group">
							<span class="input-group-text">{locale.T('start')}</span>
							<input type="date" name="start" class="form-control" />
						</div>
					</div>
					<div class="col-sm-2">
						<div class="input-group">
							<span class="input-group-text">{locale.T('end')}</span>
							<input type="date" name="end" class="form-control" />
						</div>
					</div>
					<div class="col-sm-3">
						<SelectParam
							q_name="q"
							placeholder={locale.T('patient')}
							name="patient_id"
							items={get_patients.map((e) => ({
								id: e.id,
								name: e.name_khmer?.concat(` ${e.name_latin}`)
							}))}
						/>
					</div>
				</HeaderQuery>
			</div>
		</div>
	</div>
	<div style="height: {store.inerHight};" class="card-body table-responsive p-0">
		<table class="table table-hover table-bordered table-light">
			<thead class="sticky-top bg-light table-active">
				<tr class="text-center">
					<th class="text-center">{locale.T('n')}</th>
					<th class="text-center">{locale.T('date')}</th>
					<th class="text-center">{locale.T('id')}</th>
					<th>{locale.T('child_name')}</th>
					<th>{locale.T('child_dob')}</th>
					<th>{locale.T('child_gender')}</th>
					<th>{locale.T('father_name')}</th>
					<th>{locale.T('mother_name')}</th>
					<th>{locale.T('delivery_type')}</th>
				</tr>
			</thead>
			<tbody>
				{#each get_documents as item, index (item.id)}
					{@const child_name = getValueField(item.fields, 'child_name')}
					{@const child_dob = getValueField(item.fields, 'child_dob')}
					{@const child_gender = getValueField(item.fields, 'child_gender').toLowerCase()}
					{@const child_delivery_difficulty = getValueField(item.fields, 'difficulty')}
					{@const child_delivery_section = getValueField(item.fields, 'section')}
					{@const child_delivery_normal = getValueField(item.fields, 'normal')}
					{@const father_name = getValueField(item.fields, 'father_name')}
					<tr class="text-center">
						<td>
							{index + n}
						</td>
						<td>
							<DdmmyyyyFormat style="date" date={item.datetime} />
						</td>
						<td>
							DOC{item.id}
							<br />
							PT{item.progressNote?.patient_id}
						</td>
						<td>
							{child_name}
						</td>
						<td>
							<DdmmyyyyFormat style="date" date={child_dob} />
						</td>
						<td>
							{child_gender === 'male' || child_gender === 'ប្រុស'
								? locale.T('male')
								: child_gender === 'female' || child_gender === 'ស្រី'
									? locale.T('female')
									: child_gender}
						</td>
						<td>
							{father_name}
						</td>
						<td>
							{item.progressNote?.patient?.name_khmer}
						</td>
						<td>
							<a target="_blank" href="/print/{item.progress_note_id}/document?title={item.title}">
								{#if child_delivery_difficulty}
									{locale.T('delivery_difficulty')}
								{:else if child_delivery_section}
									{locale.T('delivery_section')}
								{:else if child_delivery_normal}
									{locale.T('delivery_normal')}
								{/if}</a
							>
						</td>
					</tr>
				{/each}
				<tr class="table-success">
					<td colspan="9" class="text-center">
						{locale.T('total')}: {get_documents.length}
						{locale.T('people')},
						{locale.T('male')}: {total_male.length}
						{locale.T('female')}: {get_documents.length - total_male.length}
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations {items} bind:n />
	</div>
</div>
