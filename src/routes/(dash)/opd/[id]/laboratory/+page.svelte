<script lang="ts">
	import Currency from '$lib/coms/Currency.svelte';
	import type { PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import ConfirmModal from '$lib/coms-form/ConfirmModal.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_laboratory_group, get_visit, get_currency } = $derived(data);
	let total_laboratory = $derived(
		get_visit?.billing?.charge?.find((e) => e.charge_on === 'laboratory')?.price || 0
	);
	let product_id: number | null = $state(null);
	let product_name: string = $state('');
	let laboratory_request_id: number | null = $state(null);
</script>

<DeleteModal action="?/delete_laboratory_request" bind:id={laboratory_request_id} />
<ConfirmModal action="?/create_laboratory_request" id={product_id}>
	<ul class="list-group pb-2">
		<li class="list-group-item">
			<i class="fa-solid fa-flask"></i>
			{product_name}
		</li>
	</ul>
	<input type="hidden" name="product_id" value={product_id} />
</ConfirmModal>
<div class="row pb-2">
	<div class="col">
		<hr />
	</div>
	<div class="col-auto">
		<a
			target="_blank"
			aria-label="nersing_process"
			href="/print/{get_visit?.id}/laboratory-req"
			class="btn btn-success btn-sm float-end"
			><i class="fa-solid fa-print"></i>
		</a>
	</div>
</div>
<fieldset disabled={get_visit?.billing?.status !== 'checking'}>
	<div class="row">
		{#each get_laboratory_group as item (item.id)}
			<div class="col-md-3 pb-2">
				<div class="card bg-light h-100">
					<div class="card-header fs-5 text-center">
						<span>{item.laboratory_group}</span>
					</div>
					<div class="card-body">
						{#each item.product as iitem (iitem.id)}
							{@const is_already = get_visit?.laboratoryRequest.some(
								(e) => e.product_id === iitem.id
							)}
							{@const get_lab_req = get_visit?.laboratoryRequest.find(
								(e) => e.product_id === iitem.id
							)}

							<div class="btn-group w-100">
								{#if is_already}
									<!-- <a
										aria-label="delete"
										href="?/create_imagerie_request&product_id={iitem.id}"
										class="alert alert-danger px-2 me-1 py-1"
									>
										<i class="fa-regular fa-file"></i>
									</a> -->
									<button
										aria-label="delete"
										data-bs-toggle="modal"
										data-bs-target="#delete_modal"
										type="button"
										onclick={() => {
											laboratory_request_id = Number(get_lab_req?.id);
										}}
										class="alert alert-danger px-2 me-1 py-1"
									>
										<i class="fa-solid fa-trash-can"></i>
									</button>
								{/if}
								<button
									type="button"
									onclick={() => {
										product_id = iitem.id;
										product_name = iitem.products;
									}}
									data-bs-toggle="modal"
									data-bs-target={!is_already && '#confirm_modal'}
									class="alert alert-primary px-2 py-1 text-start w-100"
									class:alert-danger={is_already}
								>
									<span>
										{iitem.products}
									</span>
									<span class="text-end float-end">
										<Currency amount={iitem.price} symbol={get_currency?.currency} />
									</span>
								</button>
							</div>
						{/each}
					</div>
				</div>
			</div>
		{/each}
	</div>

	<div class="card-footer row p-2 bg-light sticky-bottom">
		<div class="col text-end">
			<button type="button" class="btn btn-warning"
				>Total Laboratory <Currency
					class="fs-6"
					symbol={get_currency?.currency}
					amount={total_laboratory}
				/></button
			>
		</div>
	</div>
</fieldset>
