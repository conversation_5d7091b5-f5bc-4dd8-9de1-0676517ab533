<script lang="ts">
	import Currency from '$lib/coms/Currency.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	import ConfirmModal from '$lib/coms-form/ConfirmModal.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_visit, get_currency, get_vaccine_groups } = $derived(data);
	let total_vaccine_service = $derived(
		data.get_visit?.billing?.charge.find((e) => e.charge_on === 'vaccine')?.price || 0
	);
	let product_id: number | null = $state(null);
	let product_name: string = $state('');
	let vaccine_id: number | null = $state(null);
</script>

<DeleteModal action="?/delete_vaccine" bind:id={vaccine_id} />
<ConfirmModal action="?/create_vaccine" id={product_id}>
	<ul class="list-group pb-2">
		<li class="list-group-item">
			<i class="fa-solid fa-crutch"></i>
			{product_name}
		</li>
	</ul>
	<input type="hidden" name="product_id" value={product_id} />
</ConfirmModal>

<fieldset disabled={get_visit?.billing?.status !== 'checking'}>
	<div class="row">
		{#each get_vaccine_groups || [] as item (item.id)}
			{@const products = item.product}
			<div class="col-md-3 pb-2">
				<div class="card bg-light h-100">
					<div class="card-header fs-5 text-center">
						<span>{item?.name}</span>
					</div>
					<div class="card-body">
						{#each products || [] as iitem (iitem.id)}
							{@const is_already = get_visit?.vaccine.some((e) => e.product_id === iitem.id)}
							{@const get_vaccine = get_visit?.vaccine.find((e) => e.product_id === iitem.id)}
							<div class="btn-group w-100">
								{#if is_already}
									<!-- <a
											aria-label="delete"
											href="?/create_imagerie_request&product_id={iitem.id}"
											class="alert alert-danger px-2 me-1 py-1"
										>
											<i class="fa-regular fa-file"></i>
										</a> -->
									<button
										aria-label="delete"
										data-bs-toggle="modal"
										data-bs-target="#delete_modal"
										type="button"
										onclick={() => {
											vaccine_id = Number(get_vaccine?.id);
										}}
										class="alert alert-danger px-2 me-1 py-1"
									>
										<i class="fa-solid fa-trash-can"></i>
									</button>
								{/if}
								<button
									type="button"
									onclick={() => {
										product_id = iitem.id;
										product_name = iitem.products;
										vaccine_id = Number(get_vaccine?.id);
									}}
									data-bs-toggle="modal"
									data-bs-target={!is_already && '#confirm_modal'}
									class="alert alert-primary px-2 py-1 text-start w-100"
									class:alert-danger={is_already}
								>
									<span>
										{iitem.products}
									</span>
									<span class="text-end float-end">
										<Currency amount={iitem.price} symbol={get_currency?.currency} />
									</span>
								</button>
							</div>
						{/each}
					</div>
				</div>
			</div>
		{/each}
		<div class="card-footer row bg-light p-2 sticky-bottom">
			<div class="col text-end">
				<button type="button" class="btn btn-warning"
					>{locale.T('total')}
					<Currency
						class="fs-6"
						symbol={get_currency?.currency}
						amount={total_vaccine_service}
					/></button
				>
			</div>
		</div>
	</div>
</fieldset>
