import { db } from '$lib/server/db';
import { group, imagerieRequest, product, visit } from '$lib/server/schemas';
import type { Actions, PageServerLoad } from './$types';
import { asc, eq } from 'drizzle-orm';
import { createProductOrder, deleteProductOrder } from '$lib/server/models';
import logError from '$lib/server/utils/logError';
import { YYYYMMDD_Format } from '$lib/server/utils';
import { fail } from '@sveltejs/kit';

export const load = (async ({ params }) => {
	const get_currency = await db.query.currency.findFirst({});
	const get_imageerie_groups = await db.query.group.findMany({
		where: eq(group.category_id, 2),
		with: {
			product: {
				orderBy: asc(product.products)
			}
		}
	});
	const get_visit = await db.query.visit.findFirst({
		with: {
			imagerieRequest: {
				with: {
					product: true
				}
			},
			billing: {
				with: {
					charge: {
						with: {
							productOrder: true
						}
					}
				}
			}
		},
		where: eq(visit.id, +params.id)
	});

	return {
		get_imageerie_groups,
		get_visit,
		get_currency
	};
}) satisfies PageServerLoad;
export const actions: Actions = {
	create_imagerie_request: async ({ request, params, url }) => {
		const visit_id = parseInt(params.id);
		const body = await request.formData();
		const { note, product_id } = Object.fromEntries(body) as Record<string, string>;
		if (!product_id || isNaN(+product_id)) return fail(400, { errId: true });
		const get_visit = await db.query.visit.findFirst({
			where: eq(visit.id, visit_id),
			with: {
				imagerieRequest: {
					with: {
						product: true
					}
				},
				billing: {
					with: {
						charge: {
							with: {
								productOrder: true
							}
						}
					}
				}
			}
		});
		// Create Imagerie Request
		const charge_on_imagerie = get_visit?.billing?.charge.find((e) => e.charge_on === 'imagerie');
		const is_created = get_visit?.imagerieRequest.some((ee) => ee.product_id === +product_id);
		const find_imagerie_request = get_visit?.imagerieRequest.find(
			(e) => e.product_id === +product_id
		);
		if (!is_created) {
			const get_product = await db.query.product.findFirst({
				where: eq(product.id, +product_id)
			});
			await db
				.insert(imagerieRequest)
				.values({
					product_id: +product_id,
					visit_id: get_visit?.id,
					note: note,
					patient_id: get_visit?.patient_id,
					request_datetime: YYYYMMDD_Format.datetime(new Date())
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
			await createProductOrder({
				charge_id: Number(charge_on_imagerie?.id),
				price: Number(get_product?.price),
				product_id: Number(get_product?.id),
				qty: 1,
				body: body,
				url: url
			});
		}

		// Delete Imagere Request
		if (is_created && find_imagerie_request?.id) {
			await db
				.update(imagerieRequest)
				.set({
					note: note
				})
				.where(eq(imagerieRequest.id, +find_imagerie_request?.id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	},
	delete_imagerie_request: async ({ request, params, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		const visit_id = parseInt(params.id);
		if (!id) return fail(400, { errId: true });
		const get_visit = await db.query.visit.findFirst({
			where: eq(visit.id, visit_id),
			with: {
				imagerieRequest: {
					with: {
						product: true
					}
				},
				billing: {
					with: {
						charge: {
							with: {
								productOrder: true
							}
						}
					}
				}
			}
		});

		// Create Imagerie Request
		const get_imageerie_request = await db.query.imagerieRequest.findFirst({
			where: eq(imagerieRequest.id, +id)
		});
		const charge_on_imagerie = get_visit?.billing?.charge.find((e) => e.charge_on === 'imagerie');
		const product_order_ = charge_on_imagerie?.productOrder.find(
			(ee) => ee.product_id === Number(get_imageerie_request?.product_id)
		);
		if (!get_imageerie_request || !product_order_?.id) return fail(400, { errId: true });
		await db
			.delete(imagerieRequest)
			.where(eq(imagerieRequest.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await deleteProductOrder(Number(product_order_?.id));
	}
};
