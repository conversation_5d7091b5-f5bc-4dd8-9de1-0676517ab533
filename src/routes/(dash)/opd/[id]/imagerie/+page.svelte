<script lang="ts">
	import ConfirmModal from '$lib/coms-form/ConfirmModal.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_imageerie_groups, get_visit, get_currency } = $derived(data);
	let total_imagerie = $derived(
		get_visit?.billing?.charge.find((e) => e.charge_on === 'imagerie')?.price
	);
	let product_id: number | null = $state(null);
	let product_name: string = $state('');
	let imagerie_request_id: number | null = $state(null);
	let get_imagerie_request = $derived(get_visit?.imagerieRequest);
	let find_imagerie_request = $derived(
		get_imagerie_request?.find((e) => e.id === imagerie_request_id)
	);
</script>

<DeleteModal action="?/delete_imagerie_request" bind:id={imagerie_request_id} />
<ConfirmModal action="?/create_imagerie_request" id={product_id}>
	<ul class="list-group pb-2">
		<li class="list-group-item">
			<i class="fa-solid fa-camera"></i>
			{product_name}
		</li>
	</ul>
	<label for="note">{locale.T('note')}</label>
	<textarea value={find_imagerie_request?.note ?? ''} name="note" class="form-control" id=""
	></textarea>
	<input type="hidden" name="product_id" value={product_id} />
</ConfirmModal>
<div class="row pb-2">
	<div class="col">
		<hr />
	</div>
	<div class="col-auto">
		<a
			target="_blank"
			aria-label="nersing_process"
			href="/print/{get_visit?.id}/imagrie-req"
			class="btn btn-success btn-sm float-end"
			><i class="fa-solid fa-print"></i>
		</a>
	</div>
</div>
<fieldset disabled={get_visit?.billing?.status !== 'checking'}>
	<div class="row">
		{#each get_imageerie_groups as item}
			{@const products = item.product}
			<div class="col-md-3 pb-2">
				<div class="card bg-light h-100">
					<div class="card-header fs-5 text-center">
						<span>{item.name}</span>
					</div>
					<div class="card-body">
						{#each products as iitem (iitem.id)}
							{@const is_already = get_visit?.imagerieRequest.some(
								(e) => e.product_id === iitem.id
							)}
							{@const get_img_req = get_visit?.imagerieRequest.find(
								(e) => e.product_id === iitem.id
							)}
							<div class="btn-group w-100">
								{#if is_already}
									<!-- <a
										aria-label="delete"
										href="?/create_imagerie_request&product_id={iitem.id}"
										class="alert alert-danger px-2 me-1 py-1"
									>
										<i class="fa-regular fa-file"></i>
									</a> -->
									<button
										aria-label="delete"
										data-bs-toggle="modal"
										data-bs-target="#delete_modal"
										type="button"
										onclick={() => {
											imagerie_request_id = Number(get_img_req?.id);
										}}
										class="alert alert-danger px-2 me-1 py-1"
									>
										<i class="fa-solid fa-trash-can"></i>
									</button>
								{/if}
								<button
									type="button"
									onclick={() => {
										product_id = iitem.id;
										product_name = iitem.products;
										imagerie_request_id = Number(get_img_req?.id);
									}}
									data-bs-toggle="modal"
									data-bs-target="#confirm_modal"
									class="alert alert-primary px-2 py-1 text-start w-100"
									class:alert-danger={is_already}
								>
									<span>
										{iitem.products}
									</span>
									<span class="text-end float-end">
										<Currency amount={iitem.price} symbol={get_currency?.currency} />
									</span>
								</button>
							</div>
						{/each}
					</div>
				</div>
			</div>
		{/each}
		<div class="card-footer row bg-light p-2 sticky-bottom">
			<div class="col text-end">
				<button type="button" class="btn btn-warning"
					>Total Imagerie
					<Currency class="fs-6" amount={total_imagerie || 0} symbol={get_currency?.currency} />
				</button>
			</div>
		</div>
	</div>
</fieldset>
