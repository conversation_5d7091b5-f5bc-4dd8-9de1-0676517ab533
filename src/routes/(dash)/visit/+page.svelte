<script lang="ts">
	import type { PageServerData } from './$types';
	import { locale } from '$lib/translations/locales.svelte';
	import { page } from '$app/state';
	import Form from '$lib/coms-form/Form.svelte';
	import Words from '$lib/coms-cu/Words.svelte';
	import AddBedToIpd from '$lib/coms-ipd-opd/AddBedToIPD.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_patient, get_departments, get_progress_notes } = $derived(data);
	let visit = $state(data.get_visit);
	let get_progress_note = $state(data.get_progress_note);
	let get_beds = $state(data.get_beds);
	let get_wards = $state(data.get_wards);
	let get_words_objective = $state(data.get_words_objective);
	let visit_id = $derived(page.url.searchParams.get('visit_id'));
	let loading = $state(false);
	let pulse = $state(visit?.vitalSign?.pulse || '');
	let t = $state(visit?.vitalSign?.t || '');
	let sp02 = $state(visit?.vitalSign?.sp02 || '');
	let sbp = $state(visit?.vitalSign?.sbp || '');
	let dbp = $state(visit?.vitalSign?.dbp || '');
	let rr = $state(visit?.vitalSign?.rr || '');
	let height: number | null = $derived(null);
	let weight: number | null = $derived(null);
	let height_m_to_cm = $derived((Number(height) / 100) * (Number(height) / 100));
	let appointment_id = $derived(page.url.searchParams.get('appointment_id'));
	let bmi = $state({
		number: visit?.vitalSign?.bmi || 0,
		string: '',
		color: ''
	});

	let find_active_bed = $state(
		data.get_progress_note?.activeBed.find((e) => e.datetime_out === null)
	);
	let bed_id: number | null = $state(find_active_bed?.bed_id ?? null);
	let find_bed = $derived(get_beds?.find((e) => e.id === bed_id));
	let department_id: number | null = $state(null);
	let etiology = $state(visit?.etiology || get_progress_note?.etiology || '');
	$effect(() => {
		if (height !== null && weight !== null) {
			if (weight / height_m_to_cm < 16) {
				bmi.string = 'Severely Underweight';
				bmi.color = '#F9E61A';
			} else if (weight / height_m_to_cm > 16.0 && weight / height_m_to_cm < 18.4) {
				bmi.string = 'Underweight';
				bmi.color = '#E4D10C';
			} else if (weight / height_m_to_cm > 18.5 && weight / height_m_to_cm < 24.9) {
				bmi.string = 'Normal';
				bmi.color = '#06C506';
			} else if (weight / height_m_to_cm > 25.0 && weight / height_m_to_cm < 29.9) {
				bmi.string = 'Overweight';
				bmi.color = '#C7760C';
			} else if (weight / height_m_to_cm > 30.0 && weight / height_m_to_cm < 34.9) {
				bmi.string = 'Moderately Obesity';
				bmi.color = '#D44A10';
			} else if (weight / height_m_to_cm > 35.0 && weight / height_m_to_cm < 50) {
				bmi.string = 'Severly Obesity';
				bmi.color = '#EC0404';
			} else {
				if (height && weight) {
					bmi.string = 'Severly Obesity';
					bmi.color = '#EC0404';
				}
			}
			bmi.number = weight / height_m_to_cm;
		}
	});
</script>

<AddBedToIpd {department_id} bind:bed_id data={{ get_progress_notes, get_wards }} />
<Words
	category="subjective"
	name="Symptoms"
	bind:value={etiology}
	words={get_words_objective.filter((e) => e.type === 'etiology')}
	modal_name_type="etiology"
/>
<Form
	method="post"
	action={page.url.searchParams.get('visit_type') === 'ipd'
		? '?/create_visit_ipd'
		: '?/create_visit_opd'}
	bind:loading
>
	<input type="hidden" name="appointment_id" value={appointment_id || ''} />
	<input value={get_patient?.id} type="hidden" name="patient_id" />
	<input value={visit_id} type="hidden" name="visit_id" />
	<input type="hidden" name="bmi" value={bmi?.number.toFixed(1) || ''} />
	{#if page.url.searchParams.get('visit_type') === 'ipd'}
		<div class="alert border border-primary border-2">
			{#if get_progress_note}
				<input value={get_progress_note?.id ?? ''} type="hidden" name="progress_note_id" />
			{/if}
			<input value={bed_id} type="hidden" name="bed_id" />
			<input value={find_bed?.room?.product_id} type="hidden" name="product_id" />
			<input value={find_bed?.room?.product?.price} type="hidden" name="price" />
			<input value={get_patient?.id} type="hidden" name="patient_id" />
			<input value={visit?.id ?? ''} type="hidden" name="visit_id" />
			<input value={visit?.billing?.id ?? ''} type="hidden" name="billing_id" />
			<div class="card-body">
				<div class=" row pb-3">
					<div class="col-sm-3">
						<button
							data-bs-toggle="modal"
							data-bs-target="#etiology"
							type="button"
							class="btn btn-outline-primary btn-sm">{locale.T('symptoms')}</button
						>
					</div>
					<div class="col-sm-9">
						<input
							bind:value={etiology}
							name="etiology"
							type="text"
							class="form-control"
							id="etiology"
						/>
					</div>
				</div>
				<div class=" row pb-3">
					<label for="department_id" class="col-sm-3 col-form-label"
						>{locale.T('department')}
					</label>
					<div class="col-sm-9">
						<SelectParam
							bind:value={department_id}
							name="department_id"
							items={get_departments.map((e) => ({ id: e.id, name: e.products }))}
						/>
					</div>
				</div>
				<div class=" row pb-3">
					<label for="ward_id" class="col-sm-3 col-form-label"
						>{locale.T('ward')}-{locale.T('room')}-{locale.T('bed')}</label
					>
					<div class="col-sm-9">
						<button
							disabled={!department_id}
							type="button"
							data-bs-toggle="modal"
							data-bs-target="#add_bed_ipd"
							class="form-control text-start"
							>{#if find_bed}
								{find_bed?.ward?.ward},
								{find_bed?.room?.room}
								{find_bed?.room?.product?.products},
								{find_bed?.bed}
							{:else}
								{locale.T('select')}
							{/if}
						</button>
					</div>
				</div>
			</div>
		</div>
	{/if}
	{#if page.url.searchParams.get('visit_type') === 'opd'}
		<div class="alert border border-primary border-2">
			<div class=" row pb-3">
				<div class="col-sm-3">
					<button
						data-bs-toggle="modal"
						data-bs-target="#etiology"
						type="button"
						class="btn btn-outline-primary btn-sm">{locale.T('symptoms')}</button
					>
				</div>
				<div class="col-sm-9">
					<input
						bind:value={etiology}
						name="etiology"
						type="text"
						class="form-control"
						id="etiology"
					/>
				</div>
			</div>
			<div class=" row pb-3">
				<div class="col-sm-3">
					<label for="ddepartment_idoctor" class="">{locale.T('department')}</label>
				</div>

				<div class="col-sm-9">
					<SelectParam
						name="department_id"
						bind:value={department_id}
						items={get_departments.map((e) => ({ id: e.id, name: e.products }))}
					/>
				</div>
			</div>
		</div>
	{/if}
	<div class="alert border border-primary border-2 mt-4">
		<span
			class="position-absolute top-0 fs-6 start-50 translate-middle badge rounded-pill bg-primary"
		>
			{locale.T('vital_sign')}
			<span class="visually-hidden">unread messages</span>
		</span>
		<div class=" row pb-3 mt-2">
			<label for="bp" class="col-sm-3 col-form-label">BP (mmHg)</label>
			<div class="col-sm-9">
				<div class="row">
					<div class="col-sm-6">
						<div class="input-group">
							<input
								bind:value={sbp}
								id="sbp"
								placeholder="Systolic"
								name="sbp"
								type="number"
								step="any"
								class="form-control"
							/>
						</div>
					</div>
					<div class="col-sm-6">
						<div class="input-group">
							<input
								bind:value={dbp}
								id="dbp"
								placeholder="Diastolic"
								name="dbp"
								type="number"
								step="any"
								class="form-control"
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class=" row pb-3">
			<label for="pulse" class="col-sm-3 col-form-label">Pulse (min)</label>
			<div class="col-sm-9">
				<div class="input-group">
					<input
						bind:value={pulse}
						id="pulse"
						name="pulse"
						type="number"
						step="any"
						class="form-control"
					/>
				</div>
			</div>
		</div>
		<div class=" row pb-3">
			<label for="t" class="col-sm-3 col-form-label">T (<sup>o</sup>C)</label>
			<div class="col-sm-9">
				<div class="input-group">
					<input bind:value={t} id="t" name="t" type="number" step="any" class="form-control" />
				</div>
			</div>
		</div>
		<div class=" row pb-3">
			<label for="rr" class="col-sm-3 col-form-label">RR (min)</label>
			<div class="col-sm-9">
				<div class="input-group">
					<input bind:value={rr} name="rr" type="number" step="any" class="form-control" />
				</div>
			</div>
		</div>
		<div class=" row pb-3">
			<label for="sp02" class="col-sm-3 col-form-label">SpO2 (%)</label>
			<div class="col-sm-9">
				<div class="input-group">
					<input
						bind:value={sp02}
						id="sp02"
						name="sp02"
						type="number"
						step="any"
						class="form-control"
					/>
				</div>
			</div>
		</div>
		<div class=" row pb-3">
			<label for="height" class="col-sm-3 col-form-label">Height (cm) </label>
			<div class="col-sm-9">
				<div class="input-group">
					<input
						id="height"
						bind:value={height}
						name="height"
						type="number"
						step="any"
						class="form-control"
					/>
				</div>
			</div>
		</div>
		<div class=" row pb-3">
			<label for="weight" class="col-sm-3 col-form-label">Weight (kg)</label>
			<div class="col-sm-9">
				<div class="input-group">
					<input
						id="weight"
						bind:value={weight}
						name="weight"
						type="number"
						step="any"
						class="form-control"
					/>
				</div>
			</div>
		</div>
		<div class=" row pb-3">
			<label for="bmi" class="col-sm-3 col-form-label">BMI </label>
			<div class="col-sm-9">
				<div class="row">
					<div class="col-6">
						<div class="input-group">
							<input
								value={bmi.number > 0 ? bmi.number.toFixed(1) : ''}
								disabled
								id="bmi"
								name="bmi"
								type="text"
								class="form-control"
							/>
						</div>
					</div>
					<div class="col-6">
						<div class="input-group">
							<input
								bind:value={bmi.string}
								disabled
								id="bmi_color"
								name="bmi_color"
								type="text"
								class="form-control text-white"
								style="background-color: {bmi.color};"
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="text-end">
			<button type="submit" class="btn btn-primary btn-sm">
				{locale.T('next')} <i class="fa-solid fa-circle-chevron-right"></i></button
			>
		</div>
	</div>
</Form>
