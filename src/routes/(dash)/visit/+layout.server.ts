import { db } from '$lib/server/db';
import { eq } from 'drizzle-orm';
import type { LayoutServerLoad } from './$types';
import { patient } from '$lib/server/schemas';
import { redirect } from '@sveltejs/kit';
import { YYYYMMDD_Format } from '$lib/server/utils';

export const load = (async ({ url }) => {
	const patient_id = url.searchParams.get('patient_id') ?? '';
	const get_patient = await db.query.patient.findFirst({
		where: eq(patient.id, +patient_id),
		with: {
			commune: true,
			district: true,
			provice: true,
			village: true
		}
	});
	let patient_info;
	if (get_patient) {
		patient_info = {
			...get_patient,
			date_checkup: YYYYMMDD_Format.datetime(new Date())
		};
	}
	const visit_type = url.searchParams.get('visit_type')?.toString() ?? '';
	if (!['ipd', 'opd'].includes(visit_type) || !get_patient) redirect(302, '/patient/all');
	return {
		patient_info
	};
}) satisfies LayoutServerLoad;
