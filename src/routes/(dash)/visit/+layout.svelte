<script lang="ts">
	import { page } from '$app/state';
	import PatientInfo from '$lib/coms-ipd-opd/PatientInfo.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { LayoutServerData } from './$types';
	interface Props {
		data: LayoutServerData;
		children?: import('svelte').Snippet;
	}

	let { data, children }: Props = $props();
	let { patient_info } = $derived(data);
	let stepper = $state('');
	$effect(() => {
		if (page.url.pathname.includes('subjective')) {
			stepper = 'two';
		} else if (page.url.pathname.includes('exam')) {
			stepper = 'three';
		} else if (page.url.pathname.includes('payment')) {
			stepper = 'four';
		} else {
			stepper = 'one';
		}
	});
</script>

<div class="row g-0">
	<div class="col-sm-6">
		<a href="/patient/opd" class="btn btn-link m-0 p-0"
			><i class="fa-solid fa-rotate-left"></i> {locale.T('back')}
		</a>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/patient/all" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-restroom"></i>
					{locale.T('patient')}
				</a>
			</li>
			{#if page.url.searchParams.get('visit_type') === 'opd'}
				<li class="breadcrumb-item">
					<a href={'#'} class="btn btn-link p-0 text-secondary">
						<i class=" fas fa-stethoscope"></i>
						{locale.T('opd')}
					</a>
				</li>
			{:else}
				<li class="breadcrumb-item">
					<a href={'#'} class="btn btn-link p-0 text-secondary">
						<i class=" fas fa-stethoscope"></i>
						{locale.T('ipd')}
					</a>
				</li>
			{/if}
		</ol>
	</div>
</div>
<PatientInfo {patient_info} />
<div class="alert fs-5 alert-primary py-2 mt-3">
	<div class="row justify-content-between align-content-center">
		<div class="col-auto text-primary">
			<span class="badge rounded-pill text-bg-primary"
				>1. <i class="fa-solid fa-stethoscope"></i></span
			>
			{locale.T('symptoms')} / {locale.T('vital_sign')}
		</div>
		<div
			class={stepper === 'two' || stepper === 'three' || stepper === 'four'
				? 'col-auto text-primary'
				: 'col-auto text-secondary'}
		>
			<span
				class={stepper === 'two' || stepper === 'three' || stepper === 'four'
					? 'badge rounded-pill text-bg-primary'
					: 'badge rounded-pill text-bg-secondary'}
				>2. <i class="fa-solid fa-briefcase-medical"></i></span
			> Subjective
		</div>

		<div
			class={stepper === 'three' || stepper === 'four'
				? 'col-auto text-primary'
				: 'col-auto text-secondary'}
		>
			<span
				class={stepper === 'three' || stepper === 'four'
					? 'badge rounded-pill text-bg-primary'
					: 'badge rounded-pill text-bg-secondary'}>3. <i class="fa-solid fa-user-doctor"></i></span
			>
			{locale.T('physical_exam')}
		</div>
		<div class={stepper === 'four' ? 'col-auto text-primary' : 'col-auto text-secondary'}>
			<span
				class={stepper === 'four'
					? 'badge rounded-pill text-bg-primary'
					: 'badge rounded-pill text-bg-secondary'}>4. <i class="fa-solid fa-wallet"></i></span
			>
			{locale.T('payment')}
		</div>
	</div>
</div>

{@render children?.()}
<br />
