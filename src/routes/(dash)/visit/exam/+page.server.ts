import { db } from '$lib/server/db';
import { visit, words } from '$lib/server/schemas';
import type { PageServerLoad } from './$types';
import { eq } from 'drizzle-orm';

export const load = (async ({ url, parent }) => {
	await parent();
	const visit_id = url.searchParams.get('visit_id');
	const get_physicals = await db.query.physical.findMany();
	const get_exams = await db.query.exam.findMany({
		with: {
			physical: true
		}
	});
	const get_visit = await db.query.visit.findFirst({
		where: eq(visit.id, Number(visit_id)),
		with: {
			vitalSign: true,
			physicalExam: true,
			billing: {
				columns: {
					id: true
				}
			}
		}
	});

	const get_words_objective = await db.query.words.findMany({
		where: eq(words.category, 'objective')
	});

	return {
		get_exams,
		get_physicals,
		get_visit,
		get_words_objective
	};
}) satisfies PageServerLoad;
