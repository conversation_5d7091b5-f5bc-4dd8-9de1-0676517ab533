<script lang="ts">
	import type { PageServerData } from './$types';
	import { locale } from '$lib/translations/locales.svelte';
	import Examinations from '$lib/coms-form/Examinations.svelte';
	import { page } from '$app/state';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_exams, get_physicals, get_visit, get_words_objective } = $derived(data);
	let visit_id = $derived(page.url.searchParams.get('visit_id'));
</script>

<div class="alert border border-primary border-2">
	<Examinations
		action="/opd/{visit_id}/objective/?/exam_result"
		data={{ get_exams, get_physicals, get_visit, get_words: get_words_objective }}
	/>
	<div class="text-end">
		<a
			href="/visit/payment{page.url.search}&billing_id={get_visit?.billing?.id}"
			class="btn btn-primary btn-sm"
		>
			{locale.T('next')} <i class="fa-solid fa-circle-chevron-right"></i></a
		>
	</div>
</div>
