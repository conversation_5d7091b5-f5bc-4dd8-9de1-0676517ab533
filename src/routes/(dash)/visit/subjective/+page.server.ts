import { db } from '$lib/server/db';
import { subjective, words } from '$lib/server/schemas';
import { redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { eq } from 'drizzle-orm';
import logError from '$lib/server/utils/logError';
export const load = (async ({ url, parent }) => {
	await parent();
	const visit_id = url.searchParams.get('visit_id');

	const get_words_subjective = await db.query.words.findMany({
		where: eq(words.category, 'subjective')
	});

	const get_sujective = await db.query.subjective.findFirst({
		where: eq(subjective.visit_id, Number(visit_id))
	});
	return {
		get_words_subjective,
		get_sujective
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_subjective: async ({ request, url }) => {
		const body = await request.formData();
		const {
			cheif_complaint,
			history_of_present_illness,
			past_medical_history,
			allesgy_medicine,
			surgical_history,
			current_medication,
			family_and_social_history,
			visit_id,
			pre_diagnosis,
			url: url_,
			subjective_id
		} = Object.fromEntries(body) as Record<string, string>;
		if (
			subjective_id ||
			cheif_complaint ||
			history_of_present_illness ||
			past_medical_history ||
			surgical_history ||
			current_medication ||
			family_and_social_history ||
			pre_diagnosis
		) {
			if (subjective_id) {
				await db
					.update(subjective)
					.set({
						allesgy_medicine: allesgy_medicine ? allesgy_medicine : null,
						cheif_complaint: cheif_complaint ? cheif_complaint : null,
						past_medical_history: past_medical_history ? past_medical_history : null,
						history_of_present_illness: history_of_present_illness
							? history_of_present_illness
							: null,
						surgical_history: surgical_history ? surgical_history : null,
						current_medication: current_medication ? current_medication : null,
						family_and_social_history: family_and_social_history ? family_and_social_history : null,
						pre_diagnosis: pre_diagnosis ? pre_diagnosis : null
					})
					.where(eq(subjective.id, +subjective_id))
					.catch((e) => {
						logError({ url, body, err: e });
					});
			} else {
				await db
					.insert(subjective)
					.values({
						allesgy_medicine: allesgy_medicine ? allesgy_medicine : null,
						cheif_complaint: cheif_complaint ? cheif_complaint : null,
						past_medical_history: past_medical_history ? past_medical_history : null,
						history_of_present_illness: history_of_present_illness
							? history_of_present_illness
							: null,
						surgical_history: surgical_history ? surgical_history : null,
						visit_id: +visit_id,
						current_medication: current_medication ? current_medication : null,
						family_and_social_history: family_and_social_history ? family_and_social_history : null,
						pre_diagnosis: pre_diagnosis ? pre_diagnosis : null
					})
					.catch((e) => {
						logError({ url, body, err: e });
					});
			}
		}
		redirect(303, `/visit/exam${url_}`);
	}
};
