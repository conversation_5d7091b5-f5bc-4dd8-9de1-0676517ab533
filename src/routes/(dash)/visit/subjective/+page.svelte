<script lang="ts">
	import type { PageServerData } from './$types';
	import { locale } from '$lib/translations/locales.svelte';
	import Words from '$lib/coms-cu/Words.svelte';
	import { page } from '$app/state';
	import Form from '$lib/coms-form/Form.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_words_subjective, get_sujective: get_subjective_ } = $derived(data);
	let get_sujective = $state(data.get_sujective);
	let visit_id = $derived(page.url.searchParams.get('visit_id'));
	let loading = $state(false);
	let cheif_complaint = $state(get_sujective?.cheif_complaint || '');
	let history_of_present_illness = $state(get_sujective?.history_of_present_illness || '');
	let current_medication = $state(get_sujective?.current_medication || '');
	let past_medical_history = $state(get_sujective?.past_medical_history || '');
	let allesgy_medicine = $state(get_sujective?.allesgy_medicine || '');
	let surgical_history = $state(get_sujective?.surgical_history || '');
	let family_and_social_history = $state(get_sujective?.family_and_social_history || '');
	let pre_diagnosis = $state(get_sujective?.pre_diagnosis || '');
</script>

<Words
	name="Cheif complaint"
	bind:value={cheif_complaint}
	words={get_words_subjective.filter((e) => e.type === 'cheif_complaint')}
	modal_name_type="cheif_complaint"
	category="subjective"
/>
<Words
	name="History of Present illness"
	bind:value={history_of_present_illness}
	words={get_words_subjective.filter((e) => e.type === 'history_of_present_illness')}
	modal_name_type="history_of_present_illness"
	category="subjective"
/>
<Words
	name="Current Medication"
	bind:value={current_medication}
	words={get_words_subjective.filter((e) => e.type === 'current_medication')}
	modal_name_type="current_medication"
	category="subjective"
/>
<Words
	name="Past medical history"
	bind:value={past_medical_history}
	words={get_words_subjective.filter((e) => e.type === 'past_medical_history')}
	modal_name_type="past_medical_history"
	category="subjective"
/>
<Words
	name="Allergy medicine"
	bind:value={allesgy_medicine}
	words={get_words_subjective.filter((e) => e.type === 'allesgy_medicine')}
	modal_name_type="allesgy_medicine"
	category="subjective"
/>
<Words
	name="Surgical history"
	bind:value={surgical_history}
	words={get_words_subjective.filter((e) => e.type === 'surgical_history')}
	modal_name_type="surgical_history"
	category="subjective"
/>
<Words
	name="Family and social history"
	bind:value={family_and_social_history}
	words={get_words_subjective.filter((e) => e.type === 'family_and_social_history')}
	modal_name_type="family_and_social_history"
	category="subjective"
/>
<Words
	name="pre Diagnosis"
	bind:value={pre_diagnosis}
	words={get_words_subjective.filter((e) => e.type === 'pre_diagnosis')}
	modal_name_type="pre_diagnosis"
	category="subjective"
/>

<Form method="post" action="?/create_subjective" bind:loading>
	<input type="hidden" name="visit_id" value={visit_id || ''} />
	<input type="hidden" name="url" value={page.url.search} />
	<input type="hidden" name="subjective_id" value={get_subjective_?.id ?? ''} />
	<div class="alert border border-primary border-2">
		<div class=" row pb-2 pt-3">
			<div class="col-sm-3">
				<button
					data-bs-toggle="modal"
					data-bs-target="#cheif_complaint"
					type="button"
					class="btn btn-outline-primary btn-sm">Cheif complaint</button
				>
			</div>
			<!-- <label for="cheif_coplaint" class="col-sm-3 col-form-label">Cheif complaint</label> -->
			<div class="col-sm-9">
				<textarea bind:value={cheif_complaint} rows="4" class="form-control" name="cheif_complaint">
				</textarea>
			</div>
		</div>
		<div class=" row pb-2">
			<div class="col-sm-3">
				<button
					data-bs-toggle="modal"
					data-bs-target="#history_of_present_illness"
					type="button"
					class="btn btn-outline-primary btn-sm">History of Present illness</button
				>
			</div>
			<!-- <label for="cheif_coplaint" class="col-sm-3 col-form-label">Cheif complaint</label> -->
			<div class="col-sm-9">
				<textarea
					bind:value={history_of_present_illness}
					rows="4"
					class="form-control"
					name="history_of_present_illness"
					id="history_of_present_illness"
				>
				</textarea>
			</div>
		</div>
		<span class="btn btn-sm btn-info">Past Medicine History</span>
		<hr />
		<div class=" row pb-2">
			<div class="col-sm-3">
				<button
					data-bs-toggle="modal"
					data-bs-target="#current_medication"
					type="button"
					class="btn btn-outline-primary btn-sm">Current Medication</button
				>
			</div>
			<div class="col-sm-9">
				<div class="input-group">
					<input
						bind:value={current_medication}
						id="current_medication"
						name="current_medication"
						type="text"
						class="form-control"
					/>
				</div>
			</div>
		</div>
		<div class=" row pb-2">
			<div class="col-sm-3">
				<button
					data-bs-toggle="modal"
					data-bs-target="#past_medical_history"
					type="button"
					class="btn btn-outline-primary btn-sm">Past medical history</button
				>
			</div>
			<div class="col-sm-9">
				<div class="input-group">
					<input
						bind:value={past_medical_history}
						id="past_medical_history"
						name="past_medical_history"
						type="text"
						class="form-control"
					/>
				</div>
			</div>
		</div>
		<div class="row pb-2">
			<div class="col-sm-3">
				<button
					data-bs-toggle="modal"
					data-bs-target="#allesgy_medicine"
					type="button"
					class="btn btn-outline-primary btn-sm">Allergy medicine</button
				>
			</div>

			<div class="col-sm-9">
				<div class="input-group">
					<input
						bind:value={allesgy_medicine}
						id="allesgy_medicine_"
						name="allesgy_medicine"
						type="text"
						class="form-control"
					/>
				</div>
			</div>
		</div>
		<div class=" row pb-2">
			<div class="col-sm-3">
				<button
					data-bs-toggle="modal"
					data-bs-target="#surgical_history"
					type="button"
					class="btn btn-outline-primary btn-sm">Surgical history</button
				>
			</div>

			<div class="col-sm-9">
				<div class="input-group">
					<input
						bind:value={surgical_history}
						id="surgical_history"
						name="surgical_history"
						type="text"
						class="form-control"
					/>
				</div>
			</div>
		</div>
		<div class=" row pb-2">
			<div class="col-sm-3">
				<button
					data-bs-toggle="modal"
					data-bs-target="#family_and_social_history"
					type="button"
					class="btn btn-outline-primary btn-sm">Family and social history</button
				>
			</div>

			<div class="col-sm-9">
				<div class="input-group">
					<input
						bind:value={family_and_social_history}
						id="family_and_social_history"
						name="family_and_social_history"
						type="text"
						class="form-control"
					/>
				</div>
			</div>
		</div>
		<div class=" row pb-2">
			<div class="col-sm-3">
				<button
					data-bs-toggle="modal"
					data-bs-target="#pre_diagnosis"
					type="button"
					class="btn btn-success btn-sm">Pre-Diagnosis</button
				>
			</div>

			<div class="col-sm-9">
				<div class="input-group">
					<input
						bind:value={pre_diagnosis}
						id="pre_diagnosis"
						name="pre_diagnosis"
						type="text"
						class="form-control"
					/>
				</div>
			</div>
		</div>
		<div class="text-end">
			<button type="submit" class="btn btn-primary btn-sm">
				{locale.T('next')} <i class="fa-solid fa-circle-chevron-right"></i></button
			>
		</div>
	</div>
</Form>
