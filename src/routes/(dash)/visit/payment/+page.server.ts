import { db } from '$lib/server/db';
import { billing, paymentService } from '$lib/server/schemas';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import logError from '$lib/server/utils/logError';
import { fileHandle } from '$lib/server/upload';
import { eq } from 'drizzle-orm';

export const load = (async ({ url, parent }) => {
	await parent();
	const get_service_types = await db.query.serviceType.findMany({});
	const billing_id = url.searchParams.get('billing_id') ?? '';
	return {
		billing_id,
		get_service_types
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_payment_service: async ({ request, url }) => {
		const body = await request.formData();
		const { code, billing_id, service_type_id } = Object.fromEntries(body) as Record<
			string,
			string
		>;
		console.log(body);
		if (!billing_id || !service_type_id) return fail(400, { errId: true });
		const file = body.get('file') as File;
		const create_payment_service: { id: number }[] = await db
			.insert(paymentService)
			.values({
				code: code ?? '',
				status: 'debt',
				service_type_id: +service_type_id,
				billing_id: +billing_id
			})
			.$returningId()
			.catch((e) => {
				logError({ url, body, err: e });
				return [];
			});
		await db
			.update(billing)
			.set({
				service_type_id: +service_type_id
			})
			.where(eq(billing.id, +billing_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await fileHandle.insert(file, create_payment_service[0].id, 'paymentService');
		redirect(303, `/patient/dialy`);
	}
};
