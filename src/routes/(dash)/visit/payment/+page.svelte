<script lang="ts">
	import type { PageServerData } from './$types';
	import { locale } from '$lib/translations/locales.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import CropImage from '$lib/coms-form/CropImage.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_service_types, billing_id } = $derived(data);
	let loading = $state(false);
	let payment_service = $state(1);
</script>

<div class="alert border border-primary border-2 pt-3">
	<Form enctype="multipart/form-data" method="post" action="?/create_payment_service" bind:loading>
		<input type="hidden" name="billing_id" value={billing_id || ''} />
		<div class=" row pb-3">
			<div class="col-sm-3">
				<button
					data-bs-toggle="modal"
					data-bs-target="#payment_service"
					type="button"
					class="btn btn-outline-primary btn-sm">{locale.T('payment')}</button
				>
			</div>
			<div class="col-sm-9">
				<select
					bind:value={payment_service}
					class="form-control"
					name="service_type_id"
					id="payment_service_"
				>
					{#each get_service_types as item}
						<option value={item?.id}>{item?.by}</option>
					{/each}
				</select>
				{#if payment_service !== 1}
					<div class="input-group pt-3">
						<label for="code" class="input-group-text">Code</label>
						<input type="text" name="code" class="form-control" id="code" />
					</div>
				{/if}
			</div>
		</div>
		<div class=" row pb-3">
			<div class="col-sm-3">
				<button type="button" class="btn btn-primary btn-sm">{locale.T('references')}</button>
			</div>
			<div class="col-sm-9">
				<CropImage
					aspect_ratio
					label={locale.T('references')}
					name="file"
					related_type_="paymentService"
				/>
			</div>
		</div>
		<div class="text-end">
			<SubmitButton {loading} />
		</div>
	</Form>
</div>
