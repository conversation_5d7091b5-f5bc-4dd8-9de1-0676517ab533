<script lang="ts">
	import type { PageServerData } from './$types';
	import { locale } from '$lib/translations/locales.svelte';
	import Graphs from '$lib/coms/Graphs.svelte';

	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { count_patinet, count_opd, count_ipd, count_labo, count_img, count_medecine } =
		$derived(data);
	let list = $derived([
		{
			data: [count_opd, count_ipd, count_labo, count_img]
		}
	]);
	$effect(() => {});
</script>

<div class="row g-1">
	<div class="col-lg-3 col-md-4 col-sm-12">
		<div class="card text-bg-danger">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 30px;">{locale.T('billing')}</div>

					<div style="font-size: 30px;">{count_medecine}</div>
				</div>
				<div class="col-auto">
					<i class="fa-solid fa-tablets fa-6x"></i>
				</div>
			</div>
		</div>
	</div>
	<div class="col-lg-3 col-md-6 col-sm-12">
		<div class="card text-bg-primary">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 30px;">{locale.T('patient')}</div>
					<div style="font-size: 30px;">{count_patinet}</div>
				</div>
				<div class="col-auto">
					<i class="fas fa-person fa-6x"></i>
				</div>
			</div>
		</div>
	</div>
	<div class="col-lg-3 col-md-4 col-sm-12">
		<div class="card text-bg-warning">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 30px;">{locale.T('opd')}</div>
					<div style="font-size: 30px;">{count_opd}</div>
				</div>
				<div class="col-auto">
					<i class="fas fa-stethoscope fa-6x"></i>
				</div>
			</div>
		</div>
	</div>
	<div class="col-lg-3 col-md-4 col-sm-12">
		<div class="card text-bg-info">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 30px;">{locale.T('ipd')}</div>
					<div style="font-size: 30px;">{count_ipd}</div>
				</div>
				<div class="col-auto">
					<i class="fas fa-procedures fa-6x"></i>
				</div>
			</div>
		</div>
	</div>

	<div class="col-lg-3 col-md-4 col-sm-12">
		<div class="card text-bg-success">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 30px;">{locale.T('laboratory')}</div>
					<div style="font-size: 30px;">{count_labo}</div>
				</div>
				<div class="col-auto">
					<i class="fas fa-flask nav-icon fa-6x"></i>
				</div>
			</div>
		</div>
	</div>
	<div class="col-lg-3 col-md-4 col-sm-12">
		<div class="card text-bg-dark">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 30px;">{locale.T('imagerie')}</div>
					<div style="font-size: 30px;">{count_img}</div>
				</div>
				<div class="col-auto">
					<i class="fas fa-image fa-6x"></i>
				</div>
			</div>
		</div>
	</div>

	<div class="col-lg-3 col-md-4 col-sm-12">
		<div class="card text-bg-info">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 30px;">{locale.T('vaccine')}</div>
					<div style="font-size: 30px;">{150}</div>
				</div>
				<div class="col-auto">
					<i class="fa-solid fa-syringe fa-6x"></i>
				</div>
			</div>
		</div>
	</div>
	<div class="col-lg-3 col-md-4 col-sm-12">
		<div class="card text-bg-danger">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 30px;">{locale.T('documents')}</div>
					<div style="font-size: 30px;">150</div>
				</div>
				<div class="col-auto">
					<i class="fa-regular fa-file-lines fa-6x"></i>
				</div>
			</div>
		</div>
	</div>
	<div class="col-lg-3 col-md-4 col-sm-12">
		<div class="card text-bg-danger">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 30px;">{locale.T('medicine')}</div>
					<div style="font-size: 30px;">{count_medecine}</div>
				</div>
				<div class="col-auto">
					<i class="fa-solid fa-tablets fa-6x"></i>
				</div>
			</div>
		</div>
	</div>
</div>
<div class="row g-1">
	<div class="col-4">
		<Graphs
			type="bar"
			labels={[locale.T('opd'), locale.T('ipd'), locale.T('laboratory'), locale.T('imagerie')]}
			title=""
			datasets={list}
		/>
	</div>
	<div class="col-4">
		<Graphs
			type="pie"
			labels={[locale.T('opd'), locale.T('ipd'), locale.T('laboratory'), locale.T('imagerie')]}
			title=""
			datasets={list}
		/>
	</div>
	<div class="col-4">
		<Graphs
			type="doughnut"
			labels={[locale.T('opd'), locale.T('ipd'), locale.T('laboratory'), locale.T('imagerie')]}
			title=""
			datasets={list}
		/>
	</div>
</div>

<!-- 
<Form enctype="multipart/form-data" action="?/test" method="POST">
	<CropImage submit={false} name="file" />
	<input type="hidden" name="id" value={id ?? ''} />
	<input type="time" name="time" bind:value={time} id="" />
	<input type="date" name="date" bind:value={date} id="" />
	<input type="datetime-local" name="datetime" value={datetime} id="" />

	{date} <br />
	{YYYYMMDD_Format.date(date).length}
	<button type="submit">Submit</button>
</Form>

<hr />
{#each get_tests as item}
	<Form reset={true} action="?/d_test" method="POST">
		
		<h3 onclick={() => (id === Number(item.id) ? (id = null) : (id = item.id))}>
			<button class="btn btn-sm btn-light fs-3 text-danger" value={item.id} type="submit" name="id"
				>X {item.id ?? ''}</button
			>
			<DDMMYYYYFormat date={item.date} style="date"/>
			<DDMMYYYYFormat date={item.datetime} />
		</h3>
	</Form>
{/each}
<h1 id="one" class="m-0">Dashbaord</h1>
<ol class="breadcrumb float-sm-right">
	<li class="breadcrumb-item"><a href="/">Home</a></li>
	<li class="breadcrumb-item active">Dashboard</li>
</ol>
/> -->
