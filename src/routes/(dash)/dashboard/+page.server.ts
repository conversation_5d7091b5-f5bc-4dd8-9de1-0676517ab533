import { db } from '$lib/server/db';
import { uploadFile } from '$lib/server/upload/fileHandle';
import { eq, like, sql } from 'drizzle-orm';
import type { PageServerLoad, Actions } from './$types';
import {
	imagerieRequest,
	laboratory,
	patient,
	product,
	progressNote,
	test,
	visit
} from '$lib/server/schemas';
import { fail } from '@sveltejs/kit';
export const load: PageServerLoad = async ({ url }) => {
	const q = url.searchParams.get('q') || '';
	const get_tests = await db.query.test.findMany();
	const get_patients = await db.query.patient.findMany({
		where: like(patient.name_latin, `%${q}%`)
	});
	const p: [] = (await db.execute(sql`show tables;`)) as unknown as [];
	const tables: { Tables_in_hms: string }[] = p.flatMap((e): [] => e).slice(0, -1);
	const count_patinet = await db.$count(patient);
	const count_opd = await db.$count(visit, eq(visit.checkin_type, 'OPD'));
	const count_ipd = await db.$count(progressNote);
	const count_labo = await db.$count(laboratory);
	const count_img = await db.$count(imagerieRequest);
	const count_medecine = await db.$count(product, eq(product.category_id, 1));
	return {
		tables: tables.map((e) => e.Tables_in_hms).sort(),
		get_tests,
		get_patients,
		count_patinet,
		count_opd,
		count_ipd,
		count_labo,
		count_img,
		count_medecine
	};
};
export const actions: Actions = {
	img: async ({ request }) => {
		const body = await request.formData();
		try {
			const file = body.get('file') as File;
			await uploadFile(file);
		} catch (e) {
			console.log(e);
		}
	},
	test: async ({ request }) => {
		const body = await request.formData();
		const { date, datetime, time, id } = Object.fromEntries(body) as Record<string, string>;
		if (!date || !datetime || !time) return fail(400, { Err: true });
		if (id) {
			await db
				.update(test)
				.set({
					date: date,
					datetime: datetime,
					time: time
				})
				.where(eq(test.id, +id));
		}
		if (!id) {
			await db.insert(test).values({
				date: date,
				datetime: datetime,
				time: time
			});
		}
	},
	d_test: async ({ request }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;

		await db.delete(test).where(eq(test.id, +id));
	}
};
