import { db } from '$lib/server/db';
import {
	imagerieRequest,
	template,
	resultForm,
	type TType,
	options,
	uploads
} from '$lib/server/schemas';
import type { Actions, PageServerLoad } from './$types';
import { and, desc, eq } from 'drizzle-orm';
import { YYYYMMDD_Format } from '$lib/server/utils';
import logError from '$lib/server/utils/logError';
import { fail, redirect } from '@sveltejs/kit';
import { fileHandle } from '$lib/server/upload';
export const load = (async ({ parent, url }) => {
	await parent();
	const imagerie_request_id = url.searchParams.get('imagerie_request_id') || '';
	const group_id = url.searchParams.get('group_id') || '';
	const get_imagerie_templates = await db.query.template.findMany({
		where: eq(template.group_id, +group_id)
	});
	const get_imagerie_request = await db.query.imagerieRequest.findFirst({
		where: eq(imagerieRequest.id, +imagerie_request_id),
		with: {
			visit: {
				with: {
					progressNote: true,
					patient: {
						with: {
							commune: true,
							district: true,
							provice: true,
							village: true
						}
					},
					staff: true
				}
			},
			product: true
		},
		orderBy: desc(imagerieRequest.visit_id)
	});
	const get_uploads = await db.query.uploads.findMany({
		where: and(
			eq(uploads.related_id, +imagerie_request_id),
			eq(uploads.related_type, 'imagerieRequest')
		)
	});
	if (!get_imagerie_request) redirect(303, '/imagerie');
	// if (get_imagerie_request.is_ob_form) redirect(300, `/imagerie/result/ob?imagerie_request_id=${get_imagerie_request.id}`);
	return {
		get_imagerie_request: {
			...get_imagerie_request,
			uploads: get_uploads
		},
		get_imagerie_templates
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	update_img_result: async ({ request, url }) => {
		const body = await request.formData();
		const { id, result, status } = Object.fromEntries(body) as Record<string, string>;
		await db
			.update(imagerieRequest)
			.set({
				result: result === '<p><br></p>' ? null : result,
				status: status === 'on' ? true : false,
				finish_datetime: status === 'on' ? YYYYMMDD_Format.datetime(new Date()) : null,
				is_ob_form: false
			})
			.where(eq(imagerieRequest.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		redirect(300, '/imagerie');
	},
	uploads_img_result: async ({ request }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		const file = body.get('file') as File;
		if (file.size) {
			await fileHandle.insert(file, +id, 'imagerieRequest');
		}
	},
	delete_picture: async ({ request }) => {
		const body = await request.formData();
		const { file_name } = Object.fromEntries(body) as Record<string, string>;
		await fileHandle.drop(file_name);
	},
	create_option: async ({ request, url }) => {
		const body = await request.formData();
		const { name, result_form_id } = Object.fromEntries(body) as Record<string, string>;
		if (!name || !result_form_id) return fail(400, { optErr: true });
		await db
			.insert(options)
			.values({
				name: name,
				result_form_id: +result_form_id
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	create_form: async ({ request, url }) => {
		const body = await request.formData();
		const { name, type } = Object.fromEntries(body) as Record<string, string>;
		const check_type = ['number', 'text', 'datetime-local', 'option'];
		if (!type || !check_type.includes(type) || !name)
			return fail(400, { message: 'invalid type of form' });
		await db
			.insert(resultForm)
			.values({
				name: name,
				type: type as TType
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_form: async ({ request, url }) => {
		const body = await request.formData();
		const { name, id } = Object.fromEntries(body) as Record<string, string>;
		if (!name || !id) return fail(400, { message: 'invalid id' });
		await db
			.update(resultForm)
			.set({
				name: name
			})
			.where(eq(resultForm.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	delete_form: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		if (!id) return fail(400, { message: 'invalid id' });
		await db
			.delete(resultForm)
			.where(eq(resultForm.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
