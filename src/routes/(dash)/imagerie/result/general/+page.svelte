<script lang="ts">
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import TextEditor from '$lib/coms-cu/TextEditor.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	import Form from '$lib/coms-form/Form.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import CropImage from '$lib/coms-form/CropImage.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_imagerie_request, get_imagerie_templates } = $derived(data);
	let loading = $state(false);
	let image_id: number | null = $state(null);
	let filename = $state('');
	let imagerie_templage_id: number | null = $state(null);
	let imagerie_templage = $derived(
		get_imagerie_templates?.find((e) => e.id === imagerie_templage_id)?.template
	);
</script>

<DeleteModal action="?/delete_picture" id={image_id!}>
	<input type="hidden" name="file_name" value={filename ?? ''} />
</DeleteModal>
<br />

<div class="card bg-light">
	{#if get_imagerie_request.input_by_id}
		<Form action="?/update_img_result" method="post" bind:loading>
			<input type="hidden" name="id" value={get_imagerie_request?.id || ''} />
			<div class="card-body">
				<div class="col-12">
					<SelectParam
						bind:value={imagerie_templage_id}
						items={get_imagerie_templates?.map((e) => ({ id: e.id, name: e.diagnosis }))}
					/>
				</div>
				<br />
				<div class="col-12">
					<div class=" pb-3">
						<label for="result">{locale.T('result')}</label>
						<TextEditor
							height={500}
							name="result"
							setValue={imagerie_templage
								? imagerie_templage
								: (get_imagerie_request?.result ?? '')}
						/>
					</div>
				</div>
				<div class="row pb-3">
					<div class="col-12">
						<label for="status">&#10004; {locale.T('finish')}</label>
						<div class="form-control">
							<div class="form-check m-0">
								<input
									checked={get_imagerie_request?.status}
									name="status"
									class="form-check-input"
									type="checkbox"
									id="status"
								/>
								<label for="status">{locale.T('finish')} </label>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="card-header text-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	{/if}
	<div class="card-body">
		{#if get_imagerie_request.scan_by_id}
			<Form enctype="multipart/form-data" action="?/uploads_img_result" method="post" bind:loading>
				<input type="hidden" name="id" value={get_imagerie_request?.id || ''} />
				<div class="col-12">
					<div class=" pb-3">
						<label for="exampleInputFile">{locale.T('picture')}</label>
						<CropImage aspect_ratio submit={true} name="file" />
						<!-- <input type="hidden" name="picture" /> -->
						<!-- <input
							multiple
							type="file"
							name="file"
							accept="image/*"
							class="form-control"
							id="exampleInputFile"
						/> -->
					</div>
				</div>
				<div class="row">
					{#each get_imagerie_request?.uploads || [] as item (item.id)}
						<div class="p-2 col-3">
							<img class="rounded img-thumbnail" src={item?.filename} alt="" />
							<button
								data-bs-toggle="modal"
								data-bs-target="#delete_modal"
								type="button"
								onclick={() => {
									image_id = item.id;
									filename = item.filename || '';
								}}
								class="btn btn-danger w-100">{locale.T('delete_')}</button
							>
						</div>
					{/each}
				</div>
			</Form>
		{/if}
	</div>
</div>
