<script lang="ts">
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import GenderAge from '$lib/coms/GenderAge.svelte';
	import Name from '$lib/coms/Name.svelte';
	import ConfirmModal from '$lib/coms-form/ConfirmModal.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_patients, items, get_imagires } = $derived(data);
	let total_male = $derived(
		get_imagires.filter((e) => e.visit?.patient?.gender.toLowerCase() === 'male').length
	);
	let n = $state(1);
	let imagerie_id: number | null = $state(null);
	let is_scan = $state(false);
	let is_input = $state(false);
</script>

<ConfirmModal action="?/assign_inputer" id={imagerie_id}>
	<ul class="list-group">
		<li class="list-group-item text-primary-emphasis">
			<input
				disabled={is_scan}
				checked={is_scan}
				class="form-check-input"
				type="checkbox"
				name="is_scan"
				id="is_scan"
			/>
			&nbsp;
			<i class="fa-solid fa-camera-retro"></i>
			<label class="form-check-label" for="is_scan"> បញ្ចូលរូបភាពស្កេន</label>
		</li>
		<li class="list-group-item">
			<input
				checked={is_input}
				disabled={is_input}
				class="form-check-input"
				type="checkbox"
				name="is_input"
				id="is_input"
			/>
			&nbsp;
			<i class="fa-solid fa-file-pen"></i>
			<label class="form-check-label" for="is_input"> គ្រូពេទ្យពិនិត្យ</label>
		</li>
	</ul>
</ConfirmModal>

<div class="modal fade" id="modal-visite">
	<div class="modal-dialog modal-dialog-centered modal-sm">
		<input
			id="close_visit_modal"
			class="hide"
			data-bs-dismiss="modal"
			aria-label="Close"
			type="hidden"
		/>
	</div>
</div>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('imagerie')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/imagerie" class="btn btn-link p-0 text-secondary"
					><i class="nav-icon fas fa-image"></i>
					{locale.T('imagerie')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<HeaderQuery>
			<div class="col-sm-2">
				<div class="input-group">
					<span class="input-group-text">{locale.T('start')}</span>
					<input type="date" name="start" class="form-control" />
				</div>
			</div>
			<div class="col-sm-2">
				<div class="input-group">
					<span class="input-group-text">{locale.T('end')}</span>
					<input type="date" name="end" class="form-control" />
				</div>
			</div>
			<div class="col-sm-3">
				<SelectParam
					q_name="q"
					placeholder={locale.T('patient')}
					name="patient_id"
					items={get_patients.map((e) => ({
						id: e.id,
						name: e.name_khmer?.concat(` ${e.name_latin}`)
					}))}
				/>
			</div>
			<div class="col-sm-2">
				<div class="input-group">
					<span class="input-group-text">{locale.T('status')}</span>
					<select class="form-control" name="status" id="status">
						<option value="">All</option>
						<option value="true">Done</option>
						<option value="false">Not Done</option>
					</select>
				</div>
			</div>
		</HeaderQuery>
	</div>
	<div style="height: {store.inerHight};" class="card-body table-responsive p-0">
		<table class="table table-bordered table-light text-nowrap table-hover">
			<thead class="sticky-top table-active">
				<tr class="text-center">
					<th style="width: 3%;">{locale.T('n')}</th>
					<th style="width: 7%;">{locale.T('date')}</th>
					<th style="width: 5%;">{locale.T('id')}</th>
					<th style="width: 10%;"> {locale.T('patient_name')}</th>
					<th style="width: 7%;"> {locale.T('requester')}</th>
					<th style="width: 7%;"> {locale.T('checker')}</th>
					<th style="width: 7%;"> {locale.T('scanner')}</th>
					<th style="width: 5%;"> {locale.T('visit_type')}</th>
					<th style="width: 20%;">{locale.T('request_check')}</th>
					<th style="width: 5%;">{locale.T('view')}</th>
					<th style="width: 10%;">{locale.T('result')}</th>
				</tr>
			</thead>
			<tbody>
				{#each get_imagires as item, index}
					<tr class="text-center">
						<td>{n + index}</td>
						<td class="text-center">
							<DDMMYYYYFormat style="date" date={item.visit?.date_checkup} />
							<br />
							<DDMMYYYYFormat style="time" date={item.visit?.date_checkup} />
						</td>
						<td class="text-center">
							PT{item.visit?.patient_id}
							<br />
							IM{item.id}
							<br />
							VS{item.visit_id}
						</td>
						<td>
							{#if item?.input_by_id && item.scan_by_id}
								<a href="/report/{item.id}/imagerie?row=false" class="btn btn-link">
									{item?.patient?.name_khmer}
									<br />
									{item?.patient?.name_latin}
									<GenderAge
										dob={item?.patient?.dob}
										date={new Date()}
										gender={item?.patient?.gender}
									/>
								</a>
							{:else}
								<button
									onclick={() => {
										if (item.scan_by_id) {
											is_scan = true;
										} else {
											is_scan = false;
										}
										if (item.input_by_id) {
											is_input = true;
										} else {
											is_input = false;
										}
										imagerie_id = item.id;
									}}
									data-bs-toggle="modal"
									data-bs-target={`#confirm_modal`}
									class="btn btn-link text-danger"
								>
									<Name both name={item?.patient} />
									<GenderAge
										dob={item?.patient?.dob}
										date={new Date()}
										gender={item?.patient?.gender}
									/>
								</button>
							{/if}
						</td>
						<td>
							<Name title={item?.visit?.staff?.title} name={item?.visit?.staff} />
						</td>
						<td>
							<Name title={item?.inputBy?.title} name={item?.inputBy} />
						</td>
						<td>
							<Name title={item?.scanBy?.title} name={item?.scanBy} />
						</td>
						<td>{item?.visit?.checkin_type}</td>
						<td>
							<div>
								<span class=" badge text-bg-info py-2 text-start"
									>{item.product?.products ?? ''}</span
								>
							</div>
							<span class="badge text-bg-warning py-2 text-start text-break">
								{item?.note ?? ''}
							</span>
						</td>
						<td class="text-start">
							<fieldset disabled={!item.status}>
								<div class="dropdown">
									<a
										class="btn btn-success btn-sm"
										href={'#'}
										role="button"
										data-bs-toggle="dropdown"
										aria-expanded="false"
									>
										{locale.T('view')}
										{locale.T('result')}
										{#if item.scan_by_id && item.input_by_id}
											<span><i class="fa-solid fa-check-double"></i></span>
										{/if}
										{#if item.scan_by_id && !item.input_by_id}
											<span><i class="fa-solid fa-check"></i></span>
										{/if}
									</a>
									<ul class="dropdown-menu">
										<li>
											<a
												class="dropdown-item"
												target="_blank"
												href="/report/{item.id}/imagerie?row=true"
												><i class="fa-regular fa-image"></i> {locale.T('up_down')}
											</a>
										</li>
										<li>
											<a
												class="dropdown-item"
												target="_blank"
												href="/report/{item.id}/imagerie?row=false"
												><i class="fa-regular fa-file-image"></i> {locale.T('left_right')}
											</a>
										</li>
										<li>
											<a class="dropdown-item" target="_blank" href="/report/{item.id}/imagerie"
												><i class="fa-solid fa-images"></i> {locale.T('ecg')}
											</a>
										</li>
									</ul>
								</div>
							</fieldset>
						</td>
						<td class="text-center">
							<fieldset disabled={!item.input_by_id && !item.scan_by_id}>
								{#if item.status}
									{#if !item.is_ob_form}
										<a
											href="/imagerie/result/general?imagerie_request_id={item.id}&group_id={item
												.product?.group_id}"
											class="btn btn-warning btn-sm"
											>{locale.T('edit')}
										</a>
									{:else}
										<a
											href="/imagerie/result/ob?imagerie_request_id={item.id}"
											class="btn btn-warning btn-sm"
											>{locale.T('edit')}
										</a>
									{/if}
								{:else if !item.is_ob_form}
									<a
										href="/imagerie/result/general?imagerie_request_id={item.id}&group_id={item
											.product?.group_id}"
										class="btn btn-primary btn-sm"
										>{locale.T('result')}
									</a>
								{:else}
									<a
										href="/imagerie/result/ob?imagerie_request_id={item.id}"
										class="btn btn-primary btn-sm"
										>{locale.T('result')}
									</a>
								{/if}
							</fieldset>
						</td>
					</tr>
				{/each}
				<tr class="table-success">
					<td colspan="11" class="text-center">
						{locale.T('total')}: {get_imagires.length}
						{locale.T('people')},
						{locale.T('male')}: {total_male}
						{locale.T('female')}: {get_imagires.length - total_male}
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations bind:n {items} />
	</div>
</div>
