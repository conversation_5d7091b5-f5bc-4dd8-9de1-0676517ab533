import { db } from '$lib/server/db';
import { appointmentInjection, injection, patient, vaccine } from '$lib/server/schemas';
import { and, asc, desc, eq, like, or } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { betweenHelper, YYYYMMDD_Format, pagination } from '$lib/server/utils';
import logError from '$lib/server/utils/logError';

export const load = (async ({ parent, url }) => {
	await parent();
	const patient_id = url.searchParams.get('patient_id') ?? '';
	const q = url.searchParams.get('q') ?? '';
	const get_patients = await db.query.patient.findMany({
		where: or(
			like(patient.name_latin, `%${q}%`),
			like(patient.name_khmer, `%${q}%`),
			like(patient.telephone, `%${q}%`)
		),
		limit: 200
	});
	const get_injection = await db.query.injection.findMany({
		where: and(
			betweenHelper(url, injection.datetime),
			patient_id ? eq(injection.patient_id, +patient_id) : undefined
		),
		with: {
			unit: true,
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			vaccine: {
				with: {
					product: true,
					visit: true
				},
				orderBy: asc(vaccine.id)
			},
			appointmentInjection: {
				orderBy: asc(appointmentInjection.times)
			}
		},
		orderBy: desc(injection.datetime),
		...pagination(url)
	});
	const count = await db.$count(
		injection,
		and(
			betweenHelper(url, injection.datetime),
			patient_id ? eq(injection.patient_id, +patient_id) : undefined
		)
	);
	return {
		get_injection,
		get_patients,
		items: count
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_appointment_inject: async ({ request, url }) => {
		const body = await request.formData();
		const { times, appointment, discription, injection_id } = Object.fromEntries(body) as Record<
			string,
			string
		>;
		await db
			.insert(appointmentInjection)
			.values({
				appointment: appointment,
				times: +times,
				discription: discription,
				injection_id: +injection_id
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_appointment_inject: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		const get_appoinmten = await db.query.appointmentInjection.findFirst({
			where: eq(appointmentInjection.id, +id)
		});
		await db
			.update(appointmentInjection)
			.set({
				status: !get_appoinmten?.status,
				datetime_inject: YYYYMMDD_Format.datetime(new Date())
			})
			.where(eq(appointmentInjection.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	delete_appionment_inject: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.delete(appointmentInjection)
			.where(eq(appointmentInjection.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
