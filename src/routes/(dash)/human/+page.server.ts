import { db } from '$lib/server/db';
import { staff, role, uploads } from '$lib/server/schemas';
import type { Actions, PageServerLoad } from './$types';
import { and, eq, inArray, like } from 'drizzle-orm';
import logError from '$lib/server/utils/logError';
import { pagination } from '$lib/server/utils';
import { fail } from '@sveltejs/kit';

export const load = (async ({ parent, url }) => {
	await parent();
	const q = url.searchParams.get('q') ?? '';
	const get_staffs = await db.query.staff.findMany({
		where: and(
			q ? like(staff.name_khmer, `%${q}%`) : undefined,
			q ? like(staff.name_latin, `%${q}%`) : undefined
		),
		with: {
			user: true,
			district: true,
			commune: true,
			village: true,
			provice: true,
			staffToDemartment: {
				with: {
					department: true
				}
			},
			staffToRole: {
				with: {
					role: true
				}
			}
		},
		...pagination(url)
	});
	const items = await db.$count(
		staff,
		and(
			q ? like(staff.name_khmer, `%${q}%`) : undefined,
			q ? like(staff.name_latin, `%${q}%`) : undefined
		)
	);
	const get_uploads = await db.query.uploads.findMany({
		where: and(
			eq(uploads.related_type, 'staff'),
			inArray(
				uploads.related_id,
				get_staffs.map((e) => e.id)
			)
		)
	});
	const get_roles = await db.query.role.findMany({});
	return {
		get_staffs: get_staffs.map((e) => {
			return {
				...e,
				uploads: get_uploads.find((ee) => ee.related_id === e.id)
			};
		}),
		items,
		get_roles
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	delete_staff: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		try {
			await db.delete(staff).where(eq(staff.id, Number(id)));
		} catch (e) {
			logError({ url, body, err: e });
		}
	},
	create_role: async ({ request }) => {
		const body = await request.formData();
		const { role_, id } = Object.fromEntries(body) as Record<string, string>;
		if (!role_.trim()) return fail(400, { role_: true });
		if (id) {
			await db
				.update(role)
				.set({
					role: role_
				})
				.where(eq(role.id, Number(id)));
		} else {
			await db.insert(role).values({
				role: role_
			});
		}
	},
	delete_role: async ({ request }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db.delete(role).where(eq(role.id, Number(id)));
	}
};
