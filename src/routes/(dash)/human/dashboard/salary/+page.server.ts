import { db } from '$lib/server/db';
import { and, eq, isNull } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { payroll, salary, staff } from '$lib/server/schemas';
import { YYYYMMDD_Format } from '$lib/server/utils';
import logError from '$lib/server/utils/logError';
import { fail, redirect } from '@sveltejs/kit';
export const load = (async ({ url }) => {
	const get_currency = await db.query.currency.findFirst({});
	const now_month = YYYYMMDD_Format.date(new Date()).slice(0, 7);
	const y_m_client = url.searchParams.get('effective_date') ?? now_month;
	const get_salary_this_month = await db.query.salary.findMany({
		where: and(eq(salary.effective_date, y_m_client)),
		with: {
			staff: true,
			payroll: true
		}
	});
	const get_payment_types = await db.query.paymentType.findMany({});
	return {
		get_currency,
		get_salary_this_month,
		get_payment_types
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_salary: async ({ request, url }) => {
		const body = await request.formData();
		const { effective_date } = Object.fromEntries(body) as Record<string, string>;
		const get_salary = await db.query.salary.findMany({
			where: eq(salary.effective_date, effective_date)
		});
		if (!effective_date) return fail(400, { message: 'Please select a date' });
		if (get_salary.length) return fail(400, { message: 'Salary already created' });
		const get_staffs_active = await db.query.staff.findMany({
			where: isNull(staff.datetime_stop)
		});
		for (const staff of get_staffs_active) {
			await db
				.insert(salary)
				.values({
					staff_id: staff.id,
					base_salary: staff.base_salary,
					allowance: 0,
					bonus: 0,
					deduction: 0,
					total_salary: staff.base_salary,
					effective_date: effective_date
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		redirect(303, `/human/dashboard/salary?effective_date=${effective_date}`);
	},
	update_salary: async ({ request, url }) => {
		const body = await request.formData();
		const id = body.getAll('id');
		const base_salary = body.getAll('base_salary');
		const allowance = body.getAll('allowance');
		const bonus = body.getAll('bonus');
		const deduction = body.getAll('deduction');
		if (!id.length) return fail(400, { message: 'Please select a id' });
		for (let i = 0; i < id.length; i++) {
			const id_ = Number(id[i]);
			const base_salary_ = Number(base_salary[i]);
			const allowance_ = Number(allowance[i]);
			const bonus_ = Number(bonus[i]);
			const deduction_ = Number(deduction[i]);
			if (isNaN(base_salary_)) return fail(400, { message: 'Please enter a valid base salary' });
			if (isNaN(allowance_)) return fail(400, { message: 'Please enter a valid allowance' });
			if (isNaN(bonus_)) return fail(400, { message: 'Please enter a valid bonus' });
			if (isNaN(deduction_)) return fail(400, { message: 'Please enter a valid deduction' });
			await db
				.update(salary)
				.set({
					allowance: allowance_,
					bonus: bonus_,
					deduction: deduction_,
					total_salary: base_salary_ + allowance_ + bonus_ - deduction_
				})
				.where(eq(salary.id, id_))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	},
	update_single_salary: async ({ url, request }) => {
		const body = await request.formData();
		for (const [key, value] of Array.from(body)) {
			if (['allowance', 'bonus', 'deduction'].includes(key)) {
				const id = body.get('id') ?? '';
				const value_ = Number(value);
				if (isNaN(value_)) return fail(400, { message: 'Please enter a valid ' + key });
				await db
					.update(salary)
					.set({
						[key]: value_
					})
					.where(eq(salary.id, +id))
					.catch((e) => {
						logError({ url, body, err: e });
					});
				const get_salary = await db.query.salary.findFirst({
					where: eq(salary.id, +id)
				});

				if (get_salary) {
					await db
						.update(salary)
						.set({
							total_salary:
								Number(get_salary.base_salary) +
								Number(get_salary.allowance) +
								Number(get_salary.bonus) -
								Number(get_salary.deduction)
						})
						.where(eq(salary.id, +id))
						.catch((e) => {
							logError({ url, body, err: e });
						});
				}
			}
		}
	},
	delete_salary_this_month: async ({ url, request }) => {
		const body = await request.formData();
		const { id: effective_date } = Object.fromEntries(body) as Record<string, string>;
		if (!effective_date) return fail(400, { message: 'Please select a date' });
		await db
			.delete(salary)
			.where(eq(salary.effective_date, effective_date))
			.catch((e) => {
				logError({ url, body: body, err: e });
			});

		redirect(303, `/human/dashboard/salary?effective_date`);
	},
	create_payroll: async ({ url, request }) => {
		const body = await request.formData();
		const { effective_date, payment_date, payment_type_id } = Object.fromEntries(body) as Record<
			string,
			string
		>;
		if (!effective_date || !payment_date || !payment_type_id)
			return fail(400, { message: 'Please select a date' });
		const get_salaries = await db.query.salary.findMany({
			where: eq(salary.effective_date, effective_date),
			with: {
				payroll: true
			}
		});
		if (!get_salaries.length) return fail(400, { message: 'No salary created' });
		for (const e of get_salaries) {
			if (!e.payroll) {
				await db
					.insert(payroll)
					.values({
						salary_id: e.id,
						payment_date: YYYYMMDD_Format.date(new Date(payment_date)),
						payment_type_id: +payment_type_id,
						amount: Number(e.total_salary)
					})
					.catch((e) => {
						logError({ url, body, err: e });
					});
			} else {
				await db
					.update(payroll)
					.set({
						payment_date: YYYYMMDD_Format.date(new Date(payment_date)),
						payment_type_id: +payment_type_id,
						amount: Number(e.total_salary)
					})
					.where(eq(payroll.salary_id, e.id))
					.catch((e) => {
						logError({ url, body, err: e });
					});
			}
		}
	}
};
