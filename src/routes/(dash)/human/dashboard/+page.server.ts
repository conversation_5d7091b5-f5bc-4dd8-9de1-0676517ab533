import { db } from '$lib/server/db';
import { and, eq, inArray, isNotNull, isNull, like, or } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { staff, uploads } from '$lib/server/schemas';
import { YYYYMMDD_Format } from '$lib/server/utils';
export const load = (async ({ url }) => {
	const get_salaries = await db.query.salary.findMany({});
	const get_currency = await db.query.currency.findFirst({});
	const q = url.searchParams.get('q') || '';
	const status = url.searchParams.get('status') || '';
	const get_staffs = await db.query.staff.findMany({
		where: or(
			q ? like(staff.name_khmer, `%${q}%`) : undefined,
			q ? like(staff.name_latin, `%${q}%`) : undefined,
			status
				? status === 'active'
					? isNull(staff.datetime_stop)
					: isNotNull(staff.datetime_stop)
				: undefined
		),
		with: {
			user: true
		}
	});
	const items = await db.$count(
		staff,
		or(
			q ? like(staff.name_khmer, `%${q}%`) : undefined,
			q ? like(staff.name_latin, `%${q}%`) : undefined
		)
	);

	const get_roles = await db.query.role.findMany({});
	const get_uploads = await db.query.uploads.findMany({
		where: and(
			eq(uploads.related_type, 'staff'),
			inArray(
				uploads.related_id,
				get_staffs.map((e) => e.id)
			)
		)
	});
	const get_this_month = YYYYMMDD_Format.date(new Date()).slice(0, 7);
	return {
		get_staffs: get_staffs.map((e) => {
			return {
				...e,
				uploads: get_uploads.find((ee) => ee.related_id === e.id)
			};
		}),
		items: items,
		get_roles,
		get_salaries,
		get_currency,
		get_this_month
	};
}) satisfies PageServerLoad;
