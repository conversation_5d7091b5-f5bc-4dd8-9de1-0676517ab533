<script lang="ts">
	import HandleQ from '$lib/coms-form/HandleQ.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import SetBack from '$lib/coms/SetBack.svelte';
	import { dobToAge } from '$lib/helper';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	let { data }: { data: PageServerData } = $props();
	let { get_staffs, get_currency, get_roles, items, get_this_month } = $derived(data);
	let n = $state(1);
</script>

<div class="card bg-light">
	<div class="card-header">
		<div class="row g-1">
			<div class="col">
				<HeaderQuery class="row g-1">
					<div class="col-sm-3">
						<HandleQ />
					</div>
					<div class="col-sm-3">
						<select class="form-control" name="role_id" id="role_id">
							<option selected value="">{locale.T('role')}</option>
							{#each get_roles as item}
								<option value={item.id}>{item.role}</option>
							{/each}
						</select>
					</div>
					<div class="col-sm-3">
						<select class="form-control" name="status" id="status">
							<option selected value="">{locale.T('status')}</option>
							<option value="active">{locale.T('active_employees')}</option>
							<option value="inactive">{locale.T('inactive_employees')}</option>
						</select>
					</div>
				</HeaderQuery>
			</div>
			<div class="col-auto">
				<a href="/human/dashboard/salary?effective_date={get_this_month}" class="btn btn-success"
					><i class="fa-solid fa-money-check-dollar"></i>
					{locale.T('payroll_list')}
				</a>
			</div>
		</div>
	</div>
	<div style="height: {store.inerHight}; " class="card-body table-responsive p-0 m-0">
		<table class="table table-hover table-bordered table-light">
			<thead class="sticky-top bg-light table-active">
				<tr class="text-center">
					<th class="text-center">{locale.T('n')}</th>
					<th>{locale.T('picture')}</th>
					<th>{locale.T('name')}</th>
					<th>{locale.T('specific')}</th>
					<th>{locale.T('gender')}</th>
					<th>{locale.T('age')}</th>
					<th>{locale.T('base_salary')}</th>
					<th>{locale.T('status')}</th>
				</tr>
			</thead>
			<tbody>
				{#each get_staffs as item, index}
					<tr class="text-center">
						<td>{index + n}</td>
						<td class="text-center">
							<img
								src={item?.uploads?.filename ? `${item?.uploads?.filename}` : '/no-user.png'}
								alt=""
								height="30"
							/>
						</td>
						<td>
							<SetBack
								href="/human/create/profile/?staff_id={item.id}&village_id={item.village_id}&district_id={item.district_id}&commune_id={item.commune_id}&province_id={item.province_id}"
							>
								{item?.name_khmer} <br />
								{item?.name_latin}
							</SetBack>
						</td>
						<td class="text-break">{item?.specialist}</td>
						<td>{item?.gender}</td>
						<td>{dobToAge(item?.dob, null)} </td>
						<td>
							<Currency class="" amount={item?.base_salary} symbol={get_currency?.currency} />
						</td>
						<td>
							{#if item?.datetime_stop}
								<button class="btn btn-danger btn-sm m-1 p-1">{locale.T('stop_working')}</button>
							{:else}
								<button class="btn btn-primary btn-sm m-1 p-1">{locale.T('working')}</button>
							{/if}
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations bind:n {items} />
	</div>
</div>
