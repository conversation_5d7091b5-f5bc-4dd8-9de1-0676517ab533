<script lang="ts">
	import type { PageServerData } from './$types';
	import { locale } from '$lib/translations/locales.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import SelectMultiiple from '$lib/coms-form/SelectMultiiple.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { get_staff, get_titles, get_roles, locals, get_designations, get_products_department } =
		$derived(data);
	let loading = $state(false);
</script>

<div class="card bg-light">
	<Form class="card-body" reset={false} action="?/create_staff" method="post" bind:loading>
		{#if get_staff?.id}
			<input type="hidden" name="staff_id" value={get_staff?.id} />
		{/if}
		{#if get_staff?.uploads?.id}
			<input type="hidden" name="old_image" value={get_staff?.uploads?.id} />
		{/if}

		<div class="pb-3">
			<input type="hidden" name="old_signature" value={get_staff?.sign ?? ''} />
			<div class=" ">
				<label for="title">{locale.T('title_person')}</label>
				<select class="form-control" name="title_id" id="title_id">
					<option value="">{locale.T('not_selected')}</option>
					{#each get_titles as title}
						<option selected={get_staff?.title_id === title.id} value={title.id}
							>{title.kh} {title.eng}
						</option>
					{/each}
				</select>
			</div>
		</div>
		<div class="pb-3">
			<input type="hidden" name="old_signature" value={get_staff?.sign ?? ''} />
			<div class=" ">
				<label for="designation_id">{locale.T('designation')}</label>
				<select class="form-control" name="designation_id" id="designation_id">
					<option value="">{locale.T('not_selected')}</option>
					{#each get_designations as item}
						<option selected={get_staff?.designation_id === item.id} value={item.id}
							>{item.kh} {item.eng}
						</option>
					{/each}
				</select>
			</div>
		</div>

		{#if locals.roles?.some((e) => e.role?.toLowerCase()?.includes('admin'))}
			<div class="pb-3">
				<label for="staff_type">{locale.T('role')}</label>
				<SelectMultiiple
					name="role_id"
					placeholder="Selects"
					items={get_roles?.map((e) => ({ id: e.id, name: e.role }))}
					value={get_staff?.staffToRole?.map((e) => ({ id: e.role_id, name: e.role?.role }))}
				/>
			</div>
			<div class="pb-3">
				<label for="staff_type">{locale.T('department')}</label>
				<SelectMultiiple
					name="department_id"
					placeholder="Selects"
					items={get_products_department?.map((e) => ({ id: e.id, name: e.products }))}
					value={get_staff?.staffToDemartment?.map((e) => ({
						id: e.department_id,
						name: e.department?.products
					}))}
				/>
			</div>
		{/if}
		<div class="float-end">
			<SubmitButton {loading} />
		</div>
	</Form>
</div>
