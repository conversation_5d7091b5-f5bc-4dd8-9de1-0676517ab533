import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { db } from '$lib/server/db';
import { commune, district, uploads, staff, village } from '$lib/server/schemas';
import logError from '$lib/server/utils/logError';
import { and, eq } from 'drizzle-orm';
import { YYYYMMDD_Format } from '$lib/server/utils';
import { permision } from '$lib/server/auth/permision';
import { fileHandle } from '$lib/server/upload';
export const load: PageServerLoad = async ({ parent, url, locals }) => {
	await parent();
	const get_currency = await db.query.currency.findFirst({});
	const staff_id = url.searchParams.get('staff_id') ?? '';
	const province_id = url.searchParams.get('province_id') ?? '';
	const district_id = url.searchParams.get('district_id') ?? '';
	const commune_id = url.searchParams.get('commune_id') ?? '';
	permision.go({
		locals,
		staff_id: +staff_id,
		redirect_: '/staff'
	});
	const get_provinces = await db.query.provice.findMany({});
	const get_districts = await db.query.district.findMany({
		where: eq(district.province_id, Number(province_id))
	});
	const get_conmunies = await db.query.commune.findMany({
		where: eq(commune.district_id, Number(district_id))
	});
	const get_vilages = await db.query.village.findMany({
		where: eq(village.commune_id, Number(commune_id))
	});
	const get_staff = await db.query.staff.findFirst({
		where: eq(staff.id, +staff_id),
		with: {
			user: true,
			commune: true,
			district: true,
			title: true,
			village: true,
			provice: true,
			staffToRole: {
				with: {
					role: true
				}
			},
			staffToDemartment: {
				with: {
					department: true
				}
			}
		}
	});

	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'staff'), eq(uploads.related_id, +staff_id))
	});
	const get_upload_sign = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'staffSign'), eq(uploads.related_id, +staff_id))
	});
	return {
		get_provinces,
		get_districts,
		get_conmunies,
		get_vilages,
		get_staff: {
			...get_staff,
			uploads: get_upload,
			sign: get_upload_sign
		},
		get_currency
	};
};

export const actions: Actions = {
	create_staff: async ({ request, url }) => {
		const body = await request.formData();
		const {
			staff_id,
			datetime_stop,
			id_staff,
			name_khmer,
			name_latin,
			blood_group,
			telephone,
			specialist,
			gender,
			dob,
			datetime_start,
			base_salary,
			province_id,
			district_id,
			commune_id,
			village_id
		} = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			name_khmer: false,
			name_latin: false,
			telephone: false,
			specialist: false,
			gender: false,
			dob: false,
			datetime_start: false,
			province_id: false,
			district_id: false,
			commune_id: false,
			village_id: false
		};
		if (!name_khmer.trim()) validErr.name_khmer = true;
		if (!name_latin.trim()) validErr.name_latin = true;
		if (!telephone.trim()) validErr.telephone = true;
		if (!specialist.trim()) validErr.specialist = true;
		if (!gender.trim()) validErr.gender = true;
		if (!dob.trim()) validErr.dob = true;
		if (!datetime_start.trim()) validErr.datetime_start = true;
		if (!province_id.trim()) validErr.province_id = true;
		if (!district_id.trim()) validErr.district_id = true;
		if (!commune_id.trim()) validErr.commune_id = true;
		if (!village_id.trim()) validErr.village_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		let staff_id_ = staff_id;
		if (!staff_id_) {
			const create_staff: { id: number }[] = await db
				.insert(staff)
				.values({
					id_staff: id_staff,
					name_khmer: name_khmer,
					name_latin: name_latin,
					telephone: telephone,
					specialist: specialist,
					gender: gender,
					blood_group: blood_group,
					dob: dob,
					base_salary: Number(base_salary),
					datetime_start: YYYYMMDD_Format.datetime(datetime_start),
					province_id: Number(province_id),
					district_id: Number(district_id),
					commune_id: Number(commune_id),
					village_id: Number(village_id)
				})
				.$returningId();
			staff_id_ = create_staff[0].id.toString();
			redirect(
				303,
				`?staff_id=${staff_id_}&province_id=${province_id}&district_id=${district_id}&commune_id=${commune_id}`
			);
		} else {
			await db
				.update(staff)
				.set({
					id_staff: id_staff,
					name_khmer: name_khmer,
					name_latin: name_latin,
					telephone: telephone,
					datetime_stop: datetime_stop ? YYYYMMDD_Format.datetime(datetime_stop) : null,
					specialist: specialist,
					gender: gender,
					base_salary: Number(base_salary),
					blood_group: blood_group,
					dob: dob,
					datetime_start: YYYYMMDD_Format.datetime(datetime_start),
					province_id: Number(province_id),
					district_id: Number(district_id),
					commune_id: Number(commune_id),
					village_id: Number(village_id)
				})
				.where(eq(staff.id, +staff_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		await fileHandle.auto(body);
		// if (image.size) {
		// 	if (old_image) {
		// 		await fileHandle.update(image, old_image, +staff_id_, 'staff');
		// 	} else {
		// 		await fileHandle.insert(image, +staff_id_, 'staff');
		// 	}
		// }
	}
};
