import { db } from '$lib/server/db';
import { eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { salary } from '$lib/server/schemas';

export const load = (async ({ url }) => {
	const staff_id = url.searchParams.get('staff_id') || '';
	const get_salaries = await db.query.salary.findMany({
		where: eq(salary.staff_id, +staff_id),
		with: {
			payroll: true,
			leave: true
		}
	});
	const get_currency = await db.query.currency.findFirst({});
	return {
		get_salaries,
		get_currency
	};
}) satisfies PageServerLoad;
