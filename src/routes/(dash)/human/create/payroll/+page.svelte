<script lang="ts">
	import Currency from '$lib/coms/Currency.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';

	let { data }: { data: PageServerData } = $props();
	let { get_salaries, get_currency } = $derived(data);
	let total_base_salary = $derived(get_salaries.reduce((s, e) => s + Number(e.base_salary), 0));
	let total_allowance = $derived(get_salaries.reduce((s, e) => s + Number(e.allowance), 0));
	let total_bonus = $derived(get_salaries.reduce((s, e) => s + Number(e.bonus), 0));
	let total_deduction = $derived(get_salaries.reduce((s, e) => s + Number(e.deduction), 0));
	let n = $state(1);
</script>

<div class="pb-3 row">
	<div class="col-lg-3 col-md-6 col-sm-12">
		<div class="card text-bg-light text-primary-emphasis">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 30px;">{locale.T('total_base_salary')}</div>
					<div style="font-size: 30px;">
						<Currency class="" amount={total_base_salary} symbol={get_currency?.currency} />
					</div>
				</div>
				<div class="col-auto">
					<i class="fa-solid fa-wallet fa-6x"></i>
				</div>
			</div>
		</div>
	</div>
	<div class="col-lg-3 col-md-6 col-sm-12">
		<div class="card text-bg-light text-info-emphasis">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 30px;">{locale.T('total_allowance')}</div>
					<div style="font-size: 30px;">
						<Currency class="" amount={total_allowance} symbol={get_currency?.currency} />
					</div>
				</div>
				<div class="col-auto">
					<i class="fa-solid fa-sack-dollar fa-6x"></i>
				</div>
			</div>
		</div>
	</div>
	<div class="col-lg-3 col-md-6 col-sm-12">
		<div class="card text-bg-light text-warning-emphasis">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 30px;">{locale.T('total_bonus')}</div>
					<div style="font-size: 30px;">
						<Currency class="" amount={total_bonus} symbol={get_currency?.currency} />
					</div>
				</div>
				<div class="col-auto">
					<i class="fa-solid fa-money-check-dollar fa-6x"></i>
				</div>
			</div>
		</div>
	</div>
	<div class="col-lg-3 col-md-6 col-sm-12">
		<div class="card text-bg-light text-danger-emphasis">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 30px;">{locale.T('total_deduction')}</div>
					<div style="font-size: 30px;">
						<Currency class="" amount={total_deduction} symbol={get_currency?.currency} />
					</div>
				</div>
				<div class="col-auto">
					<i class="fa-solid fa-money-bill-trend-up fa-6x"></i>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="card bg-light">
	<div style="height: {store.inerHight};" class="card-body table-responsive p-0 m-0">
		<table class="table table-bordered table-hover text-nowrap table-light">
			<thead class="sticky-top top-0 bg-light table-active">
				<tr class="text-center">
					<th style="width: 3%;" class="text-center">{locale.T('n')}</th>
					<th>{locale.T('date')}</th>
					<th>{locale.T('base_salary')}</th>
					<th>{locale.T('allowance')}</th>
					<th>{locale.T('bonus')}</th>
					<th>{locale.T('deduction')}</th>
					<th>{locale.T('total_salary')}</th>
					<th>{locale.T('effective_date')}</th>
				</tr>
			</thead>
			<tbody>
				{#each get_salaries as item, index}
					<tr
						class={item.payroll?.payment_date
							? 'table-success text-center'
							: 'table-warning text-center'}
					>
						<td>{n + index}</td>
						<td>
							<DDMMYYYYFormat date={item.effective_date} style="date" />
						</td>
						<td>
							<Currency class="" amount={item.base_salary} symbol={get_currency?.currency} />
						</td>
						<td>
							<Currency class="" amount={item.allowance} symbol={get_currency?.currency} />
						</td>
						<td>
							<Currency class="" amount={item.bonus} symbol={get_currency?.currency} />
						</td>
						<td>
							<Currency class="" amount={item.deduction} symbol={get_currency?.currency} />
						</td>
						<td>
							<Currency class="" amount={item.total_salary} symbol={get_currency?.currency} />
						</td>
						<td>
							<DDMMYYYYFormat date={item.payroll?.payment_date} style="date" />
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations bind:n items={1} />
	</div>
</div>
