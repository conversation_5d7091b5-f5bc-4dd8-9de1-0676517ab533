<script lang="ts">
	import type { PageServerData, ActionData } from './$types';
	import { locale } from '$lib/translations/locales.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { YYYYMMDD_Format } from '$lib/helper';
	import Form from '$lib/coms-form/Form.svelte';
	import Address from '$lib/coms-cu/Address.svelte';
	import SelectMultiiple from '$lib/coms-form/SelectMultiiple.svelte';
	import CropImage from '$lib/coms-form/CropImage.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import Sign from '$lib/coms-report/Sign.svelte';
	interface Props {
		data: PageServerData;
		form: ActionData;
	}
	let { data, form }: Props = $props();
	let {
		get_staff,
		get_provinces,
		get_districts,
		get_conmunies,
		get_titles,
		get_vilages,
		get_roles,
		locals,
		get_designations,
		get_products_department
	} = $derived(data);
	let loading = $state(false);
</script>

<div class="row">
	<div class="col-sm-6">
		<a href="/staff" class="btn btn-link p-0"
			><i class="fa-solid fa-rotate-left"></i>
			{locale.T('back')}
		</a>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/staff" class="btn btn-link p-0 text-secondary">
					<i class="fa-solid fa-user"></i>
					{locale.T('staff')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/user" class="btn btn-link p-0 text-secondary">
					<i class="fa-solid fa-user-lock"></i>
					{locale.T('user')}
				</a>
			</li>
		</ol>
	</div>
</div>
{#if get_staff}
	<div class="card-body bg-light">
		<div class="alert alert-primary">
			<div class="row g-0">
				<div class="col-md-3 bg-dark-subtle justify-content-center card">
					<img
						src={get_staff?.uploads?.filename ? `${get_staff?.uploads?.filename}` : '/no-user.png'}
						alt=""
						class="img-fluid"
					/>
				</div>
				<div class="col-md-3 card justify-content-center">
					<Sign
						left={{
							name:
								locale.L === 'en'
									? get_staff?.title?.eng?.concat(get_staff?.name_latin ?? '')
									: get_staff?.title?.kh?.concat(get_staff?.name_khmer ?? ''),
							role: locale.T('signature_physician'),
							date: new Date().toISOString(),
							img: get_staff?.sign ? get_staff?.sign.filename : '/sign.jpg'
						}}
					/>
				</div>
				<div class="col-md-6">
					<div style="height: 400px;" class="card">
						<div class="card-header">
							<span>{locale.T('description')}</span>
							<DeleteModal action="?/delete_staff" id={get_staff?.id}>
								<input type="hidden" name="image" value={get_staff?.uploads?.filename} />
							</DeleteModal>
							<button
								aria-label="deletemodal"
								type="button"
								class="btn btn-danger btn-sm float-end"
								data-bs-toggle="modal"
								data-bs-target="#delete_modal"
							>
								<i class="fa-solid fa-trash-can"></i>
							</button>
						</div>
						<div class="card-body">
							<div class="row pb-2">
								<div class="col-4">
									<span>{locale.T('name_khmer')}</span>
								</div>
								<div class="col-8">
									<span class="form-control text-start"
										>{get_staff?.name_khmer ?? locale.T('none')}</span
									>
								</div>
							</div>
							<div class="row pb-2">
								<div class="col-4">
									<span>{locale.T('name_latin')}</span>
								</div>
								<div class="col-8">
									<span class="form-control text-start"
										>{get_staff?.name_latin ?? locale.T('none')}</span
									>
								</div>
							</div>
							<div class="row pb-2">
								<div class="col-4">
									<span>{locale.T('specific')}</span>
								</div>
								<div class="col-8">
									<span class="form-control text-start"
										>{get_staff?.specialist ?? locale.T('none')}</span
									>
								</div>
							</div>
							<div class="row pb-2">
								<div class="col-4">
									<span>{locale.T('contact')}</span>
								</div>
								<div class="col-8">
									<span class="form-control text-start"
										>{get_staff?.telephone ?? locale.T('none')}</span
									>
								</div>
							</div>
							<div class="row pb-2">
								<div class="col-4">
									<span>{locale.T('dob')}</span>
								</div>
								<div class="col-8">
									<span class="form-control text-start">
										{#if !get_staff?.dob}
											{locale.T('none')}
										{:else}
											<DDMMYYYYFormat date={get_staff?.dob} style="date" />
										{/if}
									</span>
								</div>
							</div>
							<div class="row pb-2">
								<div class="col-4">
									<span>{locale.T('gender')}</span>
								</div>
								<div class="col-8">
									<span class="form-control text-start">
										{#if get_staff?.gender?.toLowerCase() === 'male'}
											{locale.T('male')}
										{:else if get_staff?.gender?.toLowerCase() === 'female'}
											{locale.T('female')}
										{:else}
											{locale.T('none')}
										{/if}
									</span>
								</div>
							</div>
							<div class="row pb-2">
								<div class="col-4">
									<span>{locale.T('address')}</span>
								</div>
								<div class="col-8">
									{#if get_staff?.village?.type || get_staff?.commune?.type || get_staff?.district?.type || get_staff?.provice?.type}
										<span class="form-control text-start"
											>{get_staff?.village?.type ?? ''}
											{get_staff?.village?.name_khmer ?? ''}
											{get_staff?.commune?.type ?? ''}
											{get_staff?.commune?.name_khmer ?? ''}
											{get_staff?.district?.type ?? ''}
											{get_staff?.district?.name_khmer ?? ''}
											{get_staff?.provice?.type ?? ''}
											{get_staff?.provice?.name_khmer ?? ''}</span
										>
									{:else}
										<span class="form-control text-start">
											{locale.T('none')}
										</span>
									{/if}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- <div class="vr mx-2"></div> -->
		</div>
	</div>
{/if}
<Form
	reset={false}
	action="?/create_staff"
	method="post"
	enctype="multipart/form-data"
	bind:loading
>
	{#if get_staff?.id}
		<input type="hidden" name="staff_id" value={get_staff?.id} />
	{/if}
	{#if get_staff?.uploads?.id}
		<input type="hidden" name="old_image" value={get_staff?.uploads?.id} />
	{/if}
	<div class="card bg-light">
		<div class="card-header fs-5">
			{#if get_staff?.id}
				{locale.T('edit')}
			{:else}
				{locale.T('add')}
			{/if}
		</div>
		<div class="card-body">
			<div class="row pb-3">
				<div class="col-md-6">
					<input value={get_staff?.id} type="hidden" name="patient_id" />
					<input value={get_staff?.uploads?.filename ?? ''} type="hidden" name="old_image" />
					<div class=" ">
						<label for="name_khmer">{locale.T('name_khmer')}</label>
						<input
							value={get_staff?.name_khmer ?? ''}
							required
							minlength="1"
							name="name_khmer"
							type="text"
							class="form-control"
							id="name_khmer"
						/>
					</div>
				</div>
				<div class="col-md-6">
					<div class=" ">
						<label for="name_latin">{locale.T('name_latin')}</label>
						<input
							required
							value={get_staff?.name_latin ?? ''}
							name="name_latin"
							type="text"
							class="form-control"
							id="name_latin"
						/>
					</div>
				</div>
			</div>
			<div class="row pb-3">
				<div class="col-md-6">
					<div class=" ">
						<label for="telephone">{locale.T('contact')}</label>
						<input
							value={get_staff?.telephone ?? ''}
							name="telephone"
							type="text"
							class="form-control"
							id="telephone"
						/>
					</div>
				</div>
				<div class="col-md-6">
					<div class=" ">
						<label for="exampleInputFile">{locale.T('picture')}</label>
						<CropImage
							name="file"
							default_image={get_staff?.uploads?.filename ?? ''}
							related_id={get_staff?.id}
							related_type_="staff"
						/>
					</div>
				</div>
			</div>
			<div class="row pb-3">
				<div class="col-md-3">
					<input type="hidden" name="old_signature" value={get_staff?.sign ?? ''} />
					<div class=" ">
						<label for="title">{locale.T('title_person')}</label>
						<select class="form-control" name="title_id" id="title_id">
							<option value="">{locale.T('not_selected')}</option>
							{#each get_titles as title}
								<option selected={get_staff?.title_id === title.id} value={title.id}
									>{title.kh} {title.eng}
								</option>
							{/each}
						</select>
					</div>
				</div>
				<div class="col-md-3">
					<input type="hidden" name="old_signature" value={get_staff?.sign ?? ''} />
					<div class=" ">
						<label for="designation_id">{locale.T('designation')}</label>
						<select class="form-control" name="designation_id" id="designation_id">
							<option value="">{locale.T('not_selected')}</option>
							{#each get_designations as item}
								<option selected={get_staff?.designation_id === item.id} value={item.id}
									>{item.kh} {item.eng}
								</option>
							{/each}
						</select>
					</div>
				</div>
				<div class="col-md-6">
					<div class=" ">
						<label for="signature">{locale.T('signature')}</label>
						<CropImage
							name="file"
							default_image={get_staff?.sign?.filename ?? ''}
							related_id={get_staff?.id}
							related_type_="staffSign"
						/>
					</div>
				</div>
			</div>
			<div class="row mb-3">
				<div class="col-md-12">
					<label for="specialist">{locale.T('specific')}</label>
					<textarea
						rows="3"
						class="form-control"
						value={get_staff?.specialist}
						name="specialist"
						id="specialist"
					></textarea>
				</div>
			</div>
			{#if locals.roles?.some((e) => e.role?.toLowerCase()?.includes('admin'))}
				<div class="row pb-3">
					<div class="col-md-6">
						<div class=" ">
							<label for="staff_type">{locale.T('role')}</label>
							<SelectMultiiple
								name="role_id"
								placeholder="Selects"
								items={get_roles?.map((e) => ({ id: e.id, name: e.role }))}
								value={get_staff?.staffToRole?.map((e) => ({ id: e.role_id, name: e.role?.role }))}
							/>
						</div>
					</div>
					<div class="col-md-6">
						<div class=" ">
							<label for="staff_type">{locale.T('department')}</label>
							<SelectMultiiple
								name="department_id"
								placeholder="Selects"
								items={get_products_department?.map((e) => ({ id: e.id, name: e.products }))}
								value={get_staff?.staffToDemartment?.map((e) => ({
									id: e.department_id,
									name: e.department?.products
								}))}
							/>
						</div>
					</div>
				</div>
			{/if}
			<div class="row pb-3">
				<div class="col-md-3">
					<div class=" ">
						<label for="gender">{locale.T('gender')}</label>
						<select
							required
							value={get_staff?.gender ?? ''}
							name="gender"
							class="form-control"
							id="gender"
						>
							<option value="Other">{locale.T('none')}</option>
							<option value="Male">{locale.T('male')}</option>
							<option value="Female">{locale.T('female')}</option>
						</select>
					</div>
				</div>
				<div class="col-md-3">
					<div class=" ">
						<label for="blood_group">{locale.T('blood_group')}</label>
						<select
							value={get_staff?.blood_group ?? ''}
							name="blood_group"
							class="form-control"
							id="blood_group"
						>
							<option value="">Other</option>

							<option value="A-">A-</option>
							<option value="A+">A+</option>

							<option value="AB+">AB+</option>
							<option value="AB-">AB-</option>

							<option value="B-">B-</option>
							<option value="B+">B+</option>

							<option value="O-">O-</option>
							<option value="O+">O+</option>
						</select>
					</div>
				</div>
				<div class="col-md-6">
					<div class=" ">
						<label for="dob">{locale.T('dob')}</label>
						<input value={get_staff?.dob} name="dob" type="date" class="form-control" id="dob" />
					</div>
				</div>
			</div>
			<div class="row pb-3">
				<div class="col-md-3">
					<div class=" ">
						<label for="datetime_start">{locale.T('start_working')}</label>
						<input
							value={YYYYMMDD_Format.datetime(get_staff?.datetime_start)}
							name="datetime_start"
							type="datetime-local"
							class="form-control"
							id="datetime_start"
						/>
					</div>
				</div>
				<div class="col-md-3">
					<div class=" ">
						<label for="datetime_stop">{locale.T('stop_working')}</label>
						<input
							value={YYYYMMDD_Format.datetime(get_staff?.datetime_stop)}
							name="datetime_stop"
							type="datetime-local"
							class="form-control"
							id="datetime_stop"
						/>
					</div>
				</div>
				<div class="col-md-6">
					<div class=" ">
						<label for="id_staff">{locale.T('id')} {locale.T('staff')} </label>
						<input
							value={get_staff?.id_staff}
							name="id_staff"
							type="text"
							class="form-control"
							id="id_staff"
						/>
					</div>
				</div>
			</div>
			<Address
				data={{ get_conmunies, get_districts, get_provinces, get_vilages }}
				defaultValue={{
					province_id: get_staff?.provice?.id ?? null,
					district_id: get_staff?.district?.id ?? null,
					commune_id: get_staff?.commune?.id ?? null,
					village_id: get_staff?.village?.id ?? null
				}}
			/>

			<div class="row pb-3"></div>
		</div>
		<div class="card-footer">
			<div class="float-end">
				<SubmitButton {loading} />
			</div>
		</div>
	</div>
</Form>
<br />

{#if get_staff}
	<Form action={get_staff.user?.id ? '?/update_user' : '?/register'} method="post" bind:loading>
		<div class="card bg-light">
			<div class="card-header fs-5">
				{locale.T('change_password')}
			</div>
			<div class="card-body">
				{form?.message}
				<div class="pb-3">
					<input value={get_staff?.user?.id} type="hidden" name="user_id" />
					<input value={get_staff?.id} type="hidden" name="staff_id" />
					<div class=" ">
						<label for="username">{locale.T('username')}</label>
						{#if form?.username}
							<span class="text-danger">{locale.T('invalid_username')}</span>
						{/if}
						<input
							value={get_staff?.user?.username ?? ''}
							required
							name="username"
							type="text"
							class="form-control"
							id="username"
						/>
					</div>
				</div>
				{#if get_staff?.user?.id}
					{#if !locals?.roles?.some((e) => e.role?.toLowerCase()?.includes('admin'))}
						<div>
							<div class="pb-3">
								<label for="confirm_password">{locale.T('password')}</label>
								{#if form?.confirm_password}
									<span class="text-danger">{locale.T('wrong_password')}</span>
								{/if}

								<input
									placeholder="*****"
									required
									name="confirm_password"
									type="password"
									class="form-control"
									id="confirm_password"
								/>
							</div>
						</div>
					{/if}
				{/if}

				<div>
					<div class=" ">
						<label for="password">{locale.T('new_password')}</label>
						{#if form?.password}
							<span class="text-danger">{locale.T('wrong_password')}</span>
						{/if}
						<input
							placeholder="*****"
							required
							name="password"
							type="password"
							class="form-control"
							id="password"
						/>
					</div>
				</div>
			</div>
			<div class="card-footer">
				<div class="float-end">
					<SubmitButton {loading} />
				</div>
			</div>
		</div>
	</Form>
{/if}
<br />
