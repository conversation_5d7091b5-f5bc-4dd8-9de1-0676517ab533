<script lang="ts">
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import { YYYYMMDD_Format } from '$lib/helper';
	import type { PageServerData } from './$types';
	import Form from '$lib/coms-form/Form.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_appointment } = $derived(data);
	let datetime = $state(
		data.get_appointment?.datetime ? YYYYMMDD_Format.datetime(data.get_appointment?.datetime) : ''
	);
	let loading = $state(false);
</script>

<DeleteModal action="?/delete_appointment" id={get_appointment?.id} />
<div class="card bg-light">
	<div class="card-header fs-5">
		<span># {locale.T('appintment')}</span>
	</div>
	<Form bind:loading action="?/create_appointment" method="post">
		<div class="card-body">
			<div class="row">
				<div class="col-sm-4 p-2">
					<div class=" row mx-2">
						<label for="datetime" class="form-label">{locale.T('date')}</label>
						<div class="input-group">
							<input
								bind:value={datetime}
								id="datetime"
								name="datetime"
								type="datetime-local"
								class="form-control"
							/>
						</div>
						<label for="description" class="form-label">{locale.T('description')}</label>
						<div class="input-group">
							<textarea
								value={get_appointment?.description ?? ''}
								id="description"
								name="description"
								rows="6"
								class="form-control"
							></textarea>
						</div>
					</div>
					<div class="float-end p-4">
						<SubmitButton {loading} />
					</div>
				</div>
				<div class="col-sm-8 p-2">
					<div class=" table-sm table-responsive">
						<table class="table table-bordered table-hover text-nowrap table-light">
							<thead>
								<tr>
									<th>{locale.T('date')}</th>
									<th>{locale.T('description')}</th>
									<th></th>
								</tr>
							</thead>
							<tbody class="table-sm">
								{#if get_appointment}
									<tr>
										<td style="width: 20%;">
											<DDMMYYYYFormat date={get_appointment.datetime} />
										</td>
										<td class="text-break">{get_appointment?.description ?? ''}</td>
										<td style="width: 10%;">
											<div>
												<button
													aria-label="deletemodal"
													type="button"
													class="btn btn-danger btn-sm"
													data-bs-toggle="modal"
													data-bs-target="#delete_modal"
													><i class="fa-solid fa-trash-can"></i>
												</button>
											</div>
										</td>
									</tr>
								{/if}
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</Form>
</div>
