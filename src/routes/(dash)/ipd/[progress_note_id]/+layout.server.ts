import type { LayoutServerLoad } from './$types';
import { db } from '$lib/server/db';
import { progressNote } from '$lib/server/schemas';
import { eq } from 'drizzle-orm';
import { redirect } from '@sveltejs/kit';
export const load: LayoutServerLoad = async ({ params, parent }) => {
	await parent();
	const { progress_note_id } = params;
	if (isNaN(+progress_note_id)) redirect(303, '/patient/all');
	const get_progress_note = await db.query.progressNote.findFirst({
		where: eq(progressNote.id, Number(progress_note_id)),
		with: {
			activeDepartment: {
				with: {
					activeBed: true
				}
			},
			activeBed: {
				with: {
					bed: {
						with: {
							ward: true,
							room: {
								with: {
									product: true
								}
							}
						}
					}
				}
			},
			presrciption: true,
			visit: {
				with: {
					department: true,
					physicalExam: true,
					subjective: true,
					vitalSign: true
				}
			},
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			billing: true,
			service: {
				with: {
					operationProtocol: true,
					product: true
				}
			}
		}
	});
	if (!get_progress_note || get_progress_note.status === 'LOADING') redirect(303, '/patient/all');
	const patient_info = {
		...get_progress_note?.patient,
		date_checkup: get_progress_note?.date_checkup
	};
	return {
		get_progress_note,
		patient_info
	};
};
