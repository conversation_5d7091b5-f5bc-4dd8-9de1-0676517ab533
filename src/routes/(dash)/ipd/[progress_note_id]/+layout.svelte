<script lang="ts">
	import { page } from '$app/state';
	import SendBillingToPayment from '$lib/coms-billing/SendBillingToPayment.svelte';
	import PatientInfo from '$lib/coms-ipd-opd/PatientInfo.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { LayoutServerData } from './$types';
	let progress_note_id = page.params.progress_note_id;
	interface Props {
		data: LayoutServerData;
		children?: import('svelte').Snippet;
	}

	let { data, children }: Props = $props();
	let { get_progress_note, patient_info } = $derived(data);
	let billing = $derived(get_progress_note?.billing);
	let active_department = $derived(
		get_progress_note?.activeDepartment.find((e) => e.active === true)
	);
	let active_bed = $derived(active_department?.activeBed.find((e) => e.active === true));
</script>

<div class="row">
	<div class="col-sm-6">
		<a href="/patient/ipd" class="btn btn-link m-0 p-0"
			><i class="fa-solid fa-rotate-left"></i>
			{locale.T('back')}
		</a>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/patient/all" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-restroom"></i>
					{locale.T('patient')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/patient/all" class="btn btn-link p-0 text-secondary">
					<i class="nav-icon fas fa-procedures"></i>
					{locale.T('ipd')}
				</a>
			</li>
		</ol>
	</div>
</div>
<PatientInfo {patient_info} />

<div class="row pt-2">
	<div data-sveltekit-noscroll class="col-sm-12">
		<a
			href="/ipd/{progress_note_id}/progress-note "
			class={page.url.pathname.includes('progress-note')
				? 'btn btn-primary mb-2'
				: 'btn btn-outline-primary  mb-2'}>Progress Note</a
		>
		<a
			href="/ipd/{progress_note_id}/nursing-process"
			class={page.url.pathname.includes('nursing-process')
				? 'btn btn-primary mb-2'
				: 'btn btn-outline-primary mb-2'}>Nursing Process</a
		>
		<a
			href="/ipd/{progress_note_id}/vital-sign "
			class={page.url.pathname.includes('vital-sign')
				? 'btn btn-primary mb-2'
				: 'btn btn-outline-primary mb-2'}>Vital Sign</a
		>
		<a
			href="/ipd/{progress_note_id}/service "
			class={page.url.pathname.includes('service')
				? 'btn btn-primary mb-2'
				: 'btn btn-outline-primary mb-2'}>Services</a
		>
		<a
			href="/ipd/{progress_note_id}/assessment "
			class={page.url.pathname.includes('assessment')
				? 'btn btn-primary mb-2'
				: 'btn btn btn-outline-primary mb-2'}>Assessment</a
		>
		<a
			href="/ipd/{progress_note_id}/discharge-summary"
			class={page.url.pathname.includes('discharge-summary')
				? 'btn btn-primary mb-2'
				: 'btn btn-outline-primary mb-2'}>Discharge Summary</a
		>
		<a
			href="/ipd/{progress_note_id}/prescription"
			class={page.url.pathname.includes('prescription')
				? 'btn btn-primary mb-2'
				: 'btn btn-outline-primary mb-2'}>Prescription</a
		>
		<a
			href="/ipd/{progress_note_id}/appointment"
			class={page.url.pathname.includes('appointment')
				? 'btn btn-primary mb-2'
				: 'btn btn-outline-primary mb-2'}>Appointment</a
		>
		<a
			href="/ipd/{progress_note_id}/document?title=accept_leaving"
			class={page.url.pathname.includes('document')
				? 'btn btn-primary mb-2'
				: 'btn btn-outline-primary mb-2'}>Document</a
		>
		<div class="float-end">
			<SendBillingToPayment
				action={get_progress_note?.date_checkout
					? '/ipd/checkout/?/dis_check_out'
					: '/ipd/checkout/?/check_out'}
				checking_title="<i class='fa-solid fa-comments-dollar'></i> {locale.T(
					'discharge_and_go_home'
				)}"
				paid_title="<i class='fa-solid fa-circle-check'></i> {locale.T('already_paid')}"
				btn_size="btn-md"
				status={billing?.status}
			>
				<input type="hidden" name="active_department_id" value={active_department?.id} />
				<input type="hidden" name="active_bed_id" value={active_bed?.id} />
				<input type="hidden" name="progress_note_id" value={get_progress_note.id} />
				<ul class="list-group pb-2">
					<li class="list-group-item text-danger">
						<i class="fa-solid fa-triangle-exclamation"></i>
						{locale.T('please_verify_before_sending')}
					</li>
				</ul>
				<div class="alert alert-primary py-1">
					<div>
						<span>{locale.T('date_checkin')}</span> &nbsp; -
						<DDMMYYYYFormat date={get_progress_note?.date_checkup} />
					</div>
					<div>
						<span>{locale.T('date_checkout')}</span>&nbsp; -
						<DDMMYYYYFormat date={new Date().toJSON()} />
					</div>
				</div>
			</SendBillingToPayment>
		</div>
	</div>
</div>
{@render children?.()}
