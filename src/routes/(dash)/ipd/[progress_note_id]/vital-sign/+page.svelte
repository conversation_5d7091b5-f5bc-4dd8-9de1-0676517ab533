<script lang="ts">
	import CreateVitalSignIpd from '$lib/coms-cu/CreateVitalSignIPD.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import Name from '$lib/coms/Name.svelte';
	import type { PageServerData } from './$types';
	import Graphs from '$lib/coms/Graphs.svelte';
	import { DDMMYYYY_Format } from '$lib/helper';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let vital_sign_id: number | null = $state(null);
	let { get_vital_sing_ipd, get_progress_note, get_vital_from_active_department } = $derived(data);
	let find_vital_sind = $derived(get_vital_sing_ipd.filter((e) => e.id === vital_sign_id));
</script>

<CreateVitalSignIpd
	data={{
		get_vital_sing_ipd: find_vital_sind
	}}
	bind:vital_sign_id
/>
<DeleteModal action="?/delete_vital_sign" id={vital_sign_id!} />
<!-- VitalSign  -->

<div class="card bg-light">
	<div class="card-header fs-5">
		<div class="row">
			<div class="col">
				<span># Vital Sign</span>
			</div>
			<div class="col-auto">
				<a
					target="_blank"
					aria-label="nersing_process"
					href="/print/{get_progress_note?.id}/vital-sign"
					class="btn btn-success btn-sm"
					><i class="fa-solid fa-print"></i>
				</a>
				<button
					aria-label="create_vital_sign_ipd"
					onclick={() => (vital_sign_id = null)}
					type="button"
					class="btn btn-success btn-sm"
					data-bs-toggle="modal"
					data-bs-target="#create_vital_sign_ipd"
					><i class="fa-solid fa-square-plus"></i>
				</button>
			</div>
		</div>
	</div>
	{#each get_vital_from_active_department || [] as item_0 (item_0.id)}
		{@const vital_signs = item_0.vitalSign || []}
		{@const datasets_a = [
			{
				label: 'SBP',
				borderColor: ['blue'],
				backgroundColor: ['blue'],
				data: vital_signs?.map((e) => e.sbp!)
			},
			{
				label: 'DBP',
				borderColor: ['orange'],
				backgroundColor: ['orange'],
				data: vital_signs?.map((e) => e.dbp!)
			},
			{
				label: 'PULSE',
				borderColor: ['green'],
				backgroundColor: ['green'],
				data: vital_signs?.map((e) => e.pulse!)
			}
		]}
		{@const datasets_b = [
			{
				label: 'Temperature',
				backgroundColor: ['coral'],
				data: vital_signs?.map((e) => e.t!)
			},
			{
				label: 'Respiratory Rate',
				backgroundColor: ['deeppink'],
				data: vital_signs?.map((e) => e.rr!)
			}
		]}
		{@const labels = vital_signs.map((e) => [
			DDMMYYYY_Format(e.datetime!, 'date'),
			DDMMYYYY_Format(e.datetime!, 'time')
		])}

		<div class="card-body table-responsive p-0">
			<table class="table mb-0 table-bordered table-hover text-break text-wrap table-light">
				<thead class="table-active">
					<tr class="text-center table-active">
						<th colspan="19">
							{item_0.department?.products ?? ''}
						</th>
					</tr>
					<tr class="text-center">
						<th>Date</th>
						<th>Time</th>
						<th>By</th>
						<th>BP (mmHg)</th>
						<th>Pulse (min)</th>
						<th>T (<sup>o</sup>C)</th>
						<th>RR</th>
						<th>SpO2 (%)</th>
						<th>WG (kg)</th>
						<th>Piv</th>
						<th>Drink</th>
						<th>NG_in</th>
						<th>NG_out</th>
						<th>Fluid_out</th>
						<th>Vomiting</th>
						<th>Stool</th>
						<th>Urine</th>
						<th style="width: 200px;">Note</th>
						<th></th>
					</tr>
				</thead>
				<tbody>
					{#each vital_signs as item (item.id)}
						<tr class="text-center">
							<td>
								<DDMMYYYYFormat style="date" date={item.datetime} />
							</td>
							<td>
								<DDMMYYYYFormat style="time" date={item.datetime} />
							</td>
							<td>
								<Name name={item.by} />
							</td>
							<td
								>{item.sbp?.toFixed(0).concat(' /') ?? ''}
								{item.dbp?.toFixed(0).concat(' mmHg') ?? ''}</td
							>
							<td>{item.pulse ?? ''}</td>
							<td>{item.t?.toFixed(1) ?? ''}</td>
							<td>{item.rr ?? ''}</td>
							<td>{item.sp02 ?? ''}</td>
							<td>{item.weight ?? ''}</td>
							<td>{item.piv ?? ''}</td>
							<td>{item.drink ?? ''}</td>
							<td>{item.nasogastric_tube_in ?? ''}</td>
							<td>{item.nasogastric_tube_out ?? ''}</td>
							<td>{item.fluid_out ?? ''}</td>
							<td>{item.vomiting ?? ''}</td>
							<td>{item.stool ?? ''}</td>
							<td>{item.urine ?? ''}</td>
							<td class="text-left">{item.note ?? ''}</td>
							<td>
								<div>
									<button
										aria-label="createvitalsignipd"
										class="btn btn-primary btn-sm"
										type="button"
										data-bs-toggle="modal"
										data-bs-target="#create_vital_sign_ipd"
										onclick={() => {
											vital_sign_id = item.id;
										}}
										><i class="fa-solid fa-file-pen"></i>
									</button>
									<button
										aria-label="deletemodal"
										class="btn btn-danger btn-sm"
										type="button"
										data-bs-toggle="modal"
										data-bs-target="#delete_modal"
										onclick={() => {
											vital_sign_id = item.id;
										}}
										><i class="fa-solid fa-trash-can"></i>
									</button>
								</div>
							</td>
						</tr>
					{/each}
					<tr>
						<td colspan="19">
							<div class="row g-0">
								<div class="col-6">
									<Graphs
										type="line"
										title="Vital Sign"
										datasets={datasets_a}
										labels={labels.sort()}
									/>
								</div>
								<div class="col-6">
									<Graphs
										type="bar"
										title="Respiratory Rate and Temperature"
										datasets={datasets_b}
										{labels}
									/>
								</div>
							</div>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	{/each}
</div>
