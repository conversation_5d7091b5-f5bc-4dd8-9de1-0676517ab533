import { db } from '$lib/server/db';
import { asc, desc, eq } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { progressNote, vitalSign, activeDepartment } from '$lib/server/schemas';
import { YYYYMMDD_Format } from '$lib/server/utils';
import logError from '$lib/server/utils/logError';
import { fail } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ params }) => {
	const progress_note_id = params.progress_note_id;
	const get_progress_note = await db.query.progressNote.findFirst({
		where: eq(progressNote.id, +progress_note_id),
		with: {
			activeDepartment: {
				with: {
					department: true
				},
				orderBy: asc(activeDepartment.datetime_in)
			},
			vitalSign: {
				orderBy: asc(vitalSign.datetime)
			}
		}
	});
	const get_vital_sing_ipd = await db.query.vitalSign.findMany({
		where: eq(vitalSign.progress_note_id, +progress_note_id)
	});
	const get_vital_from_active_department = await db.query.activeDepartment.findMany({
		where: eq(activeDepartment.progress_note_id, +progress_note_id),
		with: {
			vitalSign: {
				with: {
					by: true
				},
				orderBy: asc(vitalSign.datetime)
			},
			department: true
		},
		orderBy: desc(activeDepartment.datetime_in)
	});
	return {
		get_vital_sing_ipd,
		get_progress_note,
		get_vital_from_active_department
	};
};

export const actions: Actions = {
	create_vital_sign_ipd: async ({ request, params, locals, url }) => {
		const body = await request.formData();
		const progress_note_id = params.progress_note_id;
		const get_progress_note = await db.query.progressNote.findFirst({
			where: eq(progressNote.id, +progress_note_id),
			with: {
				activeDepartment: {
					with: {
						department: true
					}
				}
			}
		});
		const get_active_department = get_progress_note?.activeDepartment.find(
			(e) => e.active === true
		);
		if (!progress_note_id || !get_active_department)
			return fail(400, { message: 'Progress note not found' });
		const {
			sbp,
			dbp,
			pulse,
			t,
			rr,
			sp02,
			weight,
			piv,
			drink,
			nasogastric_tube_in,
			nasogastric_tube_out,
			fluid_out,
			vomiting,
			stool,
			urine,
			note,
			time,
			date,
			vital_sign_id
		} = Object.fromEntries(body);
		if (vital_sign_id) {
			await db
				.update(vitalSign)
				.set({
					datetime: date.toString().slice(0, 11).concat(' ').concat(time.toString()),
					progress_note_id: +progress_note_id,
					sbp: +sbp,
					dbp: +dbp,
					pulse: +pulse,
					t: +t,
					rr: +rr,
					sp02: +sp02,
					weight: +weight,
					piv: +piv,
					drink: +drink,
					nasogastric_tube_in: +nasogastric_tube_in,
					nasogastric_tube_out: +nasogastric_tube_out,
					fluid_out: +fluid_out,
					vomiting: +vomiting,
					stool: stool.toString() ?? '',
					urine: urine.toString() ?? '',
					note: note.toString() ?? ''
				})
				.where(eq(vitalSign.id, +vital_sign_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		if (!vital_sign_id) {
			await db
				.insert(vitalSign)
				.values({
					datetime: YYYYMMDD_Format.datetime(new Date()),
					by: locals.user?.staff_id,
					progress_note_id: +progress_note_id,
					sbp: sbp ? +sbp : null,
					dbp: dbp ? +dbp : null,
					pulse: pulse ? +pulse : null,
					t: t ? +t : null,
					rr: rr ? +rr : null,
					sp02: sp02 ? +sp02 : null,
					weight: weight ? +weight : null,
					piv: piv ? +piv : null,
					drink: drink ? +drink : null,
					nasogastric_tube_in: nasogastric_tube_in ? +nasogastric_tube_in : null,
					nasogastric_tube_out: nasogastric_tube_out ? +nasogastric_tube_out : null,
					fluid_out: fluid_out ? +fluid_out : null,
					vomiting: vomiting ? +vomiting : null,
					stool: stool.toString() ?? '',
					urine: urine.toString() ?? '',
					note: note.toString() ?? '',
					active_department_id: get_active_department.id
				})
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
	},
	delete_vital_sign: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.delete(vitalSign)
			.where(eq(vitalSign.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
