<script lang="ts">
	import type { ActionData, PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import PastePrescription from '$lib/coms-ipd-opd/PastePrescription.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import CreateAWord from '$lib/coms-cu/CreateAWord.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import { page } from '$app/state';
	import Words from '$lib/coms-cu/Words.svelte';
	interface Props {
		data: PageServerData;
		form: ActionData;
	}

	let { data, form }: Props = $props();
	let loading = $state(false);
	let {
		get_categories,
		get_uses,
		get_prescriptions,
		get_prescription,
		get_durations,
		get_advice_teaching,
		get_groups,
		get_products,
		get_words
	} = $derived(data);
	let prescription_id: number | null = $state(null);
	let product_id: number | null = $state(null);
	let category_id: number | null = $state(null);
	let group_id: number | null = $state(null);
	let get_product = $derived(get_products.find((e) => e.id === product_id));
	$effect(() => {
		if (get_prescription) {
			product_id = get_prescription?.product_id || null;
			group_id = get_prescription?.product?.group_id || null;
			category_id = get_prescription?.product?.category_id || null;
		}
		if (page.url.searchParams.get('product_id')) {
			product_id = Number(page.url.searchParams.get('product_id'));
		}
	});
	let description = $derived(get_advice_teaching?.description ?? '');
</script>

<DeleteModal action="?/delete_prescription" id={prescription_id!} />
<CreateAWord
	title={locale.T('use')}
	modal="create_use"
	actionCreate="?/create_use"
	actionDelete="?/delete_use"
	data={get_uses.map((e) => ({ id: e.id, word: e?.description }))}
/>
<CreateAWord
	title={locale.T('duration')}
	modal="create_duration"
	actionCreate="?/create_duration"
	actionDelete="?/delete_duration"
	data={get_durations.map((e) => ({ id: e.id, word: e?.description }))}
/>
<Words
	name="Advice teaching"
	bind:value={description}
	words={get_words}
	modal_name_type="advice_teaching"
	category="prescription"
/>
<div class="card bg-light">
	<div class="card-header">
		<div class="row g-1">
			<div class="col fs-5">
				<span># {locale.T('prescription')}</span>
			</div>
			<div class="col-auto">
				<PastePrescription action="?/paste_prescription" />
			</div>
		</div>
	</div>
	<div class="alert alert-secondary mb-0 rounded-0">
		<Form
			action={get_prescription?.id ? '?/update_prescription' : '?/create_prescription'}
			bind:loading
			fnSuccess={() => (product_id = null)}
			method="post"
		>
			<input value={get_prescription?.id || ''} type="hidden" name="prescription_id" />

			<div class="row pb-3">
				<div class="col-6">
					<div class="row g-1">
						<div class="col-sm-3">
							<label for="select_category">{locale.T('category')}</label>
							<SelectParam
								placeholder={locale.T('select')}
								name="category_id"
								bind:value={category_id}
								items={get_categories.map((e) => ({ id: e.id, name: e.name }))}
							/>
						</div>
						<div class="col-sm-3">
							<label for="select_group">{locale.T('product_group')}</label>
							<SelectParam
								placeholder={locale.T('select')}
								name="group_id"
								bind:value={group_id}
								items={get_groups.map((e) => ({ id: e.id, name: e.name }))}
							/>
						</div>
						<div class="col-sm-6">
							<label for="select_prodcts">{locale.T('products')} </label>
							<SelectParam
								q_name="q"
								name="product_id"
								placeholder={locale.T('select')}
								bind:value={product_id}
								items={get_products?.map((e) => ({
									id: e.id,
									name: e.products.concat(`
										<span class="${e.qty_available ? 'text-success' : 'text-danger'} float-end" >
										${e.qty_available} 
										${e.unit?.unit ?? ''} 
										<span/>
										`)
								}))}
							/>
							{#if form?.product_id}
								<div class="text-danger">{locale.T('input_data')}</div>
							{/if}
						</div>
					</div>
				</div>
				<div class="col-6">
					<div class="row g-1">
						<div class="col-lg">
							<label for="">{locale.T('morning')}</label>
							<input
								value={get_prescription?.morning === 0 ? '' : (get_prescription?.morning ?? '')}
								id="morning"
								name="morning"
								type="number"
								step="any"
								class="form-control"
							/>
						</div>
						<div class="col-lg">
							<label for="">{locale.T('noon')}</label>
							<input
								value={get_prescription?.noon === 0 ? '' : (get_prescription?.noon ?? '')}
								id="noon"
								name="noon"
								step="any"
								type="number"
								class="form-control"
							/>
						</div>
						<div class="col-lg">
							<label for="">{locale.T('afternoon')}</label>
							<input
								value={get_prescription?.afternoon === 0 ? '' : (get_prescription?.afternoon ?? '')}
								id="afternoon"
								name="afternoon"
								step="any"
								type="number"
								class="form-control"
							/>
						</div>
						<div class="col-lg">
							<label for="">{locale.T('evening')}</label>
							<input
								value={get_prescription?.evening === 0 ? '' : (get_prescription?.evening ?? '')}
								id="evening"
								step="any"
								name="evening"
								type="number"
								class="form-control"
							/>
						</div>
						<div class="col-lg">
							<label for="">{locale.T('night')}</label>
							<input
								value={get_prescription?.night === 0 ? '' : (get_prescription?.night ?? '')}
								id="night"
								step="any"
								name="night"
								type="number"
								class="form-control"
							/>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-6">
					<div class="row g-1">
						<div class="col-sm-6">
							<div class="row g-1">
								<div class="col-auto">
									<label for=""></label>
									<button
										aria-label="createuse"
										type="button"
										data-bs-toggle="modal"
										data-bs-target="#create_use"
										class="form-control text-light border-0 shadow-none bg-primary"
										onclick={() => document.getElementById('close_create_prescription')?.click()}
										><i class="fa-regular fa-bookmark"></i>
									</button>
								</div>
								<div class="col-lg">
									<label for="use">{locale.T('use')}</label>
									<SelectParam
										placeholder={locale.T('select')}
										name="use"
										value={get_prescription?.use ?? ''}
										items={get_uses.map((e) => ({ id: e.description, name: e.description }))}
									/>
								</div>
							</div>
						</div>
						<div class="col-sm-6">
							<div class="row g-1">
								<div class="col-auto">
									<label for=""></label>
									<button
										aria-label="createduration"
										type="button"
										data-bs-toggle="modal"
										data-bs-target="#create_duration"
										class="form-control text-light border-0 shadow-none bg-primary"
										><i class="fa-regular fa-bookmark"></i>
									</button>
								</div>
								<div class="col-lg">
									<label for="duration">{locale.T('duration')}</label>
									<SelectParam
										placeholder={locale.T('select')}
										name="duration"
										value={get_prescription?.duration ?? ''}
										items={get_durations.map((e) => ({
											id: e.description,
											name: e.description
										}))}
									/>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-6">
					<div class="row g-1">
						<div class="col-sm-6">
							<label for="Amount">{locale.T('amount')}</label>
							<div class="input-group">
								<input
									value={get_prescription?.amount ?? ''}
									id="Amount"
									name="amount"
									type="number"
									class="form-control"
								/>
								<select
									required
									value={get_prescription?.unit_id?.toString() || ''}
									class="form-control"
									name="unit_id"
									id="unit_id"
								>
									<option value={String(get_product?.unit_id) || ''}
										>{get_product?.unit?.unit ?? locale.T('not_selected')}
									</option>
									{#each get_product?.subUnit || [] as item}
										<option value={String(item.unit_id)}>{item.unit.unit ?? ''}</option>
									{/each}
								</select>
							</div>
						</div>
						<div class="col-sm-6">
							<label for=""></label>
							<button type="submit" class="form-control text-bg-primary border-0">
								{#if get_prescription}
									{locale.T('update')}
								{:else}
									{locale.T('save')}
								{/if}
							</button>
						</div>
					</div>
				</div>
			</div>
		</Form>
	</div>
	<div class="card-body table-responsive p-0">
		<table class="table mb-0 table-bordered table-hover text-nowrap table-light">
			<thead class="table-active">
				<tr>
					<th>{locale.T('n')}</th>
					<th>{locale.T('medicine')}</th>
					<th>{locale.T('use')}</th>
					<th>{locale.T('time_to_use')}</th>
					<th class="text-center">{locale.T('duration')}</th>
					<th class="text-center">{locale.T('amount')}</th>
					<th></th>
				</tr>
			</thead>
			<tbody class="table-sm">
				{#each get_prescriptions as item, index}
					<tr
						class:table-active={item.id === prescription_id}
						class:table-primary={item.product_id === product_id}
					>
						<td>{index + 1}</td>
						<td
							>{item.product?.products} <br />
							<span class="badge text-bg-primary">{item.product?.generic_name ?? ''}</span>
						</td>

						<td>
							{item.use ?? ''}
						</td>
						<td>
							<div>
								<span class="badge text-bg-warning">
									{#if item.morning !== 0}
										{locale.T('morning')} {item.morning}
									{/if}
								</span>
								<span class="badge text-bg-warning">
									{#if item.noon !== 0}
										{locale.T('noon')} {item.noon}
									{/if}
								</span>
								<span class="badge text-bg-warning">
									{#if item.afternoon !== 0}
										{locale.T('afternoon')} {item.afternoon}
									{/if}
								</span>
								<span class="badge text-bg-warning">
									{#if item.evening !== 0}
										{locale.T('evening')} {item.evening}
									{/if}
								</span>
								<span class="badge text-bg-warning">
									{#if item.night !== 0}
										{locale.T('night')} {item.night}
									{/if}
								</span>
							</div>
						</td>
						<td class="text-center">{item.duration ?? ''}</td>
						<td class="text-center">{item.amount} {item?.unit?.unit} </td>
						<td>
							<div>
								<a
									data-sveltekit-noscroll
									href="?group_id={item.product?.group_id}&category_id={item.product
										?.category_id}&prescription_id={item.id}"
									aria-label="createprescription"
									class="btn btn-primary btn-sm"
									><i class="fa-solid fa-file-pen"></i>
								</a>
								<button
									aria-label="deletemodal"
									onclick={() => {
										prescription_id = item.id;
									}}
									type="button"
									class="btn btn-danger btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#delete_modal"
									><i class="fa-solid fa-trash-can"></i>
								</button>
							</div>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
</div>
<br />
<Form data_sveltekit_keepfocus={true} bind:loading action="?/create_advice_teaching" method="post">
	<div class="card bg-light">
		<div class="card-header fs-5">
			<span># {locale.T('advice_or_teaching')}</span>
		</div>
		<div class="card-body">
			<div class=" row">
				<div class="col-auto">
					<button
						data-bs-toggle="modal"
						data-bs-target="#advice_teaching"
						type="button"
						class="btn btn-outline-primary btn-sm">{locale.T('description')}</button
					>
				</div>
				<div class="col">
					<!-- <label for="cheif_coplaint" class="col-sm-3 col-form-label">Cheif complaint</label> -->
					<textarea bind:value={description} rows="4" class="form-control" name="advice_teaching">
					</textarea>
					<!-- <textarea
							value={get_advice_teaching?.description ?? ''}
							id="advice_teaching"
							name="advice_teaching"
							rows="5"
							class="form-control"
						></textarea> -->
				</div>
			</div>
		</div>
		<div class="card-footer text-end">
			<SubmitButton {loading} />
		</div>
	</div>
</Form>
