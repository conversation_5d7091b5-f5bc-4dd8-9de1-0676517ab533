<script lang="ts">
	import Form from '$lib/coms-form/Form.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	import ServiceContract from '$lib/coms-document/ServiceContract.svelte';
	import RequestDischarge from '$lib/coms-document/RequestDischarge.svelte';
	import PatientTranswer from '$lib/coms-document/PatientTranswer.svelte';
	import BirthCertificate from '$lib/coms-document/BirthCertificate.svelte';
	import BirthRecognize from '$lib/coms-document/BirthRecognize.svelte';
	import ResultChecking from '$lib/coms-document/ResultChecking.svelte';
	import { dobToAge } from '$lib/helper';
	import AcceptLeaving from '$lib/coms-document/AcceptLeaving.svelte';
	import Print from '$lib/coms-document/Print.svelte';
	import { page } from '$app/state';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import CropImage from '$lib/coms-form/CropImage.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	let { data }: { data: PageServerData } = $props();
	let {
		get_documents,
		get_progress_note,
		get_words,
		get_clinich_info,
		get_upload,
		get_document,
		get_document_setting
	} = $derived(data);
	let loading = $state(false);
	let title = $derived(page.url.searchParams.get('title') ?? '');
	let birth_certificate_1 = $derived(get_documents.find((e) => e.title === 'birth_certificate_1'));
</script>

<DeleteModal action="?/delete_document" id={get_document?.id}>
	<input type="hidden" name="file_name" value={get_document?.uploads?.filename} />
</DeleteModal>
<div class="card bg-light">
	<div class="card-header fs-5">
		{locale.T('documents')}
	</div>
	<div class="card-body">
		<div class="row g-0">
			<div class="col-md-3">
				<div class="btn-group w-100 mb-2">
					<a
						class:active={title === 'accept_leaving'}
						href="/ipd/{get_progress_note?.id}/document?title=accept_leaving"
						type="button"
						data-sveltekit-noscroll
						class="btn btn-outline-primary text-start"
						><i class="fa-regular fa-file-word"></i> លិខិតអនុញ្ញាតចេញពិពេទ្យ</a
					>
					{#if get_documents.find((e) => e.title === 'accept_leaving')}
						<button style="max-width: 50px;" aria-label="upload-btn-doc-1" class="btn btn-primary"
							><i class="fa-regular fa-circle-check"></i></button
						>
					{/if}
				</div>
				<div class="btn-group w-100 mb-2">
					<a
						class:active={title === 'service_contract'}
						href="/ipd/{get_progress_note?.id}/document?title=service_contract"
						type="button"
						data-sveltekit-noscroll
						class="btn btn-outline-primary text-start"
						><i class="fa-regular fa-file-word"></i> កិច្ចសន្យាទទួលសេវា</a
					>
					{#if get_documents.find((e) => e.title === 'service_contract')}
						<button style="max-width: 50px;" aria-label="upload-btn-doc-1" class="btn btn-primary"
							><i class="fa-regular fa-circle-check"></i></button
						>
					{/if}
				</div>
				<div class="btn-group w-100 mb-2">
					<a
						class:active={title === 'request_discharge'}
						href="/ipd/{get_progress_note?.id}/document?title=request_discharge"
						type="button"
						data-sveltekit-noscroll
						class="btn btn-outline-primary text-start"
						><i class="fa-regular fa-file-word"></i> លិខិស្នើរសុំចេញពីពេទ្យ</a
					>
					{#if get_documents.find((e) => e.title === 'request_discharge')}
						<button style="max-width: 50px;" aria-label="upload-btn-doc-1" class="btn btn-primary"
							><i class="fa-regular fa-circle-check"></i></button
						>
					{/if}
				</div>
				<div class="btn-group w-100 mb-2">
					<a
						class:active={title === 'patient_transfer'}
						href="/ipd/{get_progress_note?.id}/document?title=patient_transfer"
						type="button"
						data-sveltekit-noscroll
						class="btn btn-outline-primary text-start"
						><i class="fa-regular fa-file-word"></i> លិខិតបញ្ជូនអ្នកជំងឺ</a
					>
					{#if get_documents.find((e) => e.title === 'patient_transfer')}
						<button style="max-width: 50px;" aria-label="upload-btn-doc-1" class="btn btn-primary"
							><i class="fa-regular fa-circle-check"></i></button
						>
					{/if}
				</div>
				<div class="btn-group w-100 mb-2">
					<a
						class:active={title === 'birth_recognize'}
						href="/ipd/{get_progress_note?.id}/document?title=birth_recognize"
						type="button"
						data-sveltekit-noscroll
						class="btn btn-outline-primary text-start"
						><i class="fa-regular fa-file-word"></i> លិខិតទទូលស្គាល់កំណើត</a
					>
					{#if get_documents.find((e) => e.title === 'birth_recognize')}
						<button style="max-width: 50px;" aria-label="upload-btn-doc-1" class="btn btn-primary"
							><i class="fa-regular fa-circle-check"></i></button
						>
					{/if}
				</div>
				<div class="btn-group w-100 mb-2">
					<a
						class:active={title === 'birth_certificate_1'}
						href="/ipd/{get_progress_note?.id}/document?title=birth_certificate_1"
						type="button"
						data-sveltekit-noscroll
						class="btn btn-outline-primary text-start"
						><i class="fa-regular fa-file-word"></i> លិខិតបញ្ជាក់ពីការសម្រាលកូន ទី១</a
					>
					{#if get_documents.find((e) => e.title === 'birth_certificate_1')}
						<button style="max-width: 50px;" aria-label="upload-btn-doc-1" class="btn btn-primary"
							><i class="fa-regular fa-circle-check"></i></button
						>
					{/if}
				</div>
				<div class="btn-group w-100 mb-2">
					<a
						class:active={title === 'birth_certificate_2'}
						href="/ipd/{get_progress_note?.id}/document?title=birth_certificate_2"
						type="button"
						data-sveltekit-noscroll
						class="btn btn-outline-primary text-start"
						><i class="fa-regular fa-file-word"></i> លិខិតបញ្ជាក់ពីការសម្រាលកូន ទី២</a
					>
					{#if get_documents.find((e) => e.title === 'birth_certificate_2')}
						<button style="max-width: 50px;" aria-label="upload-btn-doc-1" class="btn btn-primary"
							><i class="fa-regular fa-circle-check"></i></button
						>
					{/if}
				</div>
				<div class="btn-group w-100 mb-2">
					<a
						class:active={title === 'birth_certificate_3'}
						href="/ipd/{get_progress_note?.id}/document?title=birth_certificate_3"
						type="button"
						data-sveltekit-noscroll
						class="btn btn-outline-primary text-start"
						><i class="fa-regular fa-file-word"></i> លិខិតបញ្ជាក់ពីការសម្រាលកូន ទី៣</a
					>
					{#if get_documents.find((e) => e.title === 'birth_certificate_3')}
						<button style="max-width: 50px;" aria-label="upload-btn-doc-1" class="btn btn-primary"
							><i class="fa-regular fa-circle-check"></i></button
						>
					{/if}
				</div>
				<div class="btn-group w-100 mb-2">
					<a
						class:active={title === 'birth_certificate_4'}
						href="/ipd/{get_progress_note?.id}/document?title=birth_certificate_4"
						type="button"
						data-sveltekit-noscroll
						class="btn btn-outline-primary text-start"
						><i class="fa-regular fa-file-word"></i> លិខិតបញ្ជាក់ពីការសម្រាលកូន ទី៤</a
					>
					{#if get_documents.find((e) => e.title === 'birth_certificate_4')}
						<button style="max-width: 50px;" aria-label="upload-btn-doc-1" class="btn btn-primary"
							><i class="fa-regular fa-circle-check"></i></button
						>
					{/if}
				</div>
				<div class="btn-group w-100 mb-2">
					<a
						class:active={title === 'result_checking'}
						href="/ipd/{get_progress_note?.id}/document?title=result_checking"
						type="button"
						data-sveltekit-noscroll
						class="btn btn-outline-primary text-start"
						><i class="fa-regular fa-file-word"></i> លទ្ធផលពីនិត្យសុខភាព</a
					>
					{#if get_documents.find((e) => e.title === 'result_checking')}
						<button style="max-width: 50px;" aria-label="upload-btn-doc-1" class="btn btn-primary"
							><i class="fa-regular fa-circle-check"></i></button
						>
					{/if}
				</div>
			</div>
			<div class="col-md-9">
				{#if title}
					<div style="max-width: 870px;margin:auto;max-height: 3508px;">
						<Form reset={false} bind:loading action="?/create_document" method="post">
							<div class="row g-0 sticky-top justify-content-between">
								<button
									disabled={!get_document?.id}
									type="button"
									data-bs-toggle="modal"
									data-bs-target="#delete_modal"
									class=" btn btn-lg btn-danger col-6">{locale.T('delete_')}</button
								>
								<button type="submit" class="btn btn-lg btn-primary col-6">
									<strong>{locale.T('save')}</strong>
								</button>
							</div>
							<div style="zoom: 80%;" class="shadow p-3 mb-5 bg-body">
								<div id="print_document">
									{#if title === 'accept_leaving'}
										<AcceptLeaving
											p_name={get_progress_note?.patient?.name_khmer
												?.concat(`(${get_progress_note?.patient?.name_latin}) `)
												.concat(
													`ភេទ ${get_progress_note?.patient?.gender.toLowerCase().replace('male', 'ប្រុស').replace('female', 'ស្រី')} `
												)
												.concat(
													`អាយុ ${dobToAge(get_progress_note?.patient?.dob ?? '', get_progress_note?.date_checkup ?? '')}`
												)
												.toLowerCase()
												.replace('month', 'ខែ')
												.toLowerCase()
												.replace('year', 'ឆ្នាំ')
												.toLowerCase()
												.replace('day', 'ថ្ងៃ') ?? ''}
											p_nation={get_progress_note?.patient?.nation ?? ''}
											address={{
												village: get_progress_note?.patient?.village,
												commune: get_progress_note?.patient?.commune,
												district: get_progress_note?.patient?.district,
												provice: get_progress_note?.patient?.provice
											}}
											{get_document_setting}
											fields={get_document?.fields ?? []}
											p_date_checkup={get_progress_note?.date_checkup ?? ''}
											p_date_checkout={get_progress_note?.date_checkout ?? ''}
											title_khm={get_clinich_info?.title_khm ?? ''}
											title_eng={get_clinich_info?.title_eng ?? ''}
											logo={get_upload?.filename ?? ''}
										/>
									{/if}
									{#if title === 'service_contract'}
										<ServiceContract
											{get_document_setting}
											fields={get_document?.fields ?? []}
											address={{
												village: get_progress_note?.patient?.village,
												commune: get_progress_note?.patient?.commune,
												district: get_progress_note?.patient?.district,
												provice: get_progress_note?.patient?.provice
											}}
											occupation_list={get_words
												.filter((e) => e.type === 'occupation')
												.map((e) => e.text)}
											p_name={get_progress_note?.patient?.name_khmer
												?.concat(`(${get_progress_note?.patient?.name_latin}) `)
												.concat(
													`ភេទ ${get_progress_note?.patient?.gender.toLowerCase().replace('male', 'ប្រុស').replace('female', 'ស្រី')} `
												)
												.concat(
													`អាយុ ${dobToAge(get_progress_note?.patient?.dob ?? '', get_progress_note?.date_checkup ?? '')}`
												)
												.toLowerCase()
												.replace('month', 'ខែ')
												.toLowerCase()
												.replace('year', 'ឆ្នាំ')
												.toLowerCase()
												.replace('day', 'ថ្ងៃ') ?? ''}
											p_nation={get_progress_note?.patient?.nation ?? ''}
											title_khm={get_clinich_info?.title_khm ?? ''}
											title_eng={get_clinich_info?.title_eng ?? ''}
											logo={get_upload?.filename ?? ''}
										/>
									{/if}
									{#if title === 'request_discharge'}
										<RequestDischarge
											{get_document_setting}
											nations_list={get_words.filter((e) => e.type === 'nation').map((e) => e.text)}
											fields={get_document?.fields ?? []}
											address={{
												village: get_progress_note?.patient?.village,
												commune: get_progress_note?.patient?.commune,
												district: get_progress_note?.patient?.district,
												provice: get_progress_note?.patient?.provice
											}}
											occupation_list={get_words
												.filter((e) => e.type === 'occupation')
												.map((e) => e.text)}
											p_name={get_progress_note?.patient?.name_khmer
												?.concat(`(${get_progress_note?.patient?.name_latin}) `)
												.concat(
													`ភេទ ${get_progress_note?.patient?.gender.toLowerCase().replace('male', 'ប្រុស').replace('female', 'ស្រី')} `
												)
												.concat(
													`អាយុ ${dobToAge(get_progress_note?.patient?.dob ?? '', get_progress_note?.date_checkup ?? '')}`
												)
												.toLowerCase()
												.replace('month', 'ខែ')
												.toLowerCase()
												.replace('year', 'ឆ្នាំ')
												.toLowerCase()
												.replace('day', 'ថ្ងៃ') ?? ''}
											p_nation={get_progress_note?.patient?.nation ?? ''}
											p_contact={get_progress_note?.patient?.telephone ?? ''}
											p_date_checkup={get_progress_note?.date_checkup ?? ''}
											p_department={get_progress_note?.department.products ?? ''}
											title_khm={get_clinich_info?.title_khm ?? ''}
											title_eng={get_clinich_info?.title_eng ?? ''}
											logo={get_upload?.filename ?? ''}
										/>
									{/if}
									{#if title === 'patient_transfer'}
										<PatientTranswer
											{get_document_setting}
											fields={get_document?.fields ?? []}
											p_address={{
												village: get_progress_note?.patient?.village,
												commune: get_progress_note?.patient?.commune,
												district: get_progress_note?.patient?.district,
												provice: get_progress_note?.patient?.provice
											}}
											p_name={get_progress_note?.patient?.name_khmer
												?.concat(`(${get_progress_note?.patient?.name_latin}) `)
												.concat(
													`ភេទ ${get_progress_note?.patient?.gender.toLowerCase().replace('male', 'ប្រុស').replace('female', 'ស្រី')} `
												)
												.concat(
													`អាយុ ${dobToAge(get_progress_note?.patient?.dob ?? '', get_progress_note?.date_checkup ?? '')}`
												)
												.toLowerCase()
												.replace('month', 'ខែ')
												.toLowerCase()
												.replace('year', 'ឆ្នាំ')
												.toLowerCase()
												.replace('day', 'ថ្ងៃ') ?? ''}
											p_nation={get_progress_note?.patient?.nation ?? ''}
											p_date_checkup={get_progress_note?.date_checkup ?? ''}
											title_khm={get_clinich_info?.title_khm ?? ''}
											title_eng={get_clinich_info?.title_eng ?? ''}
											logo={get_upload?.filename ?? ''}
										/>
									{/if}
									{#if title === 'birth_recognize'}
										<BirthRecognize
											{get_document_setting}
											p_address={{
												village: get_progress_note?.patient?.village,
												commune: get_progress_note?.patient?.commune,
												district: get_progress_note?.patient?.district,
												provice: get_progress_note?.patient?.provice
											}}
											p_name={get_progress_note?.patient?.name_khmer
												?.concat(`(${get_progress_note?.patient?.name_latin}) `)
												.concat(
													`ភេទ ${get_progress_note?.patient?.gender.toLowerCase().replace('male', 'ប្រុស').replace('female', 'ស្រី')} `
												)
												.concat(
													`អាយុ ${dobToAge(get_progress_note?.patient?.dob ?? '', get_progress_note?.date_checkup ?? '')}`
												)
												.toLowerCase()
												.replace('month', 'ខែ')
												.toLowerCase()
												.replace('year', 'ឆ្នាំ')
												.toLowerCase()
												.replace('day', 'ថ្ងៃ') ?? ''}
											p_occupation={get_progress_note?.patient?.occupation ?? ''}
											fields={get_document?.fields ?? []}
											occupation_list={get_words
												.filter((e) => e.type === 'occupation')
												.map((e) => e.text)}
											title_khm={get_clinich_info?.title_khm ?? ''}
											title_eng={get_clinich_info?.title_eng ?? ''}
											logo={get_upload?.filename ?? ''}
										/>
									{/if}
									{#if title === 'birth_certificate_1'}
										<BirthCertificate
											{get_document_setting}
											p_contact={get_progress_note?.patient?.telephone ?? ''}
											p_dob={get_progress_note?.patient?.dob ?? ''}
											p_address={{
												village: get_progress_note?.patient?.village,
												commune: get_progress_note?.patient?.commune,
												district: get_progress_note?.patient?.district,
												provice: get_progress_note?.patient?.provice
											}}
											p_name_khmer={get_progress_note?.patient?.name_khmer ?? ''}
											p_nation={get_progress_note?.patient?.nation ?? ''}
											p_date_checkup={get_progress_note?.date_checkup ?? ''}
											nations_list={get_words.filter((e) => e.type === 'nation').map((e) => e.text)}
											fields_={get_document?.fields ?? []}
											birth_certificate="birth_certificate_1"
											title_khm={get_clinich_info?.title_khm ?? ''}
											title_eng={get_clinich_info?.title_eng ?? ''}
											logo={get_upload?.filename ?? ''}
										/>
									{/if}
									{#if title === 'birth_certificate_2'}
										<BirthCertificate
											{get_document_setting}
											p_contact={get_progress_note?.patient?.telephone ?? ''}
											p_dob={get_progress_note?.patient?.dob ?? ''}
											p_address={{
												village: get_progress_note?.patient?.village,
												commune: get_progress_note?.patient?.commune,
												district: get_progress_note?.patient?.district,
												provice: get_progress_note?.patient?.provice
											}}
											p_name_khmer={get_progress_note?.patient?.name_khmer ?? ''}
											p_nation={get_progress_note?.patient?.nation ?? ''}
											p_date_checkup={get_progress_note?.date_checkup ?? ''}
											nations_list={get_words.filter((e) => e.type === 'nation').map((e) => e.text)}
											fields_={get_document?.fields ?? []}
											birth_certificate="birth_certificate_2"
											title_khm={get_clinich_info?.title_khm ?? ''}
											title_eng={get_clinich_info?.title_eng ?? ''}
											logo={get_upload?.filename ?? ''}
											fields_1={birth_certificate_1?.fields ?? []}
										/>
									{/if}
									{#if title === 'birth_certificate_3'}
										<BirthCertificate
											{get_document_setting}
											p_contact={get_progress_note?.patient?.telephone ?? ''}
											p_dob={get_progress_note?.patient?.dob ?? ''}
											p_address={{
												village: get_progress_note?.patient?.village,
												commune: get_progress_note?.patient?.commune,
												district: get_progress_note?.patient?.district,
												provice: get_progress_note?.patient?.provice
											}}
											p_name_khmer={get_progress_note?.patient?.name_khmer ?? ''}
											p_nation={get_progress_note?.patient?.nation ?? ''}
											p_date_checkup={get_progress_note?.date_checkup ?? ''}
											nations_list={get_words.filter((e) => e.type === 'nation').map((e) => e.text)}
											fields_={get_document?.fields ?? []}
											birth_certificate="birth_certificate_3"
											title_khm={get_clinich_info?.title_khm ?? ''}
											title_eng={get_clinich_info?.title_eng ?? ''}
											logo={get_upload?.filename ?? ''}
											fields_1={birth_certificate_1?.fields ?? []}
										/>
									{/if}
									{#if title === 'birth_certificate_4'}
										<BirthCertificate
											{get_document_setting}
											p_contact={get_progress_note?.patient?.telephone ?? ''}
											p_dob={get_progress_note?.patient?.dob ?? ''}
											p_address={{
												village: get_progress_note?.patient?.village,
												commune: get_progress_note?.patient?.commune,
												district: get_progress_note?.patient?.district,
												provice: get_progress_note?.patient?.provice
											}}
											p_name_khmer={get_progress_note?.patient?.name_khmer ?? ''}
											p_nation={get_progress_note?.patient?.nation ?? ''}
											p_date_checkup={get_progress_note?.date_checkup ?? ''}
											nations_list={get_words.filter((e) => e.type === 'nation').map((e) => e.text)}
											fields_={get_document?.fields ?? []}
											birth_certificate="birth_certificate_4"
											title_khm={get_clinich_info?.title_khm ?? ''}
											title_eng={get_clinich_info?.title_eng ?? ''}
											logo={get_upload?.filename ?? ''}
											fields_1={birth_certificate_1?.fields ?? []}
										/>
									{/if}
									{#if title === 'result_checking'}
										<ResultChecking
											{get_document_setting}
											fields={get_document?.fields ?? []}
											p_address={{
												village: get_progress_note?.patient?.village,
												commune: get_progress_note?.patient?.commune,
												district: get_progress_note?.patient?.district,
												provice: get_progress_note?.patient?.provice
											}}
											p_name_khmer={get_progress_note?.patient?.name_khmer ?? ''}
											p_name_latin={get_progress_note?.patient?.name_latin ?? ''}
											p_occupation={get_progress_note?.patient?.occupation ?? ''}
											p_id_card_passport={get_progress_note?.patient?.id_cart_passport ?? ''}
											p_dob={get_progress_note?.patient?.dob ?? ''}
											p_phone={get_progress_note?.patient?.telephone ?? ''}
											p_nation={get_progress_note?.patient?.nation ?? ''}
											p_gender={get_progress_note?.patient?.gender
												.toLowerCase()
												.replace('male', 'ប្រុស')
												.replace('female', 'ស្រី') ?? ''}
											title_khm={get_clinich_info?.title_khm ?? ''}
											title_eng={get_clinich_info?.title_eng ?? ''}
											logo={get_upload?.filename ?? ''}
										/>
									{/if}
								</div>
							</div>
						</Form>
						{#if get_document?.fields}
							<Print margin="30px" id="print_document" class="btn btn-lg btn-info w-100" />
							<br />
						{/if}
						<br />
						{#if get_document.title === title}
							<div class="alert alert-warning py-2">
								សូមកុំភ្លេចបញ្ចូលឯកសារដែលបានបេាះពុម្ពហើយ!
								{#if get_document?.uploads?.filename}
									<a
										href={get_document?.uploads?.filename}
										class="btn btn-sm btn-primary"
										target="_blank"
										rel="noopener noreferrer"
										download
									>
										<i class="fa-solid fa-download"></i>
										{get_document?.uploads?.filename}
									</a>
								{/if}
							</div>

							<Form
								class="card bg-light "
								action="?/upload_doc"
								method="post"
								enctype="multipart/form-data"
							>
								<div class="card-body">
									<CropImage
										default_image={get_document?.uploads?.filename ?? ''}
										name="file"
										related_id={get_document.id}
										related_type_="document"
										aspect_ratio
									/>
									<br />
									<SubmitButton />
								</div>
							</Form>
						{/if}
					</div>
				{/if}
			</div>
		</div>
	</div>
</div>
