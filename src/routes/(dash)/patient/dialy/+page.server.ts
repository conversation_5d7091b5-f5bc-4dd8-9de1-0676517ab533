import { db } from '$lib/server/db';
import { patient, product, progressNote, visit, words } from '$lib/server/schemas';
import type { PageServerLoad } from './$types';
import { and, asc, desc, eq, isNull, like, ne, or } from 'drizzle-orm';
import { betweenHelper, pagination } from '$lib/server/utils';
export const load = (async ({ parent, url }) => {
	await parent();
	const q = url.searchParams.get('q') ?? '';
	const patient_id = Number(url.searchParams.get('patient_id'));
	const department_id = Number(url.searchParams.get('department_id'));
	const get_departments = await db.query.product.findMany({
		where: eq(product.category_id, 11)
	});
	const get_staffs = await db.query.staff.findMany();
	const get_visits = await db.query.visit.findMany({
		where: and(
			eq(visit.checkin_type, 'OPD'),
			ne(visit.status, 'DONE'),
			// eq(visit.transfer, true),
			betweenHelper(url, visit.date_checkup),
			patient_id ? eq(visit.patient_id, patient_id) : undefined,
			department_id ? eq(visit.department_id, department_id) : undefined
		),
		with: {
			staff: {
				with: {
					title: true
				}
			},
			patient: true,
			department: true,
			billing: {
				with: {
					serviceType: true,
					paymentService: true
				}
			},
			document: true
		},
		orderBy: desc(visit.date_checkup),
		...pagination(url)
	});
	const count_opd = await db.$count(
		visit,
		and(
			eq(visit.checkin_type, 'OPD'),
			// eq(visit.transfer, true),
			betweenHelper(url, visit.date_checkup),
			patient_id ? eq(visit.patient_id, patient_id) : undefined,
			department_id ? eq(visit.department_id, department_id) : undefined
		)
	);
	const get_patients = await db.query.patient.findMany({
		where: or(
			like(patient.name_latin, `%${q}%`),
			like(patient.name_khmer, `%${q}%`),
			like(patient.telephone, `%${q}%`)
		),
		limit: 200
	});

	// ipd
	const get_pregress_notes = await db.query.progressNote.findMany({
		where: and(
			betweenHelper(url, visit.date_checkup),
			isNull(progressNote.date_checkout),
			patient_id ? eq(progressNote.patient_id, patient_id) : undefined,
			department_id ? eq(progressNote.department_id, department_id) : undefined
		),
		with: {
			activeDepartment: {
				with: {
					department: true,
					activeBed: true,
					getter: {
						with: {
							title: true
						}
					}
				}
			},
			activeBed: {
				with: {
					bed: {
						with: {
							room: {
								with: {
									department: true,
									product: true
								}
							}
						}
					}
				}
			},
			document: true,
			billing: {
				with: {
					serviceType: true,
					paymentService: true
				}
			},
			patient: true,
			staff: true,
			department: true,
			visit: {
				with: {
					staff: true
				},
				orderBy: asc(visit.date_checkup)
			}
		},
		orderBy: desc(visit.date_checkup),
		...pagination(url)
	});
	const count_ipd = await db.$count(
		progressNote,
		and(
			betweenHelper(url, visit.date_checkup),
			patient_id ? eq(progressNote.patient_id, patient_id) : undefined,
			department_id ? eq(progressNote.department_id, department_id) : undefined
		)
	);
	const get_words_payment_service = await db.query.words.findMany({
		where: eq(words.category, 'payment_service')
	});
	return {
		get_visits,
		get_departments,
		get_staffs,
		get_patients,
		count_opd,
		count_ipd,
		get_words_payment_service,
		get_pregress_notes
	};
}) satisfies PageServerLoad;
