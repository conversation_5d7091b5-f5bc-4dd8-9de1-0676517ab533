import { db } from '$lib/server/db';
import { progressNote, visit, product, patient, activeDepartment } from '$lib/server/schemas';
import logError from '$lib/server/utils/logError';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { and, asc, desc, eq, like, or } from 'drizzle-orm';
import { betweenHelper, pagination } from '$lib/server/utils';
export const load = (async ({ parent, url }) => {
	await parent();
	const q = url.searchParams.get('q') ?? '';
	const patient_id = Number(url.searchParams.get('patient_id'));
	const department_id = Number(url.searchParams.get('department_id'));
	const get_pregress_notes = await db.query.progressNote.findMany({
		where: and(
			betweenHelper(url, progressNote.date_checkup),
			patient_id ? eq(progressNote.patient_id, patient_id) : undefined,
			department_id ? eq(progressNote.department_id, department_id) : undefined
		),
		with: {
			activeDepartment: {
				with: {
					department: true,
					activeBed: true,
					getter: {
						with: {
							title: true
						}
					}
				}
			},
			activeBed: {
				with: {
					bed: {
						with: {
							room: {
								with: {
									department: true,
									product: true
								}
							}
						}
					}
				}
			},
			document: true,
			billing: {
				with: {
					serviceType: true,
					paymentService: true
				}
			},
			patient: true,
			staff: true,
			department: true,
			visit: {
				with: {
					staff: true
				},
				orderBy: asc(visit.date_checkup)
			}
		},
		orderBy: desc(visit.date_checkup),
		...pagination(url)
	});
	const count = await db.$count(
		progressNote,
		and(
			betweenHelper(url, progressNote.date_checkup),
			patient_id ? eq(progressNote.patient_id, patient_id) : undefined,
			department_id ? eq(progressNote.department_id, department_id) : undefined
		)
	);
	const get_departments = await db.query.product.findMany({
		where: eq(product.category_id, 11)
	});
	const get_patients = await db.query.patient.findMany({
		where: or(
			like(patient.name_latin, `%${q}%`),
			like(patient.name_khmer, `%${q}%`),
			like(patient.telephone, `%${q}%`)
		),
		limit: 200
	});
	const get_staffs = await db.query.staff.findMany();
	return {
		get_pregress_notes,
		get_departments,
		get_staffs,
		get_patients,
		items: count
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	delete_progress_note: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.delete(progressNote)
			.where(eq(progressNote.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_etiology: async ({ request, url }) => {
		const body = await request.formData();
		const id = body.get('id');
		const etiology = body.get('etiology') || '';
		if (!id || isNaN(+id)) return fail(303, { idErr: true });
		await db
			.update(progressNote)
			.set({
				etiology: etiology.toString()
			})
			.where(eq(progressNote.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_department: async ({ request, url }) => {
		const body = await request.formData();
		const id = body.get('id');
		const department_id = body.get('department_id') || '';
		if (!id || isNaN(+id)) return fail(303, { idErr: true });
		await db
			.update(progressNote)
			.set({
				department_id: +department_id
			})
			.where(eq(progressNote.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	active_visit: async ({ request, locals, url }) => {
		const body = await request.formData();
		const { id: progress_note_id, active_department_id } = Object.fromEntries(body) as Record<
			string,
			string
		>;
		if (!progress_note_id || !locals.user?.staff_id || !active_department_id)
			return fail(400, { message: 'Check all of id ' });
		await db
			.update(progressNote)
			.set({
				status: 'CHECKING',
				staff_id: locals.user?.staff_id
			})
			.where(eq(progressNote.id, +progress_note_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await db
			.update(activeDepartment)
			.set({
				getter_id: locals.user?.staff_id
			})
			.where(eq(activeDepartment.id, +active_department_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		redirect(303, `/ipd/${progress_note_id}/progress-note`);
	},
	update_staff: async ({ request, url }) => {
		const body = await request.formData();
		const id = body.get('id');
		const staff_id = body.get('staff_id') || '';
		if (!id || isNaN(+id)) return fail(303, { idErr: true });
		await db
			.update(progressNote)
			.set({
				staff_id: +staff_id
			})
			.where(eq(progressNote.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
