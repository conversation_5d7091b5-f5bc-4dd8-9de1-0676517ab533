<script lang="ts">
	import Sign from '$lib/coms-report/Sign.svelte';
	import Athtml from '$lib/coms/Athtml.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import TimeFormat from '$lib/coms/TimeFormat.svelte';
	import { dobToAge, khmerDate } from '$lib/helper';
	import type { PageServerData } from './$types';
	let { data }: { data: PageServerData } = $props();
	let { get_visit, get_exam } = $derived(data);
	let mean_arterial_pressure = $derived(
		(1 / 3) * Number(get_visit?.vitalSign?.sbp) + (2 / 3) * Number(get_visit?.vitalSign?.dbp)
	);
</script>

<h5 class="kh_font_muol_light text-center pt-1">{'ការសង្កេតវេជ្ជសាស្ត្រ'}</h5>
<h5 class="en_font_times_new_roman text-center pt-1">Medical Observation (SOAP NOTE)</h5>
<u class="fs-5"
	>I. <span class="kh_font_muol_light">
		{'ព័តមានអ្នកជំងឺ'}
	</span></u
>
<ul>
	<p
		class="fs-5"
		style="  text-align:justify;  
	text-justify:initial;}"
	>
		{'(ID)'} PT{get_visit?.patient_id} VS{get_visit?.id} <br />
		{'ឈ្មោះ '}{get_visit?.patient?.name_khmer ?? ''}
		{`(${get_visit?.patient?.name_latin ?? ''})`}
		{'ភេទ '}{get_visit?.patient.gender === 'Male' ? 'ប្រុស' : 'ស្រី'}
		{'ថ្ងៃខែឆ្នាំកំណើត'}
		{khmerDate(get_visit?.patient?.dob, 'date')}
		{'អាយុ'}
		{dobToAge(get_visit?.patient.dob, get_visit?.date_checkup) ?? ''}
		{'មុខរបរ'}
		{get_visit?.patient?.occupation ?? ''}
		<br />
		{'អាសយដ្ឋាន'}
		{get_visit?.patient?.village?.type ?? ''}
		{get_visit?.patient?.village?.name_khmer.concat(',') ?? ''}
		{get_visit?.patient?.commune?.type ?? ''}
		{get_visit?.patient?.commune?.name_khmer.concat(',') ?? ''}
		{get_visit?.patient?.district?.type ?? ''}
		{get_visit?.patient?.district?.name_khmer.concat(',') ?? ''}
		{get_visit?.patient?.provice?.type ?? ''}
		{get_visit?.patient?.provice?.name_khmer ?? ''}
		<br />
		{'កាលបរិច្ឆេទចូលពិនិត្យ'}
		<!-- <DDMMYYYYFormat date={get_visit?.date_checkup} style="date" /> -->
		{khmerDate(get_visit?.date_checkup, 'date')}
		{'ផ្នែក'}
		{get_visit?.department?.products}
	</p>
</ul>
<u class="fs-5">II.Subjective <span class="kh_font_muol_light">{'(ការសាកសួរពីអ្នកជំងឺ)'}</span> </u>
<ul>
	<div class="card-body mt-0 pt-0 table-responsive p-0">
		<table class="table mb-0 table-bordered table-hover text-wrap text-break table-light">
			<tbody>
				{#if get_visit?.subjective?.cheif_complaint}
					<tr>
						<td>Cheif Complaint</td>
						<td>
							{get_visit?.subjective?.cheif_complaint}
						</td>
					</tr>
				{/if}
				{#if get_visit?.subjective?.history_of_present_illness}
					<tr>
						<td style="width: 35%;">History of present illness</td>
						<td>{get_visit?.subjective?.history_of_present_illness}</td>
					</tr>
				{/if}
				{#if get_visit?.subjective?.current_medication}
					<tr>
						<td style="width: 35%;">Current Medication</td>
						<td>{get_visit?.subjective?.current_medication}</td>
					</tr>
				{/if}

				{#if get_visit?.subjective?.past_medical_history}
					<tr>
						<td style="width: 35%;">Past medical history</td>
						<td>{get_visit?.subjective?.past_medical_history}</td>
					</tr>
				{/if}

				{#if get_visit?.subjective?.allesgy_medicine}
					<tr>
						<td style="width: 35%;">Allergy medical</td>
						<td>
							{get_visit?.subjective?.allesgy_medicine}
						</td>
					</tr>
				{/if}

				{#if get_visit?.subjective?.surgical_history}
					<tr>
						<td style="width: 35%;">Surgical history</td>
						<td>{get_visit?.subjective?.surgical_history}</td>
					</tr>
				{/if}

				{#if get_visit?.subjective?.family_and_social_history}
					<tr>
						<td style="width: 35%;">Family and socail history</td>
						<td>{get_visit?.subjective?.family_and_social_history}</td>
					</tr>
				{/if}

				{#if get_visit?.subjective?.pre_diagnosis}
					<tr>
						<td style="width: 35%;">Pre diagnosis</td>
						<td>{get_visit?.subjective?.pre_diagnosis}</td>
					</tr>
				{/if}
			</tbody>
		</table>
	</div>
</ul>
<u class="fs-5">III.Objective <span class="kh_font_muol_light"> {'(ការពិនិត្យជំងឺ)'}</span> </u>
<ul>
	{#if get_visit?.vitalSign}
		<div class="card mt-0 pt-0 table-responsive p-0">
			<div class="card-header">Vital Sign</div>
			<div class="card-body">
				<table class="table table-light">
					<tbody>
						<tr>
							<td>BP(mmHg)</td>
							<td> : </td>
							<td>
								{#if get_visit?.vitalSign?.sbp}
									{get_visit?.vitalSign?.sbp?.toFixed(0).concat(' /') ?? ''}
									{get_visit?.vitalSign?.dbp?.toFixed(0).concat(' mmHg') ?? ''}
								{/if}
							</td>
							<td>MAP</td>
							<td> : </td>
							<td>
								{#if mean_arterial_pressure}
									{mean_arterial_pressure ? mean_arterial_pressure?.toFixed(0).concat(' mmHg') : ''}
								{/if}
							</td>
							<td>Pulse (min)</td>
							<td> : </td>
							<td style="width: 20%;">
								{#if get_visit?.vitalSign?.pulse}
									{get_visit?.vitalSign?.pulse?.toFixed(0).concat(' /min') ?? ''}
								{/if}
							</td>
						</tr>
						<tr>
							<td>Temperature °C </td>
							<td> : </td>
							<td>
								{#if get_visit?.vitalSign?.t}
									<Athtml html={get_visit?.vitalSign?.t?.toFixed(1).concat(' &deg;C') ?? ''} />
								{/if}
							</td>
							<td>RR (min)</td>
							<td> : </td>
							<td>
								{#if get_visit?.vitalSign?.rr}
									{get_visit?.vitalSign?.rr?.toFixed(0).concat(' /min') ?? ''}
								{/if}
							</td>
							<td>SpO2 (%)</td>
							<td> : </td>
							<td>
								{#if get_visit.vitalSign.sp02}
									{get_visit?.vitalSign?.sp02?.toFixed(0).concat(' %') ?? ''}
								{/if}
							</td>
						</tr>

						<tr>
							<td>Height (cm)</td>
							<td> : </td>

							<td>
								{#if get_visit.vitalSign.height}
									{get_visit?.vitalSign?.height?.toFixed(0).concat(' cm') ?? ''}
								{/if}
							</td>

							<td>Weight (kg)</td>
							<td> : </td>
							<td>
								{#if get_visit.vitalSign?.weight}
									{get_visit?.vitalSign?.weight?.toFixed(0).concat(' kg') ?? ''}
								{/if}
							</td>

							<td>BMI</td>
							<td> : </td>
							<td>
								{#if get_visit.vitalSign.bmi}
									{get_visit?.vitalSign?.bmi?.toFixed(1).concat(' kg/m') ?? ''}<sup>2</sup>
								{/if}
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	{/if}
	{#if get_visit?.physicalExam.length}
		<li class="fs-4 pt-2">Physical Examination and Evolution</li>
		{#each get_exam as exam}
			{#if get_visit.physicalExam.some((e) => e.physical?.exam_id === exam.id)}
				<div class="card-body mt-0 pt-0 table-responsive p-0">
					<table class="table mb-0 table-bordered table-hover text-wrap text-break table-light">
						<thead class="table-active">
							<tr class="">
								<td colspan="2">{exam?.examination}</td>
							</tr>
						</thead>
						<tbody>
							{#each get_visit.physicalExam as physicalExam}
								{#if exam.physical.some((e) => e.id === physicalExam.physical_id)}
									{#if physicalExam?.result}
										<tr>
											<td style="width: 50%;">{physicalExam.physical?.physical} </td>
											<td>{physicalExam.result} </td>
										</tr>
									{/if}
								{/if}
							{/each}
						</tbody>
					</table>
				</div>
			{/if}
		{/each}
	{/if}
	<li class="fs-4 pt-2">Para-Clinic (Request)</li>
	<div class="row">
		<div class="col-6">
			<div class="card">
				<div class="card-header">Laboratory</div>
				<table class="table table-sm table-borderless table-light">
					<tbody>
						<tr>
							<td>
								<ol>
									{#each get_visit?.laboratoryRequest || [] as item}
										<li class="">{item.product?.products}</li>
									{/each}
								</ol>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
		<div class="col-6">
			<div class="card">
				<div class="card-header">Imagerie</div>
				<table class="table table-sm table-borderless table-light">
					<tbody>
						<tr>
							<td>
								<ol>
									{#each get_visit?.imagerieRequest || [] as item}
										<li class="">{item.product?.products}</li>
									{/each}
								</ol>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</ul>
<u class="fs-5"
	>IV.Accessment <span class="kh_font_muol_light">{'(ការវាយតម្លៃនិងរោគវិនិច្ឆ័យ)'}</span>
</u>
<ul>
	{#if get_visit?.accessment?.diagnosis_or_problem}
		<li class="fs-4 pt-2">Diagnosis</li>
		<h5>
			{get_visit?.accessment?.diagnosis_or_problem}
		</h5>
	{/if}
	{#if get_visit?.accessment?.differential_diagnosis}
		<li class="fs-4 pt-2">Differential Diagnosis</li>
		<h5>
			{get_visit?.accessment?.differential_diagnosis}
		</h5>
	{/if}
	{#if get_visit?.accessment?.assessment_process}
		<li class="fs-4 pt-2">Accessment Process</li>
		<h5 class="text-break">
			{get_visit?.accessment?.assessment_process}
		</h5>
	{/if}
	{#if get_visit?.service.length}
		<li class="fs-4 pt-2">Protocol</li>
		{#each get_visit.service || [] as item}
			<div class="card">
				<div class="card-header">
					{item.product?.products ?? ''}
				</div>
				<div class="card-body p-0 m-0">
					<table class="table p-0 m-0 table-sm table-light">
						<tbody class="">
							<tr>
								<td style="width: 12.5%;">Surgeon</td>
								<td>:</td>
								<td style="width: 12.5%;"> {item.operationProtocol?.surgeon ?? ''} </td>
								<td style="width: 12.5%;">Scrub Nurse</td>
								<td>:</td>
								<td style="width: 12.5%;"> {item.operationProtocol?.scrub_nurse ?? ''} </td>
								<td style="width: 12.5%;">Start Time</td>
								<td>:</td>
								<td style="width: 12.5%;">
									<TimeFormat time={item.operationProtocol?.start_time} />
								</td>
							</tr>
							<tr>
								<td style="width: 12.5%;">Assistant Surgeon</td>
								<td>:</td>
								<td style="width: 12.5%;">
									{item.operationProtocol?.assistant_surgeon ?? ''}
								</td>

								<td style="width: 12.5%;">Circulation Nurse block</td>
								<td>:</td>
								<td style="width: 12.5%;">
									{item.operationProtocol?.cirulating_nurse_block ?? ''}
								</td>
								<td style="width: 12.5%;">Finish Time</td>
								<td>:</td>
								<td style="width: 12.5%;">
									<TimeFormat time={item.operationProtocol?.finish_time} />
								</td>
							</tr>
							<tr>
								<td style="width: 12.5%;">Anesthetist</td>
								<td>:</td>
								<td style="width: 12.5%;"> {item.operationProtocol?.anesthetist ?? ''} </td>

								<td style="width: 12.5%;">Midwife</td>
								<td>:</td>
								<td style="width: 12.5%;"> {item.operationProtocol?.midwife ?? ''} </td>

								<td style="width: 12.5%;">Pre Diagnosis</td>
								<td>:</td>
								<td style="width: 12.5%;">
									{item.operationProtocol?.pre_diagnosis ?? ''}
								</td>
							</tr>
							<tr>
								<td style="width: 12.5%;">Assistant Anesthetist</td>
								<td>:</td>
								<td style="width: 12.5%;">
									{item.operationProtocol?.assistant_anesthetist ?? ''}
								</td>

								<td style="width: 12.5%;">Dates</td>
								<td>:</td>
								<td style="width: 12.5%;">
									<DDMMYYYYFormat date={item.operationProtocol?.date} style="date" />
								</td>
								<td style="width: 12.5%;">Post Diagnosis</td>
								<td>:</td>
								<td style="width: 12.5%;">
									{item.operationProtocol?.post_diagnosis ?? ''}
								</td>
							</tr>
							<tr>
								<td style="width: 12.5%;">Type Anesthesia</td>
								<td>:</td>
								<td style="width: 12.5%;">
									{item.operationProtocol?.type_anesthesia ?? ''}
								</td>
								<td style="width: 12.5%;">Opertive Technique</td>
								<td>:</td>
								<td style="width: 12.5%;">
									{item.operationProtocol?.opertive_technique ?? ''}
								</td>
								<td style="width: 12.5%;">Blood Less</td>
								<td>:</td>
								<td style="width: 12.5%;"> {item.operationProtocol?.blood_less ?? ''} </td>
							</tr>
							<tr>
								<td style="width: 12.5%;">Notes</td>
								<td>:</td>
								<td colspan="7" style="width: 12.5%;">
									{item.operationProtocol?.notes ?? ''}
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		{/each}
	{/if}
</ul>
<u class="fs-5 pt-2">V.Plan <span class="kh_font_muol_light">{'(ផែនការណ៍និងការព្យាបាល)'}</span> </u>
<ul>
	{#if get_visit?.presrciption.length}
		<li class="fs-4 pt-2">វេជ្ជបញ្ជា</li>
		<div class="card-body mt-0 pt-0 table-responsive p-0">
			<table class="table mb-0 table-bordered table-hover text-wrap text-break table-light">
				<thead class="">
					<tr class="text-center table-active">
						<th>
							{'ល.រ'}
						</th>
						<th>
							{'ប្រភេទឱសថ'}
						</th>
						<th>
							{'ឈ្មោះឱសថ'}
						</th>
						<th>
							{'របៀបប្រើ'}
						</th>
						<th>
							{'ពេលវេលាប្រើ'}
						</th>
						<th>
							{'រយៈពេល'}
						</th>
						<th>
							{'ចំនួន'}
						</th>
					</tr>
				</thead>
				<tbody>
					{#each get_visit?.presrciption || [] as item, i}
						<tr class="text-center">
							<td>{i + 1}</td>
							<td>{item?.product?.group?.name ?? ''}</td>
							<td class="text-start">
								<span>{item.product?.products ?? ''}</span>
								{#if item.product?.generic_name}
									<br />
									<span class="">
										{`(${item.product?.generic_name ?? ''})`}
									</span>
								{/if}
							</td>
							<td>{item?.use}</td>
							<td class="text-start">
								<div>
									<span class="">
										{#if item.morning !== 0}
											ពេលព្រឹក {item.morning},
										{/if}
									</span>
									<span class="">
										{#if item.noon !== 0}
											ពេលថ្ងៃត្រង់ {item.noon},
										{/if}
									</span>
									<span class="">
										{#if item.afternoon !== 0}
											ពេលរសៀល {item.afternoon},
										{/if}
									</span>
									<span class="">
										{#if item.evening !== 0}
											ពេលល្ងាច {item.evening},
										{/if}
									</span>
									<span class="">
										{#if item.night !== 0}
											ពេលយប់ {item.night}
										{/if}
									</span>
								</div>
							</td>
							<td>{item?.duration}</td>
							<td>
								{item?.amount}
								{item.product?.unit?.unit ?? ''}
							</td>
						</tr>
					{/each}
					<tr class="text-center table-active">
						<td colspan="7">
							{'ឱសថសរុបចំនួន'}
							{get_visit?.presrciption?.length ?? 0}
							{'មុខ។'}
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	{/if}
	{#if get_visit?.adviceTeaching?.description}
		<li class="fs-4 pt-2">Advice or Teaching</li>
		<h5 class="text-break">
			{get_visit?.adviceTeaching?.description}
		</h5>
	{/if}
	{#if get_visit?.appointment}
		<li class="fs-4 pt-2">
			Appoinment

			<DDMMYYYYFormat date={get_visit?.appointment?.datetime} />
		</li>
		<h5 class="text-break">
			{get_visit?.appointment?.description}
		</h5>
	{/if}
</ul>
<br />
<Sign
	left={{
		title: `<div class="kh_font_muol_light">បានឃើញ និងឯកភាព </div>`,
		date: get_visit?.date_checkup,
		role: 'ប្រធានផ្នែក'
	}}
	right={{
		role: `គ្រូពេទ្យពិនិត្យ`,
		date: get_visit?.date_checkup,
		name: get_visit?.staff?.name_khmer,
		img: '/sign.png'
	}}
/>

<style>
	@media print {
		@page {
			size: A4;
			margin-top: 5mm;
			padding-bottom: 5mm;
		}
	}
</style>
