import { db } from '$lib/server/db';
import { visit } from '$lib/server/schemas';
import { eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';

export const load = (async ({ params }) => {
	const { id } = params;
	const get_exam = await db.query.exam.findMany({
		with: {
			physical: true
		}
	});
	const get_visit = await db.query.visit.findFirst({
		where: eq(visit.id, +id),
		with: {
			service: {
				with: {
					product: true,
					operationProtocol: true
				}
			},
			subjective: true,
			physicalExam: {
				with: {
					physical: {
						with: {
							exam: true
						}
					}
				}
			},
			department: true,
			accessment: true,
			adviceTeaching: true,
			appointment: true,
			vitalSign: true,
			patient: {
				with: {
					village: true,
					district: true,
					commune: true,
					provice: true
				}
			},
			staff: true,
			laboratoryRequest: {
				with: {
					product: {
						with: {
							laboratoryGroup: true
						}
					}
				}
			},
			imagerieRequest: {
				with: {
					product: true
				}
			},
			presrciption: {
				with: {
					product: {
						with: {
							group: true,
							unit: true
						}
					}
				}
			}
		}
	});
	// const map_examination = new Map()
	return { get_visit, get_exam };
}) satisfies PageServerLoad;
