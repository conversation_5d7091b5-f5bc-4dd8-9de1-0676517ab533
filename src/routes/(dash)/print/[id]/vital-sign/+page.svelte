<script lang="ts">
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import Name from '$lib/coms/Name.svelte';
	import Graphs from '$lib/coms/Graphs.svelte';
	import { calculateAge, DDMMYYYY_Format } from '$lib/helper';
	import type { PageServerData } from './$types';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_progress_note, get_clinic_info, get_vital_from_active_department } = $derived(data);
</script>

{#each get_vital_from_active_department || [] as item}
	{@const vital_signs = item.vitalSign || []}
	{@const datasets_a = [
		{
			label: 'SBP',
			borderColor: ['blue'],
			backgroundColor: ['blue'],
			data: vital_signs?.map((e) => e.sbp!)
		},
		{
			label: 'DBP',
			borderColor: ['orange'],
			backgroundColor: ['orange'],
			data: vital_signs?.map((e) => e.dbp!)
		},
		{
			label: 'PULSE',
			borderColor: ['green'],
			backgroundColor: ['green'],
			data: vital_signs?.map((e) => e.pulse!)
		}
	]}
	{@const datasets_b = [
		{
			label: 'Temperature',
			backgroundColor: ['coral'],
			data: vital_signs?.map((e) => e.t!)
		},
		{
			label: 'Respiratory Rate',
			backgroundColor: ['deeppink'],
			data: vital_signs?.map((e) => e.rr!)
		}
	]}
	{@const labels = vital_signs.map((e) => [
		DDMMYYYY_Format(e.datetime!, 'date'),
		DDMMYYYY_Format(e.datetime!, 'time')
	])}
	{@const get_active_bed = item.activeBed[0]}
	<h5 class="kh_font_muol_light text-center pt-1">{'សាលាកប័ត្រតាមដានថែទាំប្រចាំថ្ងៃ'}</h5>
	<h6>
		<span>មន្ទីរពេទ្យ {get_clinic_info?.title_khm}</span>,
		<span>ផ្នែក {item?.department?.products ?? ''}</span>,
		<span>
			{get_active_bed?.bed?.ward?.ward ?? ''},
			{get_active_bed?.bed?.room?.room ?? ''},
			{get_active_bed?.bed?.bed ?? ''}
		</span>
	</h6>
	<h6>
		<span>
			{'នាមត្រកូលនិងនាមខ្លួន'}
			{get_progress_note?.patient?.name_khmer}
			{`( ${get_progress_note?.patient?.name_latin} )`}
		</span>
		<span>
			{'អាយុ'}
			{calculateAge(get_progress_note?.patient?.dob)}
		</span>
		<span>
			{'ភេទ'}
			{get_progress_note?.patient.gender}
		</span>
		<span>#ID {`PT${get_progress_note?.patient_id}`},{`VSI${get_progress_note?.id}`} </span>
	</h6>
	<h6>
		<span>
			{'សម្រាកចាប់ពីថ្ងៃទី'}
			<DDMMYYYYFormat date={item.datetime_in} />
		</span>
		<span>
			{'រហូតដល់ថ្ងៃទី'}
			<DDMMYYYYFormat date={item.datetime_out} />
		</span>
	</h6>
	<div style="page-break-after: always;" class="card-body table-responsive p-0">
		<table class="table mb-0 table-bordered table-hover text-break table-light">
			<thead class="">
				<tr class=" table-active">
					<th colspan="17">
						{item.department?.products ?? ''}
					</th>
				</tr>
				<tr>
					<td colspan="17">
						<div class="row g-0 zoom justify-content-center">
							<div class="col-6">
								<Graphs type="line" title="Vital Sign" datasets={datasets_a} {labels} />
							</div>
							<div class="col-6">
								<Graphs
									type="bar"
									title="Respiratory Rate and Temperature"
									datasets={datasets_b}
									{labels}
								/>
							</div>
						</div>
					</td>
				</tr>

				<tr class="text-center">
					<th rowspan="2" style="width: 100px;">{'កាលបរិច្ឆេទ'}</th>
					<th rowspan="2" style="width: 100px;">{'បុគ្គលិក'}</th>
					<th rowspan="2" style="width: 100px;">
						<span class="text-rotate">{'សម្ពាធឈាម'}</span>
					</th>
					<th rowspan="2" style="width: auto;">
						<span class="text-rotate">{'ចង្វាក់បេះដូង'}</span>
					</th>
					<th rowspan="2" style="width: auto;">
						<span class="text-rotate">{'សីតុណ្ហភាព'}</span>
					</th>
					<th rowspan="2" style="width: auto;">
						<span class="text-rotate">{'ចង្វាក់ដង្ហើម'}</span>
					</th>
					<th rowspan="2" style="width: auto;">
						<span class="text-rotate">{'អុកសុីសែនក្នុងឈាម'}</span>
					</th>
					<th rowspan="2" style="width: auto;">
						<span class="text-rotate">{'ទម្ងន់'}</span>
					</th>
					<th colspan="3" style="width: auto;">
						<span>{'ចូល'}</span>
					</th>
					<th colspan="5" style="width: auto;">
						<span>{'ចេញ'}</span>
					</th>

					<th rowspan="2" style="width: 200px;">
						<span>{'កត់ចំណាំ'}</span>
					</th>
				</tr>
				<tr>
					<!-- in -->
					<th style="width: auto;">
						<span class="text-rotate">{'សេរ៉ូម'}</span>
					</th>
					<th style="width: auto;">
						<span class="text-rotate">{'ការផឹក'}</span>
					</th>
					<th style="width: auto;">
						<span class="text-rotate">{'តាមសុង'}</span>
					</th>
					<!-- end in  -->
					<!-- out  -->
					<th style="width: auto;">
						<span class="text-rotate">{'តាមសុង'}</span>
					</th>
					<th style="width: auto;">
						<span class="text-rotate">{'តាមការបង្ហូចេញ'}</span>
					</th>
					<th style="width: auto;">
						<span class="text-rotate">{'ក្អួត'}</span>
					</th>
					<th style="width: auto;">
						<span class="text-rotate">{'លាមក'}</span>
					</th>
					<th style="width: auto;">
						<span class="text-rotate">{'ទឹកនោម'}</span>
					</th>
					<!-- end out  -->
				</tr>
			</thead>
			<tbody>
				{#each vital_signs as item_1 (item_1.id)}
					<tr class="text-center">
						<td>
							<DDMMYYYYFormat style="date" date={item_1.datetime} />
							<DDMMYYYYFormat style="time" date={item_1.datetime} />
						</td>
						<td>
							<Name name={item_1.by} />
						</td>
						<td
							>{item_1.sbp?.toFixed(0).concat(' /') ?? ''}
							{item_1.dbp?.toFixed(0).concat(' mmHg') ?? ''}</td
						>
						<td>{item_1.pulse ?? ''}</td>
						<td>{item_1.t ?? ''}</td>
						<td>{item_1.rr ?? ''}</td>
						<td>{item_1.sp02 ?? ''}</td>
						<td>{item_1.weight ?? ''}</td>
						<td>{item_1.piv ?? ''}</td>
						<td>{item_1.drink ?? ''}</td>
						<td>{item_1.nasogastric_tube_in ?? ''}</td>
						<td>{item_1.nasogastric_tube_out ?? ''}</td>
						<td>{item_1.fluid_out ?? ''}</td>
						<td>{item_1.vomiting ?? ''}</td>
						<td>{item_1.stool ?? ''}</td>
						<td>{item_1.urine ?? ''}</td>
						<td>{item_1.note ?? ''}</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
{/each}

<style>
	tr {
		text-align: center;
	}
	/* .zoom {
		zoom: 90%;
	} */
	@media print {
		.zoom {
			zoom: 90%;
		}
		table {
			page-break-inside: auto;
		}
		td {
			border: 1px solid lightgray;
		}
		tr,
		h5,
		h6,
		span {
			page-break-inside: auto;
		}
		@page {
			size: A4 landscape;
		}

		/* img
	 {
		page-break-inside: avoid;
	} */
	}
</style>
