import { db } from '$lib/server/db';
import { asc, desc, eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { progressNote, vitalSign, activeDepartment, activeBed } from '$lib/server/schemas';

export const load: PageServerLoad = async ({ params }) => {
	const { id } = params;
	const get_clinic_info = await db.query.clinicinfo.findFirst({});
	const get_progress_note = await db.query.progressNote.findFirst({
		where: eq(progressNote.id, +id),
		with: {
			patient: true,
			activeDepartment: {
				with: {
					department: true
				},
				orderBy: desc(activeDepartment.id)
			},
			vitalSign: {
				orderBy: asc(vitalSign.datetime)
			}
		}
	});
	const get_vital_from_active_department = await db.query.activeDepartment.findMany({
		where: eq(activeDepartment.progress_note_id, +id),
		with: {
			vitalSign: {
				with: {
					by: true
				},
				orderBy: asc(vitalSign.datetime)
			},
			department: true,
			activeBed: {
				where: eq(activeBed.active, true),
				with: {
					bed: {
						with: {
							ward: true,
							room: true
						}
					}
				}
			}
		},
		orderBy: asc(activeDepartment.id)
	});
	return {
		get_progress_note,
		get_clinic_info,
		get_vital_from_active_department
	};
};
