<script lang="ts">
	import type { PageServerData } from './$types';
	import PatientTranswer from '$lib/coms-document/PatientTranswer.svelte';
	import BirthCertificate from '$lib/coms-document/BirthCertificate.svelte';
	import { dobToAge } from '$lib/helper';
	import AcceptLeaving from '$lib/coms-document/AcceptLeaving.svelte';
	import { page } from '$app/state';
	let { data }: { data: PageServerData } = $props();
	let {
		get_documents,
		get_progress_note,
		get_words,
		get_clinich_info,
		get_upload,
		get_document,
		get_document_setting
	} = $derived(data);
	let title = $derived(page.url.searchParams.get('title') ?? '');
	let birth_certificate_1 = $derived(get_documents.find((e) => e.title === 'birth_certificate_1'));
</script>

<div>
	{#if title === 'accept_leaving'}
		<AcceptLeaving
			death_leave={true}
			p_name={get_progress_note?.patient?.name_khmer
				?.concat(`(${get_progress_note?.patient?.name_latin}) `)
				.concat(
					`ភេទ ${get_progress_note?.patient?.gender.toLowerCase().replace('male', 'ប្រុស').replace('female', 'ស្រី')} `
				)
				.concat(
					`អាយុ ${dobToAge(get_progress_note?.patient?.dob ?? '', get_progress_note?.date_checkup ?? '')}`
				)
				.toLowerCase()
				.replace('month', 'ខែ')
				.toLowerCase()
				.replace('year', 'ឆ្នាំ')
				.toLowerCase()
				.replace('day', 'ថ្ងៃ') ?? ''}
			p_nation={get_progress_note?.patient?.nation ?? ''}
			address={{
				village: get_progress_note?.patient?.village,
				commune: get_progress_note?.patient?.commune,
				district: get_progress_note?.patient?.district,
				provice: get_progress_note?.patient?.provice
			}}
			{get_document_setting}
			fields={get_document?.fields ?? []}
			p_date_checkup={get_progress_note?.date_checkup ?? ''}
			p_date_checkout={get_progress_note?.date_checkout ?? ''}
			title_khm={get_clinich_info?.title_khm ?? ''}
			title_eng={get_clinich_info?.title_eng ?? ''}
			logo={get_upload?.filename ?? ''}
		/>
	{/if}
	{#if title === 'patient_transfer'}
		<PatientTranswer
			{get_document_setting}
			fields={get_document?.fields ?? []}
			p_address={{
				village: get_progress_note?.patient?.village,
				commune: get_progress_note?.patient?.commune,
				district: get_progress_note?.patient?.district,
				provice: get_progress_note?.patient?.provice
			}}
			p_name={get_progress_note?.patient?.name_khmer
				?.concat(`(${get_progress_note?.patient?.name_latin}) `)
				.concat(
					`ភេទ ${get_progress_note?.patient?.gender.toLowerCase().replace('male', 'ប្រុស').replace('female', 'ស្រី')} `
				)
				.concat(
					`អាយុ ${dobToAge(get_progress_note?.patient?.dob ?? '', get_progress_note?.date_checkup ?? '')}`
				)
				.toLowerCase()
				.replace('month', 'ខែ')
				.toLowerCase()
				.replace('year', 'ឆ្នាំ')
				.toLowerCase()
				.replace('day', 'ថ្ងៃ') ?? ''}
			p_nation={get_progress_note?.patient?.nation ?? ''}
			p_date_checkup={get_progress_note?.date_checkup ?? ''}
			title_khm={get_clinich_info?.title_khm ?? ''}
			title_eng={get_clinich_info?.title_eng ?? ''}
			logo={get_upload?.filename ?? ''}
		/>
	{/if}
	{#if title === 'birth_certificate_1'}
		<BirthCertificate
			{get_document_setting}
			p_contact={get_progress_note?.patient?.telephone ?? ''}
			p_dob={get_progress_note?.patient?.dob ?? ''}
			p_address={{
				village: get_progress_note?.patient?.village,
				commune: get_progress_note?.patient?.commune,
				district: get_progress_note?.patient?.district,
				provice: get_progress_note?.patient?.provice
			}}
			p_name_khmer={get_progress_note?.patient?.name_khmer ?? ''}
			p_nation={get_progress_note?.patient?.nation ?? ''}
			p_date_checkup={get_progress_note?.date_checkup ?? ''}
			nations_list={get_words.filter((e) => e.type === 'nation').map((e) => e.text)}
			fields_={get_document?.fields ?? []}
			birth_certificate="birth_certificate_1"
			title_khm={get_clinich_info?.title_khm ?? ''}
			title_eng={get_clinich_info?.title_eng ?? ''}
			logo={get_upload?.filename ?? ''}
		/>
	{/if}
	{#if title === 'birth_certificate_2'}
		<BirthCertificate
			{get_document_setting}
			p_contact={get_progress_note?.patient?.telephone ?? ''}
			p_dob={get_progress_note?.patient?.dob ?? ''}
			p_address={{
				village: get_progress_note?.patient?.village,
				commune: get_progress_note?.patient?.commune,
				district: get_progress_note?.patient?.district,
				provice: get_progress_note?.patient?.provice
			}}
			p_name_khmer={get_progress_note?.patient?.name_khmer ?? ''}
			p_nation={get_progress_note?.patient?.nation ?? ''}
			p_date_checkup={get_progress_note?.date_checkup ?? ''}
			nations_list={get_words.filter((e) => e.type === 'nation').map((e) => e.text)}
			fields_={get_document?.fields ?? []}
			birth_certificate="birth_certificate_2"
			title_khm={get_clinich_info?.title_khm ?? ''}
			title_eng={get_clinich_info?.title_eng ?? ''}
			logo={get_upload?.filename ?? ''}
			fields_1={birth_certificate_1?.fields ?? []}
		/>
	{/if}
	{#if title === 'birth_certificate_3'}
		<BirthCertificate
			{get_document_setting}
			p_contact={get_progress_note?.patient?.telephone ?? ''}
			p_dob={get_progress_note?.patient?.dob ?? ''}
			p_address={{
				village: get_progress_note?.patient?.village,
				commune: get_progress_note?.patient?.commune,
				district: get_progress_note?.patient?.district,
				provice: get_progress_note?.patient?.provice
			}}
			p_name_khmer={get_progress_note?.patient?.name_khmer ?? ''}
			p_nation={get_progress_note?.patient?.nation ?? ''}
			p_date_checkup={get_progress_note?.date_checkup ?? ''}
			nations_list={get_words.filter((e) => e.type === 'nation').map((e) => e.text)}
			fields_={get_document?.fields ?? []}
			birth_certificate="birth_certificate_3"
			title_khm={get_clinich_info?.title_khm ?? ''}
			title_eng={get_clinich_info?.title_eng ?? ''}
			logo={get_upload?.filename ?? ''}
			fields_1={birth_certificate_1?.fields ?? []}
		/>
	{/if}
	{#if title === 'birth_certificate_4'}
		<BirthCertificate
			{get_document_setting}
			p_contact={get_progress_note?.patient?.telephone ?? ''}
			p_dob={get_progress_note?.patient?.dob ?? ''}
			p_address={{
				village: get_progress_note?.patient?.village,
				commune: get_progress_note?.patient?.commune,
				district: get_progress_note?.patient?.district,
				provice: get_progress_note?.patient?.provice
			}}
			p_name_khmer={get_progress_note?.patient?.name_khmer ?? ''}
			p_nation={get_progress_note?.patient?.nation ?? ''}
			p_date_checkup={get_progress_note?.date_checkup ?? ''}
			nations_list={get_words.filter((e) => e.type === 'nation').map((e) => e.text)}
			fields_={get_document?.fields ?? []}
			birth_certificate="birth_certificate_4"
			title_khm={get_clinich_info?.title_khm ?? ''}
			title_eng={get_clinich_info?.title_eng ?? ''}
			logo={get_upload?.filename ?? ''}
			fields_1={birth_certificate_1?.fields ?? []}
		/>
	{/if}
</div>

<style>
	@page {
		size: A4 portrait;
		margin: 30px;
	}
</style>
