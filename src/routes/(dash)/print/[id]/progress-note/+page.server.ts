import { db } from '$lib/server/db';
import { progressNote, visit } from '$lib/server/schemas';
import { eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';

export const load = (async ({ params, url }) => {
	const visit_id = url.searchParams.get('visit_id') || '';
	const is_visit = url.searchParams.has('visit_id');
	const { id } = params;
	const get_exam = await db.query.exam.findMany({
		with: {
			physical: true
		}
	});
	const get_progress_note = await db.query.progressNote.findFirst({
		where: eq(progressNote.id, +id),
		with: {
			department: true,
			staff: true,
			patient: {
				with: {
					village: true,
					district: true,
					commune: true,
					provice: true
				}
			},
			visit: {
				where: is_visit ? eq(visit.id, +visit_id) : undefined,
				with: {
					service: {
						with: {
							product: true,
							operationProtocol: true
						}
					},
					subjective: true,
					physicalExam: {
						with: {
							physical: {
								with: {
									exam: true
								}
							}
						}
					},
					department: true,
					accessment: true,
					adviceTeaching: true,
					appointment: true,
					vitalSign: true,
					staff: true,
					laboratoryRequest: {
						with: {
							product: {
								with: {
									laboratoryGroup: true
								}
							}
						}
					},
					imagerieRequest: {
						with: {
							product: true
						}
					},
					presrciption: {
						with: {
							product: {
								with: {
									group: true,
									unit: true
								}
							}
						}
					}
				}
			}
		}
	});

	return { get_progress_note, get_exam };
}) satisfies PageServerLoad;
