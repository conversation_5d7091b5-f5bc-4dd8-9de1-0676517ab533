<script lang="ts">
	import Sign from '$lib/coms-report/Sign.svelte';
	import Athtml from '$lib/coms/Athtml.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { dobToAge } from '$lib/helper';
	import type { PageServerData } from './$types';
	let { data }: { data: PageServerData } = $props();
	let { get_exam, get_progress_note } = $derived(data);
	function mean_arterial_pressure(sbp: number, dbp: number) {
		return (1 / 3) * sbp + (2 / 3) * dbp;
	}
</script>

<h5 class="kh_font_muol_light text-center pt-1">{'ការព្យាបាលជំងឺប្រចាំថ្ងៃ'}</h5>
<h5 class="en_font_times_new_roman text-center pt-1">Progress Note (Date)</h5>
<u class="fs-5"
	>I. <span class="kh_font_muol_light">
		{'ព័តមានអ្នកជំងឺ'}
	</span></u
>
<ul>
	<p
		class="fs-5"
		style="  text-align:justify;  
	text-justify:initial;}"
	>
		{'(ID)'} PT{get_progress_note?.patient_id} VS{get_progress_note?.id} <br />
		{'ឈ្មោះ '}{get_progress_note?.patient?.name_khmer ?? ''}
		{`(${get_progress_note?.patient?.name_latin ?? ''})`}
		{'ភេទ '}{get_progress_note?.patient.gender === 'Male' ? 'ប្រុស' : 'ស្រី'}
		{'ថ្ងៃខែឆ្នាំកំណើត'}
		<DDMMYYYYFormat date={get_progress_note?.patient?.dob} style="date" />
		{'អាយុ'}
		{dobToAge(get_progress_note?.patient.dob, get_progress_note?.date_checkup) ?? '........'}
		<br />
		{'មុខរបរ........................'}
		<br />
		{'អាសយដ្ឋាន'}
		{get_progress_note?.patient?.village?.type ?? ''}
		{get_progress_note?.patient?.village?.name_khmer.concat(',') ?? ''}
		{get_progress_note?.patient?.commune?.type ?? ''}
		{get_progress_note?.patient?.commune?.name_khmer.concat(',') ?? ''}
		{get_progress_note?.patient?.district?.type ?? ''}
		{get_progress_note?.patient?.district?.name_khmer.concat(',') ?? ''}
		{get_progress_note?.patient?.provice?.type ?? ''}
		{get_progress_note?.patient?.provice?.name_khmer ?? ''} <br />
		{'ថ្ងៃខែឆ្នាំចូលពិនិត្យ'}
		<DDMMYYYYFormat date={get_progress_note?.date_checkup} style="date" />
		{'ផ្នែក'}
		{get_progress_note?.department?.products}
	</p>
</ul>
{#each get_progress_note?.visit || [] as item}
	<table class="table table-bordered table-light">
		<thead class="table-active">
			<tr>
				<td colspan="3"><DDMMYYYYFormat date={item.date_checkup} /> </td>
			</tr>
			<tr class="text-center">
				<td style="width: 25%">
					<div>VITAL SIGN</div>
					<div>Nurse's Name & Signature</div>
				</td>
				<td style="width: 40%">
					<div>EVOLUTION</div>
					<div>(SOAP Note)</div>
				</td>
				<td style="width: 33%">
					<div>TREATMENT</div>
					<div>Physician's Name & Signature</div>
				</td>
			</tr>
		</thead>
		<tbody>
			<tr>
				<td>
					<div>
						{#if item?.vitalSign}
							<div class="mx-0 px-0">
								<table class="table table-sm mx-0 px-0 table-light">
									<thead>
										{#if item?.vitalSign?.sbp}
											<tr>
												<td style="width: 40%;">BP(mmHg)</td>
												<td style="width: 5%;">:</td>
												<td style="width: 55%;">
													{item?.vitalSign?.sbp?.toFixed(0).concat(' /') ?? ''}
													{item?.vitalSign?.dbp?.toFixed(0).concat(' mmHg') ?? ''}
												</td>
											</tr>
										{/if}
										{#if mean_arterial_pressure(Number(item?.vitalSign?.sbp), Number(item?.vitalSign?.dbp))}
											<tr>
												<td style="width: 40%;">MAP</td>
												<td style="width: 5%;">:</td>
												<td style="width: 55%;">
													{mean_arterial_pressure(
														Number(item.vitalSign.sbp),
														Number(item.vitalSign?.dbp)
													)
														?.toFixed(0)
														.concat(' mmHg')}
												</td>
											</tr>
										{/if}
										{#if item?.vitalSign?.pulse}
											<tr>
												<td style="width: 40%;">Pulse (min)</td>
												<td style="width: 5%;">:</td>
												<td style="width: 20%;"
													>{item?.vitalSign?.pulse?.toFixed(0).concat(' /min') ?? ''}</td
												>
											</tr>
										{/if}
										{#if item?.vitalSign?.t}
											<tr>
												<td style="width: 40%;">Temper °C</td>
												<td style="width: 5%;">:</td>
												<td style="width: 55%;"
													><Athtml
														html={item?.vitalSign?.t?.toFixed(1).concat(' &deg;C') ?? ''}
													/></td
												>
											</tr>
										{/if}
										{#if item?.vitalSign?.rr}
											<tr>
												<td style="width: 40%;">RR (min)</td>
												<td style="width: 5%;">:</td>
												<td style="width: 55%;"
													>{item?.vitalSign?.rr?.toFixed(0).concat(' /min') ?? ''}</td
												>
											</tr>
										{/if}
										{#if item?.vitalSign?.sp02}
											<tr>
												<td style="width: 40%;">SpO2 (%)</td>
												<td style="width: 5%;">:</td>
												<td style="width: 55%;"
													>{item?.vitalSign?.sp02?.toFixed(0).concat(' %') ?? ''}</td
												>
											</tr>
										{/if}
										{#if item?.vitalSign?.height}
											<tr>
												<td style="width: 40%;">Height (cm)</td>
												<td style="width: 5%;">:</td>
												<td style="width: 55%;"
													>{item?.vitalSign?.height?.toFixed(0).concat(' cm') ?? ''}</td
												>
											</tr>
										{/if}
										{#if item?.vitalSign?.weight}
											<tr>
												<td style="width: 40%;">Weight (kg)</td>
												<td style="width: 5%;">:</td>
												<td style="width: 55%;"
													>{item?.vitalSign?.weight?.toFixed(0).concat(' kg') ?? ''}</td
												>
											</tr>
										{/if}
										{#if item?.vitalSign?.bmi}
											<tr>
												<td style="width: 40%;">BMI</td>
												<td style="width: 5%;">:</td>
												<td style="width: 55%;"
													>{item?.vitalSign?.bmi?.toFixed(1).concat(' kg/m2') ?? ''}</td
												>
											</tr>
										{/if}
									</thead>
								</table>
								<div class="row">
									<Sign
										left={{
											date: item.date_checkup,
											name: item?.staff?.name_khmer,
											role: `Sign's Nurse/Midwife`,
											img: '/sign.png'
										}}
									/>
								</div>
							</div>
						{/if}
					</div>
				</td>
				<td>
					<div>
						{#if item.subjective}
							<li>{'Subjective (ការសាកសួរពីអ្នកជំងឺ)'}</li>
							<table class="table text-break table-light mx-0 px-0">
								<tbody>
									{#if item?.subjective?.cheif_complaint}
										<tr>
											<td>Cheif Complaint</td>
											<td>
												{item?.subjective?.cheif_complaint}
											</td>
										</tr>
									{/if}
									{#if item?.subjective?.history_of_present_illness}
										<tr>
											<td style="width: 35%;">History of present illness</td>
											<td>{item?.subjective?.history_of_present_illness}</td>
										</tr>
									{/if}
									{#if item?.subjective?.current_medication}
										<tr>
											<td style="width: 35%;">Current Medication</td>
											<td>{item?.subjective?.current_medication}</td>
										</tr>
									{/if}

									{#if item?.subjective?.past_medical_history}
										<tr>
											<td style="width: 35%;">Past medical history</td>
											<td>{item?.subjective?.past_medical_history}</td>
										</tr>
									{/if}

									{#if item?.subjective?.allesgy_medicine}
										<tr>
											<td style="width: 35%;">Allergy medical</td>
											<td>
												{item?.subjective?.allesgy_medicine}
											</td>
										</tr>
									{/if}

									{#if item?.subjective?.surgical_history}
										<tr>
											<td style="width: 35%;">Surgical history</td>
											<td>{item?.subjective?.surgical_history}</td>
										</tr>
									{/if}

									{#if item?.subjective?.family_and_social_history}
										<tr>
											<td style="width: 35%;">Family and socail history</td>
											<td>{item?.subjective?.family_and_social_history}</td>
										</tr>
									{/if}

									{#if item?.subjective?.pre_diagnosis}
										<tr>
											<td style="width: 35%;">Pre diagnosis</td>
											<td>{item?.subjective?.pre_diagnosis}</td>
										</tr>
									{/if}
								</tbody>
							</table>
						{/if}
					</div>
					<div>
						{#if item?.physicalExam.length}
							<li class=" pt-2">Physical Examination and Evolution</li>
							{#each get_exam as exam}
								{#if item.physicalExam.some((e) => e.physical?.exam_id === exam.id)}
									<div class="card-body mt-0 pt-0 table-responsive p-0">
										<table
											class="table mb-0 table-bordered table-hover text-wrap text-break table-light"
										>
											<thead class="table-active">
												<tr class="">
													<td colspan="2">{exam?.examination}</td>
												</tr>
											</thead>
											<tbody>
												{#each item.physicalExam as physicalExam}
													{#if exam.physical.some((e) => e.id === physicalExam.physical_id)}
														{#if physicalExam?.result}
															<tr>
																<td style="width: 50%;">{physicalExam.physical?.physical} </td>
																<td>{physicalExam.result} </td>
															</tr>
														{/if}
													{/if}
												{/each}
											</tbody>
										</table>
									</div>
								{/if}
							{/each}
						{/if}
						{#if item.laboratoryRequest.length || item.imagerieRequest.length}
							<li class=" pt-2">Para-Clinic (Request)</li>
						{/if}
						<div class="row">
							{#if item.laboratoryRequest.length}
								<div class="col-12">
									<div class="card">
										<div class="card-header">Laboratory</div>
										<table class="table table-sm table-borderless table-light">
											<tbody>
												<tr>
													<td>
														<ol>
															{#each item?.laboratoryRequest || [] as laboratory}
																<li class="">{laboratory.product?.products}</li>
															{/each}
														</ol>
													</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							{/if}
							{#if item.imagerieRequest.length}
								<div class="col-12">
									<div class="card">
										<div class="card-header">Imagerie</div>
										<table class="table table-sm table-borderless table-light">
											<tbody>
												<tr>
													<td>
														<ol>
															{#each item?.imagerieRequest || [] as imagerie}
																<li class="">{imagerie.product?.products}</li>
															{/each}
														</ol>
													</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							{/if}
						</div>
					</div>
					<div>
						<li class="">
							Accessment <span class="kh_font_muol_light">{'(ការវាយតម្លៃនិងរោគវិនិច្ឆ័យ)'}</span>
						</li>
						<div>
							{#if item?.accessment?.diagnosis_or_problem}
								<u class=" pt-2">Diagnosis</u>
								<div>
									{item?.accessment?.diagnosis_or_problem}
								</div>
								<br />
							{/if}
							{#if item?.accessment?.differential_diagnosis}
								<u class="pt-2">Differential Diagnosis</u>
								<div>
									{item?.accessment?.differential_diagnosis}
								</div>
								<br />
							{/if}
							{#if item?.accessment?.assessment_process}
								<u class=" pt-2">Accessment Process</u>
								<div class="text-break">
									{item?.accessment?.assessment_process}
								</div>
								<br />
							{/if}
						</div>
					</div>
				</td>
				<td>
					<div>
						{#each item?.presrciption || [] as presrciption, index}
							<div>
								<span class="fs-6 text-decoration-underline text-primary"
									>{index + 1}
									{presrciption.product?.products ?? ''}
									{presrciption.product?.generic_name
										? `, (  ${presrciption.product?.generic_name ?? ''} )`
										: ''}
								</span>,

								<span>{presrciption.use ?? ''}</span>
								<span>
									{#if presrciption.morning !== 0}
										{'ព្រឹក'} {presrciption.morning}
									{/if}
								</span>
								<span>
									{#if presrciption.noon !== 0}
										{'ថ្ងៃត្រង់'} {presrciption.noon}
									{/if}
								</span>
								<span>
									{#if presrciption.afternoon !== 0}
										{'រសៀល'} {presrciption.afternoon}
									{/if}
								</span>
								<span>
									{#if presrciption.evening !== 0}
										{'ល្ងាច'} {presrciption.evening}
									{/if}
								</span>
								<span>
									{#if presrciption.night !== 0}
										{'យប់'} {presrciption.night}
									{/if}
								</span>
								<span>
									,{'ចំនួន'}
									{presrciption.amount ?? ''}
									{presrciption.product?.unit?.unit},
								</span>
								<span>
									{'រយៈពេល'}
									{presrciption.duration ?? ''}
								</span>
								<hr />
							</div>
						{/each}
						{#if item?.adviceTeaching?.description}
							<li class="">Advice or Teaching</li>
							<div class="text-break">
								{item?.adviceTeaching?.description}
							</div>
						{/if}
						{#if item?.appointment}
							<li class="">Appoinment</li>
							<div class="text-break">
								<DDMMYYYYFormat date={item?.appointment?.datetime} />
								{item?.appointment?.description}
							</div>
						{/if}
						{#if item.presrciption.length}
							<hr />
							<div class="row">
								<Sign
									left={{
										date: item.date_checkup,
										name: item?.staff?.name_khmer,
										role: `Sign's Physician`,
										img: '/sign.png'
									}}
								/>
							</div>
						{/if}
					</div>
				</td>
			</tr>
		</tbody>
	</table>
{/each}

<style>
	@media print {
		/* tr,
		td,
		table {
			page-break-inside: auto;
		} */
		@page {
			size: A4;
			margin-top: 5mm;
			padding-bottom: 5mm;
		}
	}
	td {
		vertical-align: top;
	}
</style>
