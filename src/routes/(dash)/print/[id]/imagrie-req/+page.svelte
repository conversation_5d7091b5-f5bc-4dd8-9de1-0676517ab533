<script lang="ts">
	import { page } from '$app/state';
	import ClinichInfo from '$lib/coms-report/ClinichInfo.svelte';
	import Sign from '$lib/coms-report/Sign.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { dobToAge } from '$lib/helper';
	import type { PageServerData } from './$types';
	let { data }: { data: PageServerData } = $props();
	let { get_clinic_info, get_visit, sort_by_group, get_upload, get_sign } = $derived(data);
	let age_p_visit = $derived(dobToAge(get_visit?.patient.dob ?? '', get_visit?.date_checkup ?? ''));
</script>

<div class="header">
	<ClinichInfo data={{ get_clinic_info, get_upload }} />
	<div class="border table-responsive border-dark-subtle border-2 p-2">
		<table class="table table-borderless m-0">
			<tbody>
				<tr>
					<td style="width: 190px;" class="text-bold kh_font_battambang py-0">ឈ្មោះអ្នកជំងឺ</td>
					<td class="py-0">:</td>
					<td class="py-0">{get_visit?.patient?.name_khmer}</td>
					<td class="text-bold kh_font_battambang py-0">ភេទ</td>
					<td class="py-0">:</td>
					<td class="py-0"
						>{get_visit?.patient?.gender.toLowerCase() === 'female'
							? 'ស្រី'
							: get_visit?.patient?.gender.toLowerCase() === 'male'
								? 'ប្រុស'
								: ''}</td
					>
					<td class="text-bold kh_font_battambang py-0">លេខកូដ</td>
					<td class="py-0">:</td>
					<td class="py-0">PT{get_visit?.patient_id},VS{get_visit?.id} </td>
				</tr>
				<tr class="py-0">
					<td class="text-bold kh_font_battambang py-0">ឈ្មោះជាឡាតាំង</td>
					<td class="py-0">:</td>
					<td class="py-0">{get_visit?.patient?.name_latin}</td>
					<td class="text-bold kh_font_battambang py-0">អាយុ</td>
					<td class="py-0">:</td>
					<td class="py-0">
						{age_p_visit}
					</td>
					<td style="font-size: 110%;" class="text-bold en_font_times_new_roman py-0">Visit</td>
					<td class="py-0">:</td>
					<td style="font-size: 110%;" class="en_font_times_new_roman py-0"
						>{get_visit?.checkin_type ?? ''}</td
					>
				</tr>
				<tr class="py-0">
					<td class="text-bold kh_font_battambang py-0">ទំនាក់ទំនង</td>
					<td class="py-0">:</td>
					<td style="font-size: 110%;" class="en_font_times_new_roman py-0">
						{get_visit?.patient?.telephone ?? ''}
					</td>
					<td class="text-bold kh_font_battambang py-0">អាសយដ្ឋាន</td>
					<td class="py-0">:</td>
					<td colspan="6" class="kh_font_battambang py-0">
						{get_visit?.patient?.village?.type ?? ''}
						{get_visit?.patient?.village?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.commune?.type ?? ''}
						{get_visit?.patient?.commune?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.district?.type ?? ''}
						{get_visit?.patient?.district?.name_khmer.concat(',') ?? ''}
						{get_visit?.patient?.provice?.type ?? ''}
						{get_visit?.patient?.provice?.name_khmer ?? ''}
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="border table-responsive border-dark-subtle border-2 p-2 mb-2 mt-2">
		<table class="table table-borderless m-0">
			<thead>
				<tr class="py-0">
					<td style="width: 190px;" class="text-bold kh_font_battambang py-0">គ្រូពេទ្យស្នើរសុំ</td>
					<td class="py-0">:</td>
					<td class="py-0">{get_visit?.staff?.title?.kh} {get_visit?.staff?.name_khmer ?? ''}</td>

					<td class="text-bold kh_font_battambang py-0">កាលបរិច្ឆេទ-ស្នើរសុំ</td>
					<td class="py-0">:</td>
					<td class="py-0">
						<DDMMYYYYFormat date={get_visit?.date_checkup} />
					</td>
				</tr>
				<tr class="py-0">
					<td class="text-bold en_font_times_new_roman py-0">Diagnosis (Dx)</td>
					<td class="py-0"> : </td>
					<td colspan="4" class="kh_font_battambang py-0 text-break"
						>{get_visit?.accessment?.diagnosis_or_problem}
					</td>
				</tr>
			</thead>
		</table>
	</div>
</div>
<table class="w-100 table-light">
	<thead>
		<tr>
			<td>
				<div class="header-space">&nbsp;</div>
			</td>
		</tr>
	</thead>
	<tbody>
		<tr>
			<td>
				<h5 class="kh_font_muol_light text-center pt-1">{'ការស្នើរសុំរបស់គ្រូពេទ្យ'}</h5>
				<h4 class="en_font_times_new_roman text-center pt-1">Clinical Request</h4>
				<h4 class="en_font_times_new_roman pt-1 text-decoration-underline">#Imagerie Request</h4>
			</td>
		</tr>
		<tr>
			<td>
				<div class="card">
					<div class="d-flex justify-content-between align-items-start flex-wrap">
						{#each sort_by_group || [] as item, index}
							<div class="fs-6 p-2" style="flex: 0 0 48%;">
								{index + 1} : {item?.product?.products ?? ''}
								{`(IM${item?.id ?? ''})`}
							</div>
						{/each}
					</div>
				</div>
			</td>
		</tr>
	</tbody>
	<tfoot>
		<tr>
			<td>
				<div class="footer-space">&nbsp;</div>
			</td>
		</tr>
	</tfoot>
</table>

<div class="footer">
	<Sign
		right={{
			name: `${get_visit?.staff?.title?.kh ?? ''}  ${get_visit?.staff?.name_khmer ?? ''}`,
			img: get_sign?.filename,
			date: get_visit?.date_checkup,
			role: `ហត្ថលេខាគ្រូពេទ្យ`
		}}
		qr={page.url.href}
	/>

	<div>
		<hr />
		<h6 style="color:#0000FF" class="text-center">
			{get_clinic_info?.address ?? ''}
		</h6>
	</div>
</div>

<style>
	@media print {
		.footer,
		.footer-space {
			height: 300px;
		}
		.header,
		.header-space {
			height: 320px;
		}
	}
</style>
