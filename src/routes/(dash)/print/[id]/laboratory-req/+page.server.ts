import { db } from '$lib/server/db';
import { and, eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { uploads, visit } from '$lib/server/schemas';

export const load = (async ({ params }) => {
	const { id } = params;
	const get_clinic_info = await db.query.clinicinfo.findFirst({});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.mimeType, 'logo0'), eq(uploads.related_type, 'clinicinfo'))
	});
	const get_visit = await db.query.visit.findFirst({
		where: eq(visit.id, +id),
		with: {
			accessment: true,
			patient: {
				with: {
					village: true,
					district: true,
					commune: true,
					provice: true
				}
			},
			staff: {
				with: {
					title: true
				}
			},
			laboratoryRequest: {
				with: {
					product: {
						with: {
							laboratoryGroup: true
						}
					}
				}
			}
		}
	});

	const get_sign = await db.query.uploads.findFirst({
		where: and(
			eq(uploads.related_type, 'staffSign'),
			eq(uploads.related_id, get_visit?.staff_id || 0)
		)
	});
	const s = get_visit?.laboratoryRequest?.sort((a, b) => {
		const groupIdA = a?.product?.laboratory_group_id as number;
		const groupIdB = b?.product?.laboratory_group_id as number;
		if (+groupIdA < +groupIdB) return -1;
		if (+groupIdA > +groupIdB) return 1;
		return 0;
	});

	return { get_clinic_info, get_visit, sort_by_group: s, get_upload, get_sign };
}) satisfies PageServerLoad;
