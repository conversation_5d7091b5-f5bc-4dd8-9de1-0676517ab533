import { db } from '$lib/server/db';
import { asc, eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { activeBed, activeDepartment, progressNote } from '$lib/server/schemas';

export const load: PageServerLoad = async ({ params }) => {
	const { id } = params;
	const get_clinic_info = await db.query.clinicinfo.findFirst({});
	const get_progress_note = await db.query.progressNote.findFirst({
		where: eq(progressNote.id, +id),
		with: {
			patient: true,
			activeDepartment: {
				with: {
					activeBed: {
						where: eq(activeBed.active, true),
						with: {
							bed: {
								with: {
									ward: true,
									room: true
								}
							}
						}
					},
					department: true
				},
				orderBy: asc(activeDepartment.id)
			},
			nursingProcess: {
				with: {
					nursingSign: true,
					activeDepartment: true
				}
			}
		}
	});
	return {
		get_clinic_info,
		get_progress_note
	};
};
