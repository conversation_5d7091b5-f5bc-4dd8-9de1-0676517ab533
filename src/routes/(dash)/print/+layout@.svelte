<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	interface Props {
		children?: import('svelte').Snippet;
	}
	let { children }: Props = $props();
	function handlePrint() {
		window.scrollTo({ top: 0, behavior: 'smooth' });
		setTimeout(() => {
			window.print();
		}, 500); // Adjust the timeout as needed
	}
</script>

<svelte:head>
	<style>
		@media print {
			.footer,
			.footer-space {
				height: 280px;
			}
			.header,
			.header-space {
				height: 350px;
			}
			.header {
				width: 100%;
				position: fixed;
			}
			.footer {
				width: 100%;
				position: fixed;
				bottom: 0mm;
			}
			.main {
				zoom: 100% !important;
			}
		}
	</style>
</svelte:head>
<div class="main border-2">
	<button
		onclick={handlePrint}
		class="text-center rounded-0 sticky-top w-100 mt-1 mb-1 btn btn-primary btn-lg d-print-none text-decoration-none"
	>
		<i class="fa-solid fa-print"></i>
		{locale.T('print')}
	</button>
	<div class="">
		{@render children?.()}
	</div>
</div>

<style>
	@page {
		@bottom-right {
			content: counter(page) '/' counter(pages);
			font-size: 13px;
			padding-bottom: 5mm;
			size: A4;
		}
		margin: 30px;
	}
	.main {
		max-width: 1200px;
		margin-left: auto;
		margin-right: auto;
		zoom: 85%;
	}
</style>
