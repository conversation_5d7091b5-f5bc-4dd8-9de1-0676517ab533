import type { LayoutServerLoad } from './$types';
import { db } from '$lib/server/db';
import { and, eq, isNull, ne, or } from 'drizzle-orm';
import { billing, laboratory, progressNote, visit, imagerieRequest } from '$lib/server/schemas';
import { redirect } from '@sveltejs/kit';
import { appointment } from '$lib/server/schemas/appointment';

export const load: LayoutServerLoad = async ({ locals, cookies }) => {
	if (!locals.session || !locals.user || !locals.roles?.length) {
		redirect(302, '/login');
	}
	const count_opd_ = () =>
		db.$count(visit, and(eq(visit.checkin_type, 'OPD'), ne(visit.status, 'DONE')));
	const count_ipd_ = () => db.$count(progressNote, isNull(progressNote.date_checkout));
	const count_pay_opd_ = () =>
		db.$count(billing, and(eq(billing.status, 'paying'), eq(billing.billing_type, 'OPD')));
	const count_pay_ipd_ = () =>
		db.$count(
			billing,
			and(
				eq(billing.status, 'paying'),
				or(eq(billing.billing_type, 'IPD'), eq(billing.billing_type, 'CHECKING'))
			)
		);
	const count_appoinment_ = () => db.$count(appointment, eq(appointment.status, false));
	const get_currency_ = () => db.query.currency.findFirst({});
	const lang = cookies.get('lang');
	const get_clinich_info_ = () => db.query.clinicinfo.findFirst({});
	const get_progress_note_ = () =>
		db.query.progressNote.findMany({
			where: isNull(progressNote.date_checkout),
			with: {
				patient: true,
				activeBed: {
					with: {
						bed: {
							with: {
								room: {
									with: {
										product: true
									}
								}
							}
						}
					}
				}
			}
		});
	const get_wards_ = () =>
		db.query.ward.findMany({
			with: {
				room: {
					with: {
						department: true,
						product: true,
						bed: true
					}
				}
			}
		});
	const count_laboratory_ = () => db.$count(laboratory, eq(laboratory.status, false));
	const count_imagrie_ = () => db.$count(imagerieRequest, eq(imagerieRequest.status, false));
	const [
		count_opd,
		count_ipd,
		count_pay_opd,
		count_pay_ipd,
		count_appoinment,
		get_currency,
		get_clinich_info,
		get_progress_note,
		get_wards,
		count_laboratory,
		count_imagrie
	] = await Promise.all([
		count_opd_(),
		count_ipd_(),
		count_pay_opd_(),
		count_pay_ipd_(),
		count_appoinment_(),
		get_currency_(),
		get_clinich_info_(),
		get_progress_note_(),
		get_wards_(),
		count_laboratory_(),
		count_imagrie_()
	]);
	return {
		user: locals.user,
		lang: lang,
		get_clinich_info,
		get_progress_note,
		get_wards,
		get_currency,
		count_opd,
		count_ipd,
		count_pay_opd,
		count_pay_ipd,
		count_appoinment,
		count_laboratory,
		count_imagrie
	};
};
