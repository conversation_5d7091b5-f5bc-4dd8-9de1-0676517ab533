import { db } from '$lib/server/db';
import { uploads, inventory, product } from '$lib/server/schemas';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { and, eq } from 'drizzle-orm';
import { fileHandle } from '$lib/server/upload';
import logError from '$lib/server/utils/logError';
import { productQuery } from '$lib/server/models/productQuery';
export const load = (async ({ url, parent }) => {
	await parent();
	const get_currency = await db.query.currency.findFirst({});
	const get_groups = await db.query.group.findMany({});
	const get_categories = await db.query.category.findMany({});
	const get_suppliers = await db.query.supplier.findMany({});
	const get_units = await db.query.unit.findMany({});
	const get_products = await productQuery({ url, page: true });
	return {
		get_products: get_products.get_products,
		get_categories,
		get_units,
		get_currency,
		get_groups,
		items: get_products.items,
		get_suppliers
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	delete_product: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			serverError: false
		};
		const get_upload = await db.query.uploads.findFirst({
			where: and(eq(uploads.related_type, 'product'), eq(uploads.related_id, +id))
		});
		await db
			.delete(inventory)
			.where(eq(inventory.product_id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		await db
			.delete(product)
			.where(eq(product.id, Number(id)))
			.catch((e) => {
				logError({ url, body, err: e });
			});
		if (get_upload) {
			await fileHandle.drop(get_upload.filename as string);
		}
		if (Object.values(validErr).includes(true)) return fail(500, validErr);
	}
};
