import { db } from '$lib/server/db';
import { fail, redirect } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { inventory, product, exspend, payment } from '$lib/server/schemas';
import { pagination } from '$lib/server/utils';
import { eq, like, asc, or } from 'drizzle-orm';
import logError from '$lib/server/utils/logError';
import { pushProductToExspend } from '$lib/server/models';
// import { now_datetime } from '$lib/server/utils';
async function totalExspend(id: number) {
	const get_amount = await db.query.exspend.findFirst({
		where: eq(exspend.id, id),
		with: { inventory: true, payment: true }
	});
	const total_payment = get_amount?.payment.reduce((s, e) => s + Number(e.value), 0);
	const amount = get_amount?.inventory?.reduce((s, e) => s + Number(e.total_expense), 0);
	await db
		.update(exspend)
		.set({ amount: amount, paid: total_payment, credit: Number(amount) - Number(total_payment) })
		.where(eq(exspend.id, id));
	redirect(303, `?exspend_id=${id}`);
}
export const load = (async ({ url }) => {
	const q = url.searchParams.get('q') || '';
	const exspend_id = url.searchParams.get('exspend_id') || '';

	const get_exspend = await db.query.exspend.findFirst({
		where: eq(exspend.id, +exspend_id)
	});
	const get_payments = await db.query.payment.findMany({
		where: eq(payment.exspend_id, +exspend_id)
	});
	const get_suppliers = await db.query.supplier.findMany({});
	const get_products = await db.query.product.findMany({
		where: or(
			like(product.products, `%${q}%`),
			like(product.barcode, `%${q}%`),
			like(product.generic_name, `%${q}%`)
		),
		with: {
			unit: true,
			subUnit: {
				with: {
					unit: true
				}
			},
			inventory: {
				where: eq(inventory.is_close_inventory, false),
				with: {
					costUnit: true
				}
			}
		},
		orderBy: asc(product.products),
		limit: 200
	});
	const get_currency = await db.query.currency.findFirst({});
	const get_inventories = await db.query.inventory.findMany({
		where: eq(inventory.exspend_id, +exspend_id),
		with: {
			costUnit: true,
			product: {
				with: {
					subUnit: {
						with: {
							unit: true
						}
					},
					unit: true
				}
			}
		},
		...pagination(url)
	});
	const items = await db.$count(inventory, eq(inventory.exspend_id, +exspend_id));
	return {
		get_suppliers,
		get_currency,
		get_products,
		get_exspend,
		get_payments,
		get_inventories,
		items
	};
}) satisfies PageServerLoad;
export const actions: Actions = {
	create_exspend: async ({ request, url }) => {
		const body = await request.formData();
		const { datetime_invoice, invoice_no, exspend_id, supplier_id } = Object.fromEntries(
			body
		) as Record<string, string>;
		const validErr = {
			datetime_invoice: false,
			invoice_no: false,
			supplier_id: false
		};
		if (!datetime_invoice.trim()) validErr.datetime_invoice = true;
		if (!invoice_no.trim()) validErr.invoice_no = true;
		if (!supplier_id.trim()) validErr.supplier_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		if (exspend_id) {
			await db
				.update(exspend)
				.set({
					invoice_no: invoice_no,
					supplier_id: +supplier_id,
					datetime_invoice: datetime_invoice
				})
				.where(eq(exspend.id, +exspend_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		if (!exspend_id) {
			await db
				.insert(exspend)
				.values({
					invoice_no: invoice_no,
					supplier_id: +supplier_id,
					datetime_invoice: datetime_invoice
				})
				.$returningId()
				.catch((e) => {
					logError({ url, body, err: e });
					return [];
				});
		}
	},
	delete_exspend: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		if (isNaN(+id)) return fail(400, { id: true });
		await db
			.delete(exspend)
			.where(eq(exspend.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	add_product: async ({ request, url }) => {
		const body = await request.formData();
		const { exspend_id, product_id } = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			product_id: false,
			exspend_id: false
		};
		if (!product_id.trim()) validErr.product_id = true;
		if (!exspend_id.trim()) validErr.exspend_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		const pushProductToExspendResult = await pushProductToExspend({
			exspend_id: +exspend_id,
			product_id: +product_id,
			body: body,
			url: url
		});
		if (pushProductToExspendResult?.data) {
			if (Object.values(pushProductToExspendResult?.data).includes(true))
				return fail(400, pushProductToExspendResult?.data);
		}
	},
	remove_product: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		if (isNaN(+id)) return fail(400, { id: true });
		const get_inventory = await db.query.inventory.findFirst({
			where: eq(inventory.id, +id)
		});
		const get_product = await db.query.product.findFirst({
			with: {
				inventory: true
			},
			where: eq(product.id, Number(get_inventory?.product_id))
		});
		if (Number(get_product?.inventory.length) <= 1) {
			await db
				.update(inventory)
				.set({ exspend_id: null, is_close_inventory: false })
				.where(eq(inventory.id, +id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		} else {
			await db
				.delete(inventory)
				.where(eq(inventory.id, +id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		await totalExspend(Number(get_inventory?.exspend_id));
	},
	update_exspend_and_inventory: async ({ request, url }) => {
		const body = await request.formData();
		const {
			qty_per_unit,
			qty_bought,
			cost,
			cost_unit_id,
			group_id,
			datetime_expire,
			is_count_stock,
			inventory_id,
			exspend_id
		} = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			exspend_id: false,
			amount: false,
			exspend: false,
			qty_bought: false,
			cost: false,
			group_id: false,
			cost_unit_id: false,
			qty_per_unit: false,
			inventory_id: false,
			datetime_expire: false,
			is_count_stock: false
		};

		if (!cost) validErr.cost = true;
		if (!qty_bought) validErr.qty_bought = true;
		if (!exspend_id) validErr.exspend_id = true;
		if (!inventory_id) validErr.inventory_id = true;
		if (!qty_per_unit) validErr.qty_per_unit = true;
		if (!cost_unit_id) validErr.cost_unit_id = true;
		if (!group_id) validErr.group_id = true;

		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		if (inventory_id) {
			await db
				.update(inventory)
				.set({
					group_id: +group_id,
					qty_bought: +qty_bought,
					qty_adjustment: +qty_bought * +qty_per_unit,
					total_expense: +qty_bought * +cost,
					is_count_stock: is_count_stock !== undefined ? true : false,
					datetime_expire: datetime_expire ? datetime_expire : null,
					cost: +cost || 0,
					cost_unit_id: cost_unit_id ? +cost_unit_id : null
				})
				.where(eq(inventory.id, +inventory_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});
		}
		await totalExspend(Number(exspend_id));
	}
};
