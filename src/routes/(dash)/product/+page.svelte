<script lang="ts">
	import type { ActionData, PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import { store } from '$lib/store/store.svelte';
	import Toast from '$lib/coms/Toast.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import HandleQ from '$lib/coms-form/HandleQ.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import { page } from '$app/state';
	import SetBack from '$lib/coms/SetBack.svelte';
	import UnitsAdjust from '$lib/coms/UnitsAdjust.svelte';
	interface Props {
		form: ActionData;
		data: PageServerData;
	}
	let { form, data }: Props = $props();
	let product_id: number | null = $state(null);
	let { get_products, get_categories, get_currency, items, get_groups } = $derived(data);
	let n: number = $state(1);
	let category_id: number | null = $derived(
		Number(page.url.searchParams.get('qcategory_id')) || null
	);
	let groups_f = $derived(
		category_id ? get_groups.filter((e) => e.category_id === category_id) : get_groups
	);
</script>

<DeleteModal action="?/delete_product" id={get_products.find((e) => e.id === product_id)?.id} />
{#if form?.serverError}
	<Toast toas="error" message="Can't delete" />
{/if}
<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('list_products')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('products')}
				</a>
			</li>
		</ol>
	</div>
</div>
<div class="card bg-light">
	<div class="card-header">
		<div class="row gap-1">
			<div class="col">
				<HeaderQuery>
					<div class="col-sm-3">
						<SelectParam
							value={Number(page.url.searchParams.get('qcategory_id'))}
							name="category_id"
							placeholder={locale.T('category')}
							items={get_categories.map((e) => ({ id: e.id, name: e.name }))}
						/>
					</div>
					<div class="col-sm-3">
						<SelectParam
							value={Number(page.url.searchParams.get('qgroup_id'))}
							name="group_id"
							placeholder={locale.T('product_group')}
							items={groups_f.map((e) => ({ id: e.id, name: e.name }))}
						/>
					</div>
					<div class="col-sm-3">
						<HandleQ />
					</div>
				</HeaderQuery>
			</div>

			<div class="col-auto text-end">
				<a href="/product/create" class="btn btn-success"
					><i class="fa-solid fa-square-plus"></i>
					{locale.T('new_product')}
				</a>
			</div>
		</div>
	</div>

	<div style="height: {store.inerHight};" class="card-body table-responsive p-0 m-0">
		<table class="table table-bordered table-hover text-nowrap table-light">
			<thead class="sticky-top top-0 bg-light table-active">
				<tr class="text-center">
					<th style="width: 3%;" class="text-center">{locale.T('n')}</th>
					<th style="width: 24%;">{locale.T('picture')} {locale.T('products')}</th>
					<th style="width: 10%;">{locale.T('category')}</th>
					<th style="width: 10%;">{locale.T('product_group')}</th>
					<th style="width: 12.5%;">{locale.T('sales_price')}</th>
					<th style="width: 10%;">{locale.T('amount_total')}</th>
					<th style="width: 10%;">{locale.T('amount_expire')}</th>
					<th style="width: 10%;">{locale.T('amount_used')}</th>
					<th style="width: 10%;">{locale.T('amount_available')}</th>
					<th style="width: 10%;">{locale.T('action')}</th>
				</tr>
			</thead>
			<tbody>
				{#each get_products as item, index}
					<tr>
						<td class="text-center">{n + index}</td>
						<td class="text-start">
							<div class="row g-2">
								<div style="width: 60px;" class="col-auto">
									<img
										src={item.uploads?.filename ? `${item.uploads?.filename}` : '/no-image.jpg'}
										alt=""
										style="height:50px"
									/>
								</div>
								<div class="col-auto">
									<SetBack href="/product/{item.id}" class="btn btn-link p-0">
										{item.products}
									</SetBack>

									<br />
									<span class="badge bg-primary">
										{item.generic_name ?? ''}
									</span>
								</div>
							</div>
						</td>
						<td class="text-center">
							{item.category?.name ?? ''}
						</td>
						<td class="text-center">
							{item.group?.name ?? ''}
						</td>
						<td>
							<Currency amount={item?.price} symbol={get_currency?.currency} /> / {item?.unit
								?.unit ?? ''}
							{#each item?.subUnit || [] as iitem (iitem.id)}
								<br />
								<Currency amount={iitem?.price} symbol={get_currency?.currency} /> / {iitem?.unit
									?.unit ?? ''}
							{/each}
						</td>
						<td>
							<UnitsAdjust
								qty_adjustment={item.qty_adjustment}
								unit={item.unit}
								subUnit={item.subUnit}
							/>
						</td>
						<td class="text-danger">
							<UnitsAdjust
								qty_adjustment={item.qty_expire}
								unit={item.unit}
								subUnit={item.subUnit}
							/>
						</td>
						<td class="text-success">
							<UnitsAdjust
								qty_adjustment={item.qty_order}
								unit={item.unit}
								subUnit={item.subUnit}
							/>
						</td>
						<td class="text-primary">
							<UnitsAdjust
								qty_adjustment={item.qty_available}
								unit={item.unit}
								subUnit={item.subUnit}
							/>
						</td>

						<td class="text-center">
							<div class=" m-0 p-0">
								<SetBack href="/product/create?product_id={item.id}" class="btn btn-primary btn-sm">
									<i class="fa-solid fa-file-pen"></i>
								</SetBack>
								<button
									aria-label="deletemodal"
									onclick={() => {
										product_id = item.id;
									}}
									type="button"
									class="btn btn-danger btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#delete_modal"
									><i class="fa-solid fa-trash-can"></i>
								</button>
								<button
									aria-label="deletemodal"
									onclick={() => {
										product_id = item.id;
									}}
									type="button"
									class="btn btn-info btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#delete_modal"
									><i class="fa-solid fa-boxes-stacked"></i>
								</button>
							</div>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations bind:n {items} />
	</div>
</div>
