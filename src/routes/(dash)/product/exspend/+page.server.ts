import { db } from '$lib/server/db';
import type { PageServerLoad, Actions } from './$types';
import { exspend, uploads } from '$lib/server/schemas';
import { and, eq, like, or, desc, isNotNull, inArray } from 'drizzle-orm';
import logError from '$lib/server/utils/logError';
import { fail } from '@sveltejs/kit';
import { pagination, betweenHelper } from '$lib/server/utils';
import { fileHandle } from '$lib/server/upload';
export const load = (async ({ url }) => {
	const q = url.searchParams.get('q') || '';
	const exspend_id = url.searchParams.get('exspend_id') ?? '';
	const get_exspends = await db.query.exspend.findMany({
		where: and(
			betweenHelper(url, exspend.datetime_invoice),
			isNotNull(exspend.exspend_type_id),
			or(like(exspend.description, `%${q}%`), like(exspend.invoice_no, `%${q}%`))
		),
		with: { exspendType: true },
		orderBy: desc(exspend.datetime_invoice),
		...pagination(url)
	});
	const get_uploads = await db.query.uploads.findMany({
		where: and(
			eq(uploads.related_type, 'exspend'),
			inArray(
				uploads.related_id,
				get_exspends.map((e) => e.id)
			)
		)
	});
	const get_exspend = await db.query.exspend.findFirst({
		where: eq(exspend.id, +exspend_id)
	});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_id, +exspend_id), eq(uploads.related_type, 'exspend'))
	});
	const count = await db.$count(
		exspend,
		and(
			betweenHelper(url, exspend.datetime_invoice),
			isNotNull(exspend.exspend_type_id),
			or(like(exspend.description, `%${q}%`), like(exspend.invoice_no, `%${q}%`))
		)
	);
	const get_currency = await db.query.currency.findFirst({});
	return {
		get_exspends: get_exspends.map((e) => {
			return {
				...e,
				uploads: get_uploads.find((ee) => ee.related_id === e.id)
			};
		}),
		get_currency,
		get_exspend: { ...get_exspend, uploads: get_upload },
		items: count
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_exspend: async ({ request, url }) => {
		const body = await request.formData();
		const {
			amount,
			recorder_id,
			invoice_no,
			datetime_invoice,
			description,
			exspend_id,
			old_image
		} = Object.fromEntries(body) as Record<string, string>;
		const image = body.get('image') as File;
		const validErr = {
			amount: false,
			datetime_invoice: false
		};

		if (!amount) validErr.amount = true;
		if (!datetime_invoice) validErr.datetime_invoice = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		if (exspend_id) {
			await db
				.update(exspend)
				.set({
					amount: +amount,
					paid: +amount,
					invoice_no: invoice_no,
					recorder_id: recorder_id ? +recorder_id : null,
					datetime_invoice: datetime_invoice,
					description: description
				})
				.where(eq(exspend.id, +exspend_id))
				.catch((e) => {
					logError({ url, body, err: e });
				});

			if (image.size) {
				if (old_image) {
					await fileHandle.update(image, old_image, +exspend_id, 'exspend');
				}
				if (!old_image) {
					await fileHandle.insert(image, +exspend_id, 'exspend');
				}
			}
		}
		if (!exspend_id) {
			const id: { id: number }[] = await db
				.insert(exspend)
				.values({
					amount: +amount,
					paid: +amount,
					invoice_no: invoice_no,
					recorder_id: recorder_id ? +recorder_id : null,
					datetime_invoice: datetime_invoice,
					description: description
				})
				.$returningId()
				.catch((e) => {
					logError({ url, body, err: e });
					return [];
				});

			if (image.size) {
				await fileHandle.insert(image, id[0].id, 'exspend');
			}
		}
	},
	delete_exspend: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		if (isNaN(+id)) return fail(400, { id: true });
		const get_upload = await db.query.uploads.findFirst({
			where: and(eq(uploads.related_type, 'exspend'), eq(uploads.related_id, +id))
		});
		if (get_upload?.filename) await fileHandle.drop(get_upload?.filename);
		await db
			.delete(exspend)
			.where(eq(exspend.id, +id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
