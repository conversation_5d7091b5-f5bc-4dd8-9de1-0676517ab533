<script lang="ts">
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData, ActionData } from './$types';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import CurrencyInput from '$lib/coms-form/CurrencyInput.svelte';
	import CreateAWord from '$lib/coms-cu/CreateAWord.svelte';
	import { YYYYMMDD_Format } from '$lib/helper';
	import Form from '$lib/coms-form/Form.svelte';
	import CropImage from '$lib/coms-form/CropImage.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	interface Props {
		form: ActionData;
		data: PageServerData;
	}
	let { data, form }: Props = $props();
	let { get_currency, get_exspend, get_staffs, get_exspend_types } = $derived(data);
	let exspend_id: number | null = $state(null);
	let recorder_id: number | null = $state(null);
	let exspend_type_id: number | null = $state(null);
	let loading = $state(false);
	let datetime_invoice = $state(
		data.get_exspend?.datetime_invoice
			? YYYYMMDD_Format.datetime(data.get_exspend.datetime_invoice)
			: ''
	);
	$effect(() => {
		if (get_exspend?.recorder_id) {
			recorder_id = get_exspend?.recorder_id;
		} else {
			recorder_id = null;
		}
		if (get_exspend?.exspend_type_id) {
			exspend_type_id = get_exspend?.exspend_type_id;
		} else {
			exspend_type_id = null;
		}
	});
</script>

<DeleteModal action="?/delete_exspend" id={+exspend_id!} />
<!-- @_Add_ExspendTypeModal -->
<CreateAWord
	actionCreate="?/create_expsend_type"
	actionDelete="?/delete_expsend_type"
	modal="create_exspend"
	title={locale.T('exspend_type')}
	data={get_exspend_types.map((e) => ({ id: e.id, word: e.type }))}
/>

<div class="row">
	<div class="col-sm-6">
		<a href="/product/exspend" class="btn btn-link p-0"
			><i class="fa-solid fa-rotate-left"></i>
			{locale.T('back')}
		</a>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('products')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product/exspend" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('exspend')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product/exspend/create" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('add')}
				</a>
			</li>
		</ol>
	</div>
</div>
<Form
	action="?/create_exspend"
	method="post"
	enctype="multipart/form-data"
	bind:loading
	fnSuccess={() => document.getElementById('close_exspend')?.click()}
>
	<div class="card bg-light">
		<div class="card-body">
			<input type="hidden" name="exspend_id" value={get_exspend?.id ?? ''} />
			<div class="row pb-2">
				<div class="col-6">
					<label for="datetime_invoice">{locale.T('dates')}</label>
					<input
						value={datetime_invoice}
						class="form-control"
						type="datetime-local"
						name="datetime_invoice"
						id="datetime_invoice"
					/>
					{#if form?.datetime_invoice}
						<div class="text-danger">{locale.T('input_data')}</div>
					{/if}
				</div>
				<div class="col-6">
					<label for="invoice_no">{locale.T('invoice_no')}</label>
					<input
						value={get_exspend?.invoice_no ?? ''}
						class="form-control"
						step="any"
						type="number"
						name="invoice_no"
						id="invoice_no"
					/>
				</div>
			</div>
			<div class="row pb-2">
				<div class="col-6">
					<label for="amount">{locale.T('amount')}</label>
					<CurrencyInput
						name="amount"
						amount={get_exspend?.amount}
						symbol={get_currency?.currency}
					/>
					{#if form?.amount}
						<div class="text-danger">{locale.T('input_data')}</div>
					{/if}
				</div>
				<div class="col-6">
					<label for="recorder_id">{locale.T('recorder')}</label>
					<SelectParam
						bind:value={recorder_id}
						items={get_staffs.map((e) => ({ name: e.name_latin, id: e.id }))}
						name="recorder_id"
					/>
				</div>
			</div>
			<div class="row pb-2">
				<div class="col-4">
					<label for="exspend_type_id">{locale.T('exspend_type')}</label>
					<SelectParam
						bind:value={exspend_type_id}
						items={get_exspend_types.map((e) => ({ name: e.type, id: e.id }))}
						name="exspend_type_id"
					/>
					{#if form?.exspend_type_id}
						<div class="text-danger">{locale.T('input_data')}</div>
					{/if}
				</div>
				<div class="col-auto px-0 mx-0">
					<label for="add"></label>
					<button
						aria-label="add_exspend_type"
						data-bs-toggle="modal"
						data-bs-target="#create_exspend"
						id="add"
						type="button"
						class="form-control bg-primary text-light border-0 shadow-none mx-0"
						><i class="fa-regular fa-bookmark"></i></button
					>
				</div>
				<div class="col-6">
					<label for="description">{locale.T('description')}</label>
					<input
						type="text"
						value={get_exspend?.description ?? ''}
						class="form-control"
						name="description"
						id="description"
					/>
				</div>
			</div>
			<div class="col-12">
				<label for="image">{locale.T('references')}</label>
				<CropImage
					aspect_ratio
					label={locale.T('references')}
					name="file"
					default_image={get_exspend?.uploads?.filename}
					related_id={get_exspend?.id}
					related_type_="exspend"
				/>
			</div>
		</div>
		<div class="card-footer text-end">
			<SubmitButton {loading} />
		</div>
	</div>
</Form>
