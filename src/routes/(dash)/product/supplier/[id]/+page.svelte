<script lang="ts">
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { EventHandler } from 'svelte/elements';
	import type { PageServerData } from './$types';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import { YYYYMMDD_Format } from '$lib/helper';
	import CurrencyInput from '$lib/coms-form/CurrencyInput.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	interface Props {
		data: PageServerData;
	}
	let timeout: number | NodeJS.Timeout | null = $state(null);
	const handleQ: EventHandler<Event, HTMLInputElement> = ({ currentTarget }) => {
		clearTimeout(timeout!);
		const form = currentTarget?.form;
		if (!form) return;
		timeout = setTimeout(() => {
			form.requestSubmit();
		}, 400);
	};
	let { data }: Props = $props();
	let { get_invoices, get_currency, get_supplier } = $derived(data);
	let exspend_id = $state(0);
	let find_exspend = $derived(get_invoices?.find((e) => e.id === exspend_id));
	let loading = $state(false);
	let datetime: string = $state('');
	let get_inventories = $derived(get_supplier?.inventory);
	$effect(() => {
		if (find_exspend?.datetime_invoice) {
			datetime = YYYYMMDD_Format.date(find_exspend?.datetime_invoice);
		} else {
			datetime = '';
		}
	});
	function pushSupplierId(id: number) {
		exspend_id = 0;
		exspend_id = id;
	}
</script>

<DeleteModal action="?/delete_invoice_supplier" id={find_exspend?.id} />

<!-- @_Add_Ivoice -->
<div class="modal fade" id="create_invoice_supplier" data-bs-backdrop="static">
	<div class="modal-dialog modal-xl">
		<Form
			action="?/create_invoice_supplier"
			method="post"
			class="modal-content"
			bind:loading
			fnSuccess={() => document.getElementById('close_invoice_supplier')?.click()}
		>
			<input type="hidden" name="exspend_id" value={find_exspend?.id ?? ''} />
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('invoice')}</h4>
				<button
					id="close_invoice_supplier"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div class="card-body pt-0">
					<div class="row">
						<div class="col">
							<label for="datetime_invoice">{locale.T('date')}</label>
							<input
								class="form-control"
								value={datetime}
								type="date"
								name="datetime_invoice"
								id="datetime_invoice"
							/>
						</div>
						<div class="col">
							<label for="invoice_no">{locale.T('invoice_no')}</label>
							<input
								class="form-control"
								type="text"
								name="invoice_no"
								value={find_exspend?.invoice_no ?? ''}
								id="invoice_no"
							/>
						</div>
					</div>
					<div class="row pt-3 pb-3">
						<div class="col">
							<label for="amount">{locale.T('amount')}</label>
							<CurrencyInput
								name="amount"
								amount={find_exspend?.amount}
								symbol={get_currency?.currency}
							/>
						</div>
						<div class="col">
							<label for="paid">{locale.T('paid')}</label>
							<CurrencyInput
								name="paid"
								amount={find_exspend?.paid}
								symbol={get_currency?.currency}
							/>
						</div>
					</div>
					{#each get_inventories || [] as item (item.id)}
						<div class="alert alert-secondary">
							<div class="row">
								<div class="col">
									<div class="form-control">
										<div class="form-check m-0">
											<input
												checked
												name="is_count_stock"
												class="form-check-input"
												type="checkbox"
												id={String(item?.id)}
											/>
											<label for={String(item?.id)}>{item?.product?.products ?? ''}</label>
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-6">
									<label for="original_price">{locale.T('original_price')}</label>
									<CurrencyInput
										name="original_price"
										amount={find_exspend?.amount}
										symbol={get_currency?.currency}
									/>
								</div>
								<div class="col-6">
									<label for="original_price">{locale.T('unit')}</label>
									<select class="form-control" name="" id="">
										{#each item?.product.subUnit || [] as iitem}
											<option value="">
												{iitem.unit?.unit ?? ''}
											</option>
										{/each}
										<option value="">
											{item.product.unit?.unit ?? ''}
										</option>
									</select>
								</div>
							</div>
						</div>
					{/each}
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('invoice')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('products')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product/supplier" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-people-carry-box"></i>
					{locale.T('supplier')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<button class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-receipt"></i>
					{locale.T('invoice')}
				</button>
			</li>
		</ol>
	</div>
</div>
<div class="row">
	<div class="col-12">
		<div class="card">
			<div class="card-header">
				<form
					data-sveltekit-keepfocus
					onchange={({ currentTarget }) => currentTarget.requestSubmit()}
					class="row gap-1"
				>
					<div class="col-sm-3">
						<input
							oninput={handleQ}
							type="search"
							name="q"
							class="form-control float-right"
							placeholder={locale.T('search')}
						/>
					</div>
					<div class="col text-end">
						<button
							type="button"
							class="btn btn-success"
							data-bs-toggle="modal"
							data-bs-target="#create_invoice_supplier"
							><i class="fa-solid fa-square-plus"></i>
							{locale.T('new_invoice')}
						</button>
					</div>
				</form>
			</div>

			<div style="max-height: {store.inerHight};" class="card-body table-responsive p-0 m-0">
				<table class="table table-hover table-bordered">
					<thead class="sticky-top top-0 bg-light table-active">
						<tr class="text-center">
							<th class="text-center">{locale.T('n')}</th>
							<th>{locale.T('date')}</th>
							<th>{locale.T('amount')}</th>
							<th>{locale.T('paid')}</th>
							<th>{locale.T('credit')}</th>
							<th>{locale.T('invoice_no')}</th>
							<th>{locale.T('action')}</th>
						</tr>
					</thead>
					<tbody>
						{#each get_invoices as item, index}
							<tr class="text-center">
								<td>
									{index + 1}
								</td>
								<td>
									<DDMMYYYYFormat date={item?.datetime_invoice} style="date" />
								</td>

								<td>
									<Currency amount={item?.amount} symbol={get_currency?.currency} />
								</td>
								<td>
									<Currency amount={item?.paid} symbol={get_currency?.currency} />
								</td>

								<td>
									<Currency amount={item?.credit} symbol={get_currency?.currency} />
								</td>
								<td>
									{item?.invoice_no}
								</td>

								<td>
									<div class=" m-0 p-0 text-start">
										<button
											onclick={() => pushSupplierId(item.id)}
											aria-label="createproduct"
											type="button"
											class="btn btn-primary btn-sm"
											data-bs-toggle="modal"
											data-bs-target="#create_invoice_supplier"
											><i class="fa-solid fa-file-pen"></i>
										</button>
										<button
											onclick={() => pushSupplierId(item.id)}
											aria-label="deletemodal"
											type="button"
											class="btn btn-danger btn-sm"
											data-bs-toggle="modal"
											data-bs-target="#delete_modal"
											><i class="fa-solid fa-trash-can"></i>
										</button>
										{#if item?.credit}
											<button
												onclick={() => pushSupplierId(item.id)}
												aria-label="deletemodal"
												type="button"
												class="btn btn-success btn-sm"
												data-bs-toggle="modal"
												data-bs-target="#delete_modal"
												>ទូទាត់
											</button>
										{/if}
										<a
											href="?supplier_id={item?.id}"
											aria-label="deletemodal"
											type="button"
											class="btn btn-secondary btn-sm"
											>បញ្ជូលទំនិញ
										</a>
									</div>
								</td>
							</tr>
						{/each}
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>
