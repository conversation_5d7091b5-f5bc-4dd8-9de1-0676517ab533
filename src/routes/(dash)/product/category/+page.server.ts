import { db } from '$lib/server/db';
import { category, group, unit, unitsToGroups } from '$lib/server/schemas';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { and, asc, eq } from 'drizzle-orm';
import logError from '$lib/server/utils/logError';

export const load = (async () => {
	const get_categories = await db.query.category.findMany({
		orderBy: asc(category.id),
		with: {
			group: {
				orderBy: asc(group.id),
				with: {
					unitsToGroups: {
						with: {
							unit: true
						}
					}
				}
			}
		}
	});
	const get_units = await db.query.unit.findMany({});
	return {
		get_categories,
		get_units
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_category: async ({ request, url }) => {
		const body = await request.formData();
		const { category_ } = Object.fromEntries(body) as Record<string, string>;
		if (!category_.trim()) return fail(400, { category_: true });
		await db
			.insert(category)
			.values({
				name: category_
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_category: async ({ request, url }) => {
		const body = await request.formData();
		const { category_, category_id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.update(category)
			.set({
				name: category_
			})
			.where(eq(category.id, Number(category_id)))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	delete_category: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.delete(category)
			.where(eq(category.id, Number(id)))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	create_group: async ({ request, url }) => {
		const body = await request.formData();
		const { group_, category_id } = Object.fromEntries(body) as Record<string, string>;
		if (!group_.trim()) return fail(400, { group_: true });
		await db
			.insert(group)
			.values({
				name: group_,
				category_id: +category_id
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_group: async ({ request, url }) => {
		const body = await request.formData();
		const { group_, id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.update(group)
			.set({
				name: group_
			})
			.where(eq(group.id, Number(id)))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	delete_group: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.delete(group)
			.where(eq(group.id, Number(id)))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	create_unit: async ({ request, url }) => {
		const body = await request.formData();
		const { unit_ } = Object.fromEntries(body) as Record<string, string>;
		if (!unit_.trim()) return fail(400, { unit_: true });
		await db
			.insert(unit)
			.values({
				unit: unit_
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_unit: async ({ request, url }) => {
		const body = await request.formData();
		const { unit_, unit_id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.update(unit)
			.set({
				unit: unit_
			})
			.where(eq(unit.id, Number(unit_id)))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	delete_unit: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.delete(unit)
			.where(eq(unit.id, Number(id)))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	add_unit_to_group: async ({ request }) => {
		const body = await request.formData();
		const { group_id } = Object.fromEntries(body) as Record<string, string>;
		const unit_id = body.getAll('unit_id') as string[];
		const get_group = await db.query.group.findFirst({
			where: eq(group.id, +group_id),
			with: {
				unitsToGroups: true
			}
		});
		for (const e of unit_id || []) {
			if (!get_group?.unitsToGroups.some((ee) => ee.unit_id === +e)) {
				await db.insert(unitsToGroups).values({
					group_id: +group_id,
					unit_id: +e
				});
			}
		}
		for (const e of get_group?.unitsToGroups || []) {
			if (!unit_id.some((ee) => +ee === e.unit_id)) {
				if (e.unit_id && e.group_id) {
					await db
						.delete(unitsToGroups)
						.where(
							and(eq(unitsToGroups.unit_id, e.unit_id), eq(unitsToGroups.group_id, e.group_id))
						);
				}
			}
		}
	}
};
