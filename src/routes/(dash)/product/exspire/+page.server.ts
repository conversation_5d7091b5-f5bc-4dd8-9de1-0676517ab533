import { db } from '$lib/server/db';
import { uploads, group, inventory } from '$lib/server/schemas';
import type { PageServerLoad } from './$types';
import { and, asc, between, eq, gte, inArray, isNotNull, lte, or } from 'drizzle-orm';
import { pagination } from '$lib/server/utils';
import { addDays, YYYYMMDD_Format } from '$lib/server/utils';
import { fail, type Actions } from '@sveltejs/kit';
import logError from '$lib/server/utils/logError';
export const load = (async ({ url, parent }) => {
	await parent();
	const get_setting = await db.query.setting.findFirst({});
	const exspire_at = isNaN(Number(url.searchParams.get('exspire_at')))
		? ''
		: url.searchParams.get('exspire_at') || '';
	const symbol = url.searchParams.get('symbol') || '';
	const status = url.searchParams.get('status') || '';
	const get_groups = await db.query.group.findMany({
		where: or(eq(group.category_id, 1), eq(group.category_id, 3)),
		with: { unitsToGroups: { with: { unit: true } } }
	});
	const date = new Date();
	const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
	const days = +exspire_at * lastDay;
	const exspire = YYYYMMDD_Format.datetime(addDays(new Date(), days));
	const near_exspire = YYYYMMDD_Format.datetime(
		addDays(new Date(), get_setting?.warring_expire_day ?? 0)
	);
	const group_id = url.searchParams.get('group_id') || '';
	const get_inventory_expires = await db.query.inventory.findMany({
		where: and(
			exspire_at
				? symbol === '<'
					? lte(inventory.datetime_expire, exspire)
					: symbol === '>'
						? gte(inventory.datetime_expire, exspire)
						: between(inventory.datetime_expire, YYYYMMDD_Format.datetime(date), exspire)
				: undefined,
			isNotNull(inventory.datetime_expire),
			+group_id ? eq(inventory.group_id, +group_id) : undefined,
			status === 'near'
				? between(inventory.datetime_expire, YYYYMMDD_Format.datetime(date), near_exspire)
				: status === 'expired'
					? lte(inventory.datetime_expire, YYYYMMDD_Format.datetime(date))
					: status === 'not_expired'
						? gte(inventory.datetime_expire, exspire)
						: undefined
		),
		with: {
			product: {
				with: {
					group: true,
					unit: true
				}
			},
			exspend: {
				with: { supplier: true }
			}
		},
		orderBy: asc(inventory.datetime_expire),
		...pagination(url)
	});
	const product_ids = get_inventory_expires.map((e) => e.product_id);
	const get_uploads = await db.query.uploads.findMany({
		where: and(eq(uploads.related_type, 'product'), inArray(uploads.related_id, product_ids))
	});
	const product_expires = get_inventory_expires.map((e) => {
		return {
			...e,
			product: {
				...e.product,
				uploads: get_uploads.find((ee) => ee.related_id === e.product_id)
			}
		};
	});
	const count = await db.$count(
		inventory,
		and(gte(inventory.datetime_expire, exspire), isNotNull(inventory.datetime_expire))
	);
	return {
		get_groups,
		get_setting,
		items: count,
		get_product_expires: product_expires
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	catch_out_expire: async ({ request, url }) => {
		const body = await request.formData();
		const { inventory_id, qty_expire } = Object.fromEntries(body) as Record<string, string>;
		if (isNaN(+inventory_id)) return fail(400, { inventory_id: true });
		if (isNaN(+qty_expire)) return fail(400, { qty_expire: true });
		await db
			.update(inventory)
			.set({
				qty_expire: +qty_expire
			})
			.where(eq(inventory.id, +inventory_id))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
