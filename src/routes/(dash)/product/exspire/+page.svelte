<script lang="ts">
	import type { PageServerData } from './$types';
	import { store } from '$lib/store/store.svelte';
	import Paginations from '$lib/coms/Paginations.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import HeaderQuery from '$lib/coms-form/HeaderQuery.svelte';
	import HandleQ from '$lib/coms-form/HandleQ.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import SelectParam from '$lib/coms-form/SelectParam.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import SetBack from '$lib/coms/SetBack.svelte';
	import { addDays } from '$lib/helper';
	import { page } from '$app/state';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let { items, get_groups, get_product_expires, get_setting } = $derived(data);
	let n: number = $state(1);
	let category_id: number | null = $state(null);
	let groups_f = $derived(
		category_id ? get_groups.filter((e) => e.category_id === category_id) : get_groups
	);
	let inventory_id: number | null = $state(null);
	let find_exspire = $derived(get_product_expires?.find((e) => e.id === inventory_id));
	let loading = $state(false);
	let qty_expire = $derived(find_exspire?.qty_expire ?? 0);
	let status = $derived(page.url.searchParams.get('status') || '');
</script>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('infomations_medicine_expire')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/product" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('products')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/medicine/exspire" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-briefcase-medical"></i>
					{locale.T('exspire_list')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<HeaderQuery>
			<div class="col-sm-3">
				<SelectParam
					name="group_id"
					placeholder={locale.T('product_group')}
					items={groups_f.map((e) => ({ id: e.id, name: e.name }))}
				/>
			</div>

			<div class="col-sm-4">
				<fieldset disabled={status ? true : false}>
					<div class="input-group">
						<label for="" class="input-group-text">{locale.T('duration_of_expire')}</label>
						<select name="symbol" class="form-control" id="">
							<option value="<">{locale.T('less_than')}</option>
							<option value="|">{locale.T('between')}</option>
							<option value=">">{locale.T('more_than')}</option>
						</select>
						<HandleQ q_name="exspire_at" />
						<label for="" class="input-group-text">{locale.T('month')}</label>
					</div>
				</fieldset>
			</div>
			<div class="col-sm-auto">
				<a
					class:active={status === 'near'}
					href={status === 'near' ? '?' : '?status=near'}
					class="btn btn-outline-warning">{locale.T('near_expired')}</a
				>
				<a
					class:active={status === 'expired'}
					href={status === 'expired' ? '?' : '?status=expired'}
					class="btn btn-outline-danger">{locale.T('expired')}</a
				>
				<a
					class:active={status === 'not_expired'}
					href={status === 'not_expired' ? '?' : '?status=not_expired'}
					class="btn btn-outline-primary">{locale.T('not_expired')}</a
				>
			</div>
		</HeaderQuery>
	</div>

	<div style="height: {store.inerHight};" class="card-body table-responsive p-0 m-0">
		<table class="table table-bordered table-hover text-nowrap table-light">
			<thead class="sticky-top top-0 bg-light table-active">
				<tr class="text-center">
					<th style="width: 3%;" class="text-center">{locale.T('n')}</th>
					<th>{locale.T('products')}</th>
					<th style="min-width: 150px;">{locale.T('date_bought')}</th>
					<th style="min-width: 150px;">{locale.T('expires_date')}</th>
					<th style="width: 150px;">{locale.T('qty_bought')}</th>
					<th>{locale.T('qty_expire')}</th>
					<th>{locale.T('available')}</th>
					<th>{locale.T('supplier')}</th>
					<th>{locale.T('action')}</th>
				</tr>
			</thead>
			<tbody>
				{#each get_product_expires as item, index}
					{@const is_expire = new Date(String(item.datetime_expire)) <= new Date()}
					{@const is_expire_at_day =
						new Date(String(item.datetime_expire)) <=
						addDays(new Date(), get_setting?.warring_expire_day ?? 0)}
					{@const product = item.product}
					<tr>
						<td class="text-center">{n + index}</td>
						<td class="text-start">
							<div class="row g-2">
								<div style="width: 60px;" class="col-auto">
									<img
										src={product.uploads?.filename
											? `${product.uploads?.filename}`
											: '/no-image.jpg'}
										alt=""
										style="height:50px"
									/>
								</div>
								<div class="col-auto">
									<SetBack href="/product/{product.id}">{product.products}</SetBack>

									<br />
									<span class="badge bg-primary">
										{product.generic_name ?? ''}
									</span>
								</div>
							</div>
						</td>

						<td>
							<div class="text-center">
								<i class="fa-solid fa-calendar-days"></i>
								<DDMMYYYYFormat date={item?.datetime_buy} style="date" />
							</div>
						</td>
						<td>
							<div
								class={is_expire
									? 'text-danger text-center'
									: is_expire_at_day
										? 'text-warning text-center'
										: 'text-primary text-center'}
							>
								<i class="fa-regular fa-calendar-minus"></i>
								<DDMMYYYYFormat date={item.datetime_expire} style="date" />
							</div>
						</td>
						<td class="text-center">
							{item.qty_adjustment}
							{item.product.unit?.unit}
						</td>
						<td class="text-center">
							{#if item.qty_expire}
								{item.qty_expire}
								{item.product.unit?.unit}
							{:else}
								{locale.T('none')}
							{/if}
						</td>
						<td class="text-center">
							{item.qty_adjustment - item.qty_expire}
							{item.product.unit?.unit}
						</td>
						<td class="text-start">
							<span>
								{item?.exspend?.supplier?.name ?? ''}
							</span>
							<span>
								,{item?.exspend?.supplier?.address ?? ''}
							</span>
							<span>
								,{item?.exspend?.supplier?.contact ?? ''}
							</span>
						</td>
						<td class="text-center">
							<button
								onclick={() => (inventory_id = item.id)}
								type="button"
								data-bs-toggle="modal"
								data-bs-target="#modal_expire"
								class="btn btn-sm btn-success"
								><i class="fa-solid fa-calendar-minus"></i> {locale.T('catch_out')}</button
							>
							<SetBack
								href="/product/purchase/create?exspend_id={item?.exspend?.id}"
								class="btn btn-sm btn-primary"
							>
								<i class="fa-solid fa-receipt"></i>
								{locale.T('invoice')}
							</SetBack>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
	<div class="card-footer">
		<Paginations bind:n {items} />
	</div>
</div>
<!-- Modal -->
<div
	class="modal fade"
	id="modal_expire"
	data-bs-backdrop="static"
	data-bs-keyboard="false"
	tabindex="-1"
	aria-labelledby="modal_expire"
	aria-hidden="true"
>
	<div class="modal-dialog modal-lg">
		<Form
			action="?/catch_out_expire"
			method="post"
			bind:loading
			fnSuccess={() => {
				document.getElementById('close_expire')?.click();
			}}
			class="modal-content"
		>
			<div class="modal-header">
				<h1 class="modal-title fs-5" id="modal_expire">{locale.T('catch_out_medicine_expire')}</h1>
				<button
					onclick={() => {
						setTimeout(() => {
							inventory_id = null;
						}, 300);
					}}
					id="close_expire"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				></button>
			</div>
			<div class="modal-body">
				{#if find_exspire}
					<div class="alert alert-warning">
						<i class="fa-solid fa-circle-info"></i>
						{locale.T('pleas_valiidate_stock_before_catch_out')}
					</div>
					<div class="card bg-light">
						<div class="card-body">
							<div class="row">
								<div class="col-sm-12 pb-3">
									<label for="qty">{locale.T('product_name')}</label>
									<span class="form-control">
										{find_exspire?.product?.products}
									</span>
								</div>
								<div class="col-sm-12 pb-3">
									<label for="qty">{locale.T('qty_bought')}</label>
									<span class="form-control">
										{find_exspire?.qty_adjustment}
										{find_exspire?.product?.unit?.unit ?? ''}
									</span>
								</div>
								<div class="col-sm-12 pb-3">
									<label for="qty_expire">{locale.T('qty_expire')} </label>
									<div class="input-group">
										<input
											bind:value={qty_expire}
											name="qty_expire"
											type="number"
											class="form-control"
											id="qty_expire"
										/>
										<input type="hidden" name="inventory_id" value={find_exspire?.id} />
										<label for="qty_expire" class="input-group-text">
											{find_exspire.product?.unit?.unit ?? ''}
										</label>
									</div>
								</div>
							</div>
						</div>
					</div>
				{/if}
			</div>
			<div class="modal-footer">
				<button
					disabled={qty_expire > Number(find_exspire?.qty_adjustment)}
					type="submit"
					class="btn btn-primary">{locale.T('save')}</button
				>
			</div>
		</Form>
	</div>
</div>
