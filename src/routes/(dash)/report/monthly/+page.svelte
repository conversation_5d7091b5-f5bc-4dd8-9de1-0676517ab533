<script lang="ts">
	import { page } from '$app/state';
	import Currency from '$lib/coms/Currency.svelte';
	import { YYYYMMDD_Format } from '$lib/helper';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	let { data }: { data: PageServerData } = $props();
	let { get_currency, year, months, get_billings, get_exspends, get_payrolls } = $derived(data);
	let now_year = $derived(page.url.searchParams.get('year') || year);
	let total_paid = $derived(get_billings?.reduce((acc, item) => acc + Number(item.paid), 0));
	let total_expense = $derived(get_exspends?.reduce((acc, item) => acc + Number(item.amount), 0));
	let total_salary = $derived(get_payrolls?.reduce((acc, item) => acc + Number(item.amount), 0));
	let total = $derived(get_billings?.reduce((acc, item) => acc + Number(item.total), 0));
	let total_credit = $derived(total - total_paid);
</script>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('monthly_report')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/report/daily" class="btn btn-link p-0 text-secondary">
					<i class="fa-solid fa-receipt"></i>
					{locale.T('report')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/report/daily" class="btn btn-link p-0 text-secondary">
					<i class="fa-solid fa-calendar-days"></i>
					{locale.T('monthly')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="pb-3 row g-2">
	<div class="col-lg col-md-6 col-sm-12">
		<div class="card text-bg-light text-primary-emphasis">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 25px;">{locale.T('total_income')}</div>
					<div style="font-size: 25px;">
						<Currency class="" amount={total} symbol={get_currency?.currency} />
						<br />
						<Currency class="" amount={total} symbol={get_currency?.exchang_to} {get_currency} />
					</div>
				</div>
				<div class="col-auto">
					<i class="fa-solid fa-wallet fa-4x"></i>
				</div>
			</div>
		</div>
	</div>
	<div class="col-lg col-md-6 col-sm-12">
		<div class="card text-bg-light text-info-emphasis">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 25px;">{locale.T('total_expense')}</div>
					<div style="font-size: 25px;">
						<Currency class="" amount={total_expense} symbol={get_currency?.currency} /> <br />
						<Currency
							class=""
							amount={total_expense}
							symbol={get_currency?.currency}
							{get_currency}
						/>
					</div>
				</div>
				<div class="col-auto">
					<i class="fa-solid fa-sack-dollar fa-4x"></i>
				</div>
			</div>
		</div>
	</div>
	<div class="col-lg col-md-6 col-sm-12">
		<div class="card text-bg-light text-warning-emphasis">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 25px;">{locale.T('total_salary')}</div>
					<div style="font-size: 25px;">
						<Currency class="" amount={total_salary} symbol={get_currency?.currency} />
						<br />
						<Currency
							class=""
							amount={total_salary}
							symbol={get_currency?.currency}
							{get_currency}
						/>
					</div>
				</div>
				<div class="col-auto">
					<i class="fa-solid fa-money-check-dollar fa-4x"></i>
				</div>
			</div>
		</div>
	</div>
	<div class="col-lg col-md-6 col-sm-12">
		<div class="card text-bg-light text-danger-emphasis">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 25px;">{locale.T('credit')}</div>
					<div style="font-size: 25px;">
						<Currency class="" amount={total_credit} symbol={get_currency?.currency} />
						<br />
						<Currency
							class=""
							amount={total_credit}
							symbol={get_currency?.currency}
							{get_currency}
						/>
					</div>
				</div>
				<div class="col-auto">
					<i class="fa-solid fa-money-bill-trend-up fa-4x"></i>
				</div>
			</div>
		</div>
	</div>
	<div class="col-lg col-md-6 col-sm-12">
		<div class="card text-bg-light text-secondary-emphasis">
			<div class="card-body row g-0 justify-content-between">
				<div class="col-auto">
					<div style="font-size: 25px;">{locale.T('profit')}</div>
					<div style="font-size: 25px;">
						<Currency
							class=""
							amount={total - (total_salary + total_expense)}
							symbol={get_currency?.currency}
						/>
						<br />
						<Currency
							class=""
							amount={total - (total_salary + total_expense)}
							symbol={get_currency?.currency}
							{get_currency}
						/>
					</div>
				</div>
				<div class="col-auto">
					<i class="fa-regular fa-credit-card fa-4x"></i>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="card-body table-responsive overflow-auto">
	<table class="table table-bordered text-nowrap table-light">
		<thead class="sticky-top top-0 bg-light table-active">
			<tr class="text-center">
				<th>
					<a
						aria-label="Previous"
						class="text-decoration-none"
						href="/report/monthly?year={+now_year - 1}"
					>
						<i class="fa-solid fa-circle-chevron-left fs-4"></i>
					</a>
				</th>
				<th colspan="10">
					{locale.T('year')}
					{page.url.searchParams.get('year') || now_year}
				</th>
				<th>
					<a
						aria-label="Next"
						class="text-decoration-none"
						href="/report/monthly?year={+now_year + 1}"
					>
						<i class="fa-solid fa-circle-chevron-right fs-4"></i>
					</a>
				</th>
			</tr>
			<tr class="text-center">
				{#each months as month (month.num)}
					<th>{locale.T(month.name as 'none')}</th>
				{/each}
			</tr>
		</thead>
		<tbody>
			<tr class="text-center h">
				{#each months as month (month.num)}
					{@const exspend_daily = get_exspends?.filter(
						(item) => YYYYMMDD_Format.month(item.datetime_invoice) === month.num
					)}
					{@const payroll_daily = get_payrolls?.filter(
						(item) => YYYYMMDD_Format.month(item.payment_date) === month.num
					)}
					{@const total_expense_daily =
						exspend_daily?.reduce((acc, item) => acc + Number(item.amount), 0) +
						payroll_daily?.reduce((acc, item) => acc + Number(item.amount), 0)}
					{@const billing_daily = get_billings?.filter(
						(item) => YYYYMMDD_Format.month(item.created_at) === month.num
					)}
					{@const discount_as_value =
						billing_daily?.reduce((acc, item) => acc + Number(item.amount), 0) -
						billing_daily?.reduce((acc, item) => acc + Number(item.total), 0)}
					{@const tax_as_value =
						billing_daily?.reduce((acc, item) => acc + Number(item.total), 0) -
						billing_daily?.reduce((acc, item) => acc + Number(item.total_after_tax), 0)}
					{@const total_paid = billing_daily?.reduce((acc, item) => acc + Number(item.paid), 0)}
					{@const total_amount = billing_daily?.reduce((acc, item) => acc + Number(item.amount), 0)}
					{@const total_total = billing_daily?.reduce((acc, item) => acc + Number(item.total), 0)}
					{@const total_after_tax = billing_daily?.reduce(
						(acc, item) => acc + Number(item.total_after_tax),
						0
					)}
					{@const total_balance = total_total - total_paid}
					<td style="width: 250px;height: 250px;" class="table-light p-0">
						<div class="table-responsive">
							{#if billing_daily?.length || total_expense_daily}
								<table class="table table-bordered table-sm table-secondary table-striped m-0">
									<tbody>
										<tr>
											<td>
												{locale.T('invoice')}
											</td>
										</tr>
										<tr>
											<td>
												{billing_daily?.length}
											</td>
										</tr>
										<tr>
											<td>
												{locale.T('total')}
											</td>
										</tr>
										<tr>
											<td>
												<Currency class="" amount={total_amount} symbol={get_currency?.currency} />
											</td>
										</tr>
										<tr>
											<td>
												{locale.T('discount')}
											</td>
										</tr>
										<tr>
											<td>
												<Currency
													class=""
													amount={discount_as_value}
													symbol={get_currency?.currency}
												/>
											</td>
										</tr>
										<tr>
											<td>
												{locale.T('total_after_disc')}
											</td>
										</tr>
										<tr>
											<td>
												<Currency class="" amount={total_total} symbol={get_currency?.currency} />
											</td>
										</tr>
										<tr>
											<td>
												{locale.T('tax')}
											</td>
										</tr>
										<tr>
											<td>
												<Currency class="" amount={tax_as_value} symbol={get_currency?.currency} />
											</td>
										</tr>

										<tr>
											<td>
												{locale.T('grand_total')}
											</td>
										</tr>
										<tr>
											<td>
												<Currency
													class=""
													amount={total_after_tax}
													symbol={get_currency?.currency}
												/>
											</td>
										</tr>
										<tr>
											<td>
												{locale.T('credit')}
											</td>
										</tr>
										<tr>
											<td>
												<Currency class="" amount={total_balance} symbol={get_currency?.currency} />
											</td>
										</tr>
										<tr>
											<td>
												{locale.T('exspend')}
											</td>
										</tr>
										<tr>
											<td>
												<Currency
													class=""
													amount={total_expense_daily}
													symbol={get_currency?.currency}
												/>
											</td>
										</tr>
										<tr>
											<td>
												{locale.T('profit')}
											</td>
										</tr>
										<tr>
											<td>
												<Currency
													class=""
													amount={total_total - total_expense_daily}
													symbol={get_currency?.currency}
												/>
											</td>
										</tr>
									</tbody>
								</table>
							{/if}
						</div>
					</td>
				{/each}
			</tr>
		</tbody>
	</table>
</div>
