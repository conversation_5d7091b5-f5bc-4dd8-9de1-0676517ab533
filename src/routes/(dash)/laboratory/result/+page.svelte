<script lang="ts">
	import type { PageServerData } from './$types';
	import { locale } from '$lib/translations/locales.svelte';
	import Athtml from '$lib/coms/Athtml.svelte';
	import TextEditor from '$lib/coms-cu/TextEditor.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import WordsOne from '$lib/coms-cu/WordsOne.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import PatientInfo from '$lib/coms-ipd-opd/PatientInfo.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let { get_visit, get_words, patient_info } = $derived(data);
	let labo_req = $derived(get_visit?.laboratoryRequest);
	let loading = $state(false);
	let laboratory_id: number | null = $state(null);
	let filename = $state('');
	let parameter = $state('');
	let filltered_words = $derived(get_words.filter((e) => e.type === parameter));
</script>

<DeleteModal action="?/delete_picture" id={laboratory_id!}>
	<input type="hidden" name="laboratory_id" value={laboratory_id} />
	<input type="hidden" name="file_name" value={filename ?? ''} />
</DeleteModal>
<div class="row">
	<div class="col-sm-6">
		<a href="/laboratory" class="btn btn-link p-0"
			><i class="fa-solid fa-rotate-left"></i>
			{locale.T('back')}
		</a>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/laboratory" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-flask nav-icon"></i>
					{locale.T('laboratory')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/laboratory/result" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-flask nav-icon"></i>
					{locale.T('result')}
				</a>
			</li>
		</ol>
	</div>
</div>
<PatientInfo {patient_info} />

<WordsOne
	category="laboratory"
	name={parameter}
	modal_name="laboratory_words"
	words={filltered_words}
/>

<Form
	action="?/create_laboratory_result"
	enctype="multipart/form-data"
	method="post"
	data_sveltekit_keepfocus={true}
	reset={false}
	bind:loading
>
	<div class="row">
		<input type="hidden" name="visit_id" value={get_visit?.id} />
		{#each labo_req || [] as item (item.id)}
			{@const patient_gener = get_visit?.patient?.gender}
			{@const parameters = item.product?.parameter.filter((e) => {
				return e.gender === 'Other' || e.gender === patient_gener;
			})}
			<div class="col-12 py-3">
				<div class="card bg-light">
					<div class="card-header fs-5">
						<span>#{item.product?.products ?? ''}</span>
					</div>
					<div class="card-body border-0 table-responsive p-0 m-0">
						<table class="table table-light text-nowrap p-0 m-0 table-bordered table-hover">
							<thead class="table-active">
								<tr>
									<th class="text-center" style="width: 3%;">{locale.T('n')}</th>
									<th style="width: 40%;">{locale.T('parameter')} </th>
									<th style="width: 20%;" class="text-center">{locale.T('result')}</th>
									<th style="width: 15%;" class="text-center">{locale.T('unit')}</th>
									<th style="width: 20%;" class="text-center">{locale.T('range_normal')}</th>
								</tr>
							</thead>
							<tbody>
								{#each parameters || [] as iitem, index (iitem.id)}
									{@const map_result = get_visit?.laboratoryRequest.flatMap(
										(e) => e.laboratoryResult
									)}
									{@const plan_result = map_result?.find((e) => e.parameter_id === iitem.id)}
									<tr>
										<td class="text-center">{index + 1}</td>
										<td class="text-wrap"
											>{iitem.parameter} <br />
											<span class="text-sm text-wrap">
												<Athtml html={iitem?.description ?? ''} />
											</span>
										</td>
										<td>
											<div class="input-group mb-3 input-group-sm">
												<button
													onclick={() => {
														parameter = iitem?.parameter ?? '';
													}}
													data-bs-toggle="modal"
													aria-label="modaladdmedicine"
													data-bs-target="#laboratory_words"
													class="btn btn-primary btn-sm"
													type="button"><i class="fa-regular fa-bookmark"></i></button
												>

												<datalist id={`list${iitem.id.toString()}`}>
													{#each get_words || [] as world_}
														{#if world_.type === iitem.parameter}
															<option value={world_?.text}></option>
														{/if}
													{/each}
												</datalist>
												<input
													value={plan_result?.result}
													autocomplete="off"
													type="text"
													class="form-control"
													list={`list${iitem.id.toString()}`}
													name="laboratory_result"
												/>
											</div>
											<input value={iitem.id} type="hidden" name="parameter_id" />
											<input type="hidden" name="laboratory_result_id" value={plan_result?.id} />
											<input type="hidden" name="laboratory_request_id" value={item?.id ?? ''} />
										</td>
										<td class="text-center">
											<Athtml html={iitem.paraUnit?.unit ?? ''} />
										</td>
										<td class="text-center">
											<span> {iitem.mini === 0 ? '' : iitem.mini?.toLocaleString('en-US')}</span>
											<span> {iitem.sign}</span>
											<span> {iitem.maxi === 0 ? '' : iitem.maxi?.toLocaleString('en-US')}</span>
										</td>
									</tr>
								{/each}
							</tbody>
						</table>
					</div>
				</div>
			</div>
		{/each}
		<div class="col-12">
			<div class=" pb-3">
				<label for="doctor_comment">{locale.T('comment')}</label>
				<TextEditor
					height={500}
					name="doctor_comment"
					setValue={get_visit?.laboratory?.doctor_comment ?? ''}
				/>
			</div>
		</div>
		<div class="col-12 pb-3">
			<label for="exampleInputFile">{locale.T('sample')}</label>
			<input
				value={get_visit?.laboratory?.sample ?? ''}
				type="text"
				name="sample"
				class="form-control"
			/>
		</div>
		<div class="col-12">
			<div class=" pb-3">
				<label for="exampleInputFile">{locale.T('picture')}</label>
				<input type="hidden" name="picture" />
				<input
					multiple
					type="file"
					name="file"
					accept="image/*"
					class="form-control"
					id="exampleInputFile"
				/>
			</div>
		</div>
		{#each get_visit?.laboratory?.uploads || [] as item}
			<div class="p-2 col-3">
				<img class="rounded mx-auto img-thumbnail d-block" src={item?.filename} alt="" />
				<button
					data-bs-toggle="modal"
					data-bs-target="#delete_modal"
					type="button"
					onclick={() => {
						laboratory_id = item.id;
						filename = item.filename || '';
					}}
					class="btn btn-danger w-100">{locale.T('delete_')}</button
				>
			</div>
		{/each}
	</div>

	<div class="card-footer text-end">
		<SubmitButton {loading} />
	</div>
</Form>
