<script lang="ts">
	import { enhance } from '$app/forms';
	import Currency from '$lib/coms/Currency.svelte';
	import CurrencyInput from '$lib/coms-form/CurrencyInput.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import type { PageServerData } from './$types';
	import { page } from '$app/state';
	import PatientInfo from '$lib/coms-ipd-opd/PatientInfo.svelte';
	import CropImage from '$lib/coms-form/CropImage.svelte';
	import Confirm from '$lib/coms/Confirm.svelte';
	let loading = $state(false);
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let bank_pay = $state(0);
	let bank_pay_exhange = $state(0);
	let cash_pay = $state(0);
	let cash_pay_exhange = $state(0);
	let { get_progress_note, get_payment_types, get_currency, patient_info, all_money } =
		$derived(data);
	let get_billing = $derived(get_progress_note?.billing);
	let disc = $state(data?.get_progress_note?.billing.discount ?? '');
	let amount_pay = $derived(all_money?.total_ipd_opd || 0);
	let paymented = $derived(get_billing?.payment?.reduce((s, e) => s + Number(e.value), 0) || 0);
	let after_disc = $derived(calDisc(disc ?? '', amount_pay ?? 0) - paymented);
	let total_bank_pay = $derived(
		bank_pay +
			(bank_pay_exhange * Number(get_currency?.currency_rate)) / Number(get_currency?.exchang_rate)
	);
	let default_cash_pay = $derived(
		after_disc - (total_bank_pay + cash_pay_exhange * Number(get_currency?.currency_rate))
	);
	$effect(() => {
		if (default_cash_pay > 0) {
			cash_pay = default_cash_pay;
		} else {
			cash_pay = 0;
		}
	});
	function calDisc(disc: string, amount: number) {
		if (!disc) return amount;
		if (disc.includes('%')) {
			return Number(amount) - (Number(amount) * Number(disc.replace('%', ''))) / 100;
		} else {
			return Number(amount) - Number(disc);
		}
	}
	let total_cash_pay = $derived(
		cash_pay +
			(cash_pay_exhange * Number(get_currency?.currency_rate)) / Number(get_currency?.exchang_rate)
	);
	let return_or_credit = $derived(Number(total_bank_pay) + Number(total_cash_pay) - after_disc);
	let payment_id_delete = $state<number | undefined>(undefined);
</script>

<div class="row">
	<div class="col-sm-6">
		<a href="/billing/ipd/{get_billing?.progress_note_id}" class="btn btn-link p-0"
			><i class="fa-solid fa-rotate-left"></i>
			{locale.T('back')}
		</a>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/billing/opd" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-money-bills"></i>
					{locale.T('billing')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fas fa-stethoscope"></i>
					{locale.T('opd')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fas fa-stethoscope"></i>
					{locale.T('payment')}
				</a>
			</li>
		</ol>
	</div>
</div>
<PatientInfo {patient_info} />

<div class=" mt-2 p-2 border border-2 border-primary rounded-1">
	<form
		class="card-body"
		enctype="multipart/form-data"
		use:enhance={({ action }) => {
			loading = true;
			store.globalLoading = true;
			return async ({ update, result }) => {
				await update({ reset: false });
				store.globalLoading = false;
				loading = false;
				if (result.type !== 'failure') {
					if (action.search !== '?/delete_payment') {
						document.getElementById('close_update_billing')?.click();
					}
				}
			};
		}}
		method="post"
		action={`?/process_billing${`&ipd=${page.params.id}&billing_id=${get_billing.id}`}`}
	>
		<div class="">
			<table class="table table-bordered table-success table-sm my-0">
				<tbody>
					<tr>
						<td class="fs-5">{locale.T('total')}</td>

						<td
							><Currency
								class="fs-5"
								amount={all_money?.amount_ipd_opd}
								symbol={get_currency?.currency}
							/></td
						>
						<td>
							<Currency
								class="fs-5"
								amount={all_money?.amount_ipd_opd}
								symbol={get_currency?.exchang_to}
								{get_currency}
							/></td
						>
					</tr>
					<tr>
						<td class="fs-5">{locale.T('discount')} {get_currency?.currency} {locale.T('or')} %</td>
						<td colspan="2">
							<input
								name="disc"
								pattern="[0-9]+%?"
								bind:value={disc}
								class="form-control"
								type="text"
							/>
						</td>
					</tr>
					<tr>
						<td class="fs-5">{locale.T('amount_payment')}</td>
						<td><Currency class="fs-5" amount={after_disc} symbol={get_currency?.currency} /></td>
						<td>
							<Currency
								class="fs-5"
								amount={after_disc}
								symbol={get_currency?.exchang_to}
								{get_currency}
							/>
						</td></tr
					>
					<tr>
						<td class="fs-5"
							>{#if Number(return_or_credit) < 0}
								<span class="fs-5">{locale.T('debt')} </span>
							{:else}
								<span class="fs-5">{locale.T('return_money')} </span>
							{/if}</td
						>
						<td>
							<Currency
								class="fs-5"
								amount={Number(return_or_credit)}
								symbol={get_currency?.currency}
							/>
						</td>
						<td>
							<Currency
								class="fs-5"
								amount={Number(return_or_credit)}
								{get_currency}
								symbol={get_currency?.exchang_to}
							/>
						</td>
					</tr>
				</tbody>
			</table>

			{#if get_billing?.payment?.length}
				<div class="alert alert-primary rounded-0 my-1">
					<span class="btn btn-sm btn-info rounded-0">{locale.T('payment_history')}</span>
					<Currency
						class="text-bg-primary mx-2 px-2"
						amount={get_billing?.payment?.reduce((s, e) => s + Number(e.value), 0) || 0}
						symbol={get_currency?.currency}
					/>
					{#each get_billing?.payment || [] as item, index}
						<div class="row g-0">
							<input type="hidden" name="id" value={item.id} />
							<!-- <input type="hidden" name="after_disc" value={after_disc} /> -->
							<div class="col">
								<div class="alert alert-warning rounded-0 py-1 my-1">
									<div class="row">
										<div class="col-auto">{locale.T('n')} {index + 1}</div>
										<div class="col-auto">
											{locale.T('amount')}
											<Currency amount={item.value} symbol={get_currency?.currency} />
										</div>
										<div class="col-auto">
											{locale.T('date')}
											<DDMMYYYYFormat date={item.datetime} />
										</div>
										<div class="col-auto">
											{item.paymentType?.by}
										</div>
									</div>
								</div>
							</div>

							<div class="col-auto">
								<button
									data-bs-toggle="modal"
									data-bs-target="#delete_payment"
									type="button"
									onclick={() => (payment_id_delete = item.id)}
									class="alert alert-danger rounded-0 py-1 my-1">{locale.T('delete_')}</button
								>
							</div>
						</div>
					{/each}
				</div>
			{/if}
			<div class=" alert alert-primary rounded-0 my-1">
				<div class="row pb-1">
					<div class="col-4">
						<span class="fs-5">{locale.T('amount_get')}</span>
					</div>
					<div class="col">
						<input type="hidden" name="cash_pay" value={total_cash_pay} />
						<CurrencyInput
							class="input-group mb-1"
							bind:amount={cash_pay}
							symbol={get_currency?.currency}
							name="cash_pay_base_currency"
						/>
						<CurrencyInput
							bind:amount={cash_pay_exhange}
							symbol={get_currency?.exchang_to}
							name="cash_pay_exhagne_rate"
						/>
					</div>
				</div>

				<hr />
				<div class="row">
					<div class="col-4">
						<span class="fs-5">{locale.T('payment_by_bank')} </span>
					</div>
					<div class="col">
						<input value={total_bank_pay} type="hidden" name="bank_pay" />
						<CurrencyInput
							class="input-group mb-1"
							bind:amount={bank_pay}
							symbol={get_currency?.currency}
							name="bank_pay_"
						/>
						<CurrencyInput
							class="input-group mb-1"
							bind:amount={bank_pay_exhange}
							symbol={get_currency?.exchang_to}
							name="bank_pay_exhange"
						/>

						<select class="form-control" name="payment_type_id" id="payment_type_id">
							{#each get_payment_types as item}
								<option value={item.id}>{item.by}</option>
							{/each}
						</select>
					</div>
				</div>
			</div>
			<div class=" alert alert-primary rounded-0 mb-0">
				<div class="row pb-1">
					<div class="col-4">
						<span class="fs-5">{locale.T('references')}</span>
					</div>
					<div class="col">
						<CropImage
							name="file"
							aspect_ratio
							related_id={get_billing?.id}
							related_type_="billing"
							default_image={get_billing?.uploads?.filename}
						/>
						<!-- <input name="image" class="form-control" accept="image/*" type="file" /> -->
					</div>
				</div>
				<div class="row">
					<div class="col-4">
						<span class="fs-5">{locale.T('note')}</span>
					</div>
					<div class="col">
						<textarea value={get_billing?.note ?? ''} name="note" class="form-control" id="note"
						></textarea>
					</div>
				</div>
			</div>
		</div>
		<div class=" text-end pt-3">
			<SubmitButton {loading} name={locale.T('pay')} />
		</div>
	</form>
</div>
<br />
<Confirm
	body={[{ name: 'id', value: payment_id_delete?.toString() ?? '' }]}
	method="POST"
	modal_id="delete_payment"
	action={`/billing/repay?/delete_payment&payment_id=${payment_id_delete}`}
/>
