import { db } from '$lib/server/db';
import { and, asc, eq, gt, ne, notLike } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { activeBed, billing, uploads, paymentType, progressNote } from '$lib/server/schemas';
import { totalIPD } from '$lib/server/models/calulatorBillingIPD';
import { checkout } from '$lib/server/models';
// import { redirect } from '@sveltejs/kit';
export const load: PageServerLoad = async ({ params }) => {
	const { id: progress_note_id } = params;
	const get_currency = await db.query.currency.findFirst({});
	const get_progress_note = await db.query.progressNote.findFirst({
		where: eq(progressNote.id, +progress_note_id),
		with: {
			billing: {
				with: {
					payment: {
						with: {
							paymentType: true
						}
					},
					patient: true,
					visit: {
						with: {
							presrciption: {
								with: {
									product: true
								}
							}
						}
					},
					progressNote: {
						with: {
							presrciption: {
								with: {
									product: true
								}
							}
						}
					},
					charge: {
						with: {
							productOrder: {
								with: {
									product: true,
									unit: true
								}
							}
						}
					}
				}
			},
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			visit: {
				with: {
					billing: {
						with: {
							payment: {
								with: {
									paymentType: true
								}
							},
							patient: true,
							visit: {
								with: {
									presrciption: {
										with: {
											product: true
										}
									}
								}
							},
							progressNote: {
								with: {
									presrciption: {
										with: {
											product: true
										}
									}
								}
							},
							charge: {
								with: {
									productOrder: {
										with: {
											product: true,
											unit: true
										}
									}
								}
							}
						}
					}
				}
			},
			activeDepartment: {
				with: {
					activeBed: true
				}
			},
			activeBed: {
				with: {
					bed: {
						with: {
							ward: true,
							room: {
								with: {
									product: true
								}
							}
						}
					}
				}
			}
		}
	});
	const get_active_beds = await db.query.activeBed.findMany({
		where: eq(activeBed.progress_note_id, +progress_note_id),
		with: {
			bed: {
				with: {
					room: {
						with: {
							product: true
						}
					}
				}
			}
		}
	});
	const get_payment_types = await db.query.paymentType.findMany({
		orderBy: asc(paymentType.by),
		where: notLike(paymentType.by, '%CASH%')
	});

	const get_billings_due = await db.query.billing.findMany({
		where: and(
			gt(billing.balance, 0),
			eq(billing.patient_id, get_progress_note!.patient_id),
			ne(billing.id, get_progress_note!.billing!.id)
		)
	});
	const all_money = await totalIPD(+progress_note_id);
	// if (get_progress_note?.billing?.status !== "paying") redirect(307, "/billing/ipd")
	let patient_info;
	if (get_progress_note) {
		patient_info = {
			...get_progress_note?.patient,
			date_checkup: get_progress_note?.date_checkup
		};
	}
	const get_upload = await db.query.uploads.findFirst({
		where: and(
			eq(uploads.related_type, 'billing'),
			eq(uploads.related_id, get_progress_note?.billing?.id || 0)
		)
	});
	return {
		get_payment_types,
		get_progress_note: {
			...get_progress_note,
			billing: {
				...get_progress_note?.billing,
				uploads: get_upload
			}
		},
		get_currency,
		get_billings_due,
		get_active_beds,
		all_money,
		patient_info
	};
};
export const actions: Actions = {
	create_product_order: async (e) => {
		await checkout.create_product_order(e);
	},
	remove_product_order: async (e) => {
		await checkout.remove_product_order(e);
	},
	update_product_order: async (e) => {
		await checkout.update_product_order(e);
	},
	discount_product_order: async (e) => {
		await checkout.discount_product_order(e);
	},
	process_billing: async (e) => {
		await checkout.process_billing(e);
	},
	hold: async (e) => {
		await checkout.hold(e);
	},
	delete_payment: async (e) => {
		await checkout.delete_payment(e);
	}
};
