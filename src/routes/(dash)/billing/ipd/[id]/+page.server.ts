import { db } from '$lib/server/db';
import { and, eq, gt, ne } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { activeBed, billing, progressNote } from '$lib/server/schemas';
import { totalIPD } from '$lib/server/models/calulatorBillingIPD';
import { checkout } from '$lib/server/models';
import { productQuery } from '$lib/server/models/productQuery';
// import { redirect } from '@sveltejs/kit';
export const load: PageServerLoad = async ({ url, params }) => {
	const { id: progress_note_id } = params;
	const get_currency = await db.query.currency.findFirst({});
	const get_progress_note = await db.query.progressNote.findFirst({
		where: eq(progressNote.id, +progress_note_id),
		with: {
			billing: {
				with: {
					payment: {
						with: {
							paymentType: true
						}
					},
					patient: true,
					visit: {
						with: {
							presrciption: {
								with: {
									product: true
								}
							}
						}
					},
					progressNote: {
						with: {
							presrciption: {
								with: {
									product: true
								}
							}
						}
					},
					charge: {
						with: {
							productOrder: {
								with: {
									product: {
										with: {
											subUnit: {
												with: {
													unit: true
												}
											},
											unit: true
										}
									},
									unit: true
								}
							}
						}
					}
				}
			},
			patient: true,
			visit: {
				with: {
					billing: {
						with: {
							payment: {
								with: {
									paymentType: true
								}
							},
							patient: true,
							visit: {
								with: {
									presrciption: {
										with: {
											product: true
										}
									}
								}
							},
							progressNote: {
								with: {
									presrciption: {
										with: {
											product: true
										}
									}
								}
							},
							charge: {
								with: {
									productOrder: {
										with: {
											product: {
												with: {
													subUnit: {
														with: {
															unit: true
														}
													},
													unit: true
												}
											},
											unit: true
										}
									}
								}
							}
						}
					}
				}
			}
		}
	});
	const get_active_beds = await db.query.activeBed.findMany({
		where: eq(activeBed.progress_note_id, +progress_note_id),
		with: {
			bed: {
				with: {
					room: {
						with: {
							product: true
						}
					}
				}
			}
		}
	});
	const get_products = await productQuery({ url, page: false });
	const get_billings_due = await db.query.billing.findMany({
		where: and(
			gt(billing.balance, 0),
			eq(billing.patient_id, get_progress_note!.patient_id),
			ne(billing.id, get_progress_note!.billing!.id)
		)
	});
	const all_money = await totalIPD(+progress_note_id);
	// if (get_progress_note?.billing?.status !== "paying") redirect(307, "/billing/ipd")
	return {
		...get_products,
		get_progress_note,
		get_currency,
		get_billings_due,
		get_active_beds,
		all_money
	};
};
export const actions: Actions = {
	create_product_order: async (e) => {
		await checkout.create_product_order(e);
	},
	remove_product_order: async (e) => {
		await checkout.remove_product_order(e);
	},
	update_product_order: async (e) => {
		await checkout.update_product_order(e);
	},
	discount_product_order: async (e) => {
		await checkout.discount_product_order(e);
	},
	process_billing: async (e) => {
		await checkout.process_billing(e);
	},
	hold: async (e) => {
		await checkout.hold(e);
	},
	delete_payment: async (e) => {
		await checkout.delete_payment(e);
	}
};
