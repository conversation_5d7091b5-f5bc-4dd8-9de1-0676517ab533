import { db } from '$lib/server/db';
import { and, asc, eq, gt, ne, notLike } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { billing, uploads, paymentType } from '$lib/server/schemas';
import { checkout } from '$lib/server/models';

export const load: PageServerLoad = async ({ params }) => {
	const { id } = params;
	const get_currency = await db.query.currency.findFirst({});
	const get_billing = await db.query.billing.findFirst({
		where: eq(billing.id, +id || 0),
		with: {
			payment: {
				with: {
					paymentType: true
				}
			},
			patient: true,
			visit: {
				with: {
					presrciption: {
						with: {
							product: true
						}
					},
					patient: {
						with: {
							commune: true,
							district: true,
							provice: true,
							village: true
						}
					},
					department: true,
					progressNote: {
						with: {
							activeBed: {
								with: {
									bed: {
										with: {
											ward: true,
											room: {
												with: {
													product: true
												}
											}
										}
									}
								}
							},
							patient: {
								with: {
									commune: true,
									district: true,
									provice: true,
									village: true
								}
							}
						}
					}
				}
			},
			progressNote: {
				with: {
					presrciption: {
						with: {
							product: true
						}
					}
				}
			},

			charge: {
				with: {
					productOrder: {
						with: {
							product: true,
							unit: true
						}
					}
				}
			}
		}
	});
	const get_payment_types = await db.query.paymentType.findMany({
		orderBy: asc(paymentType.by),
		where: notLike(paymentType.by, '%CASH%')
	});
	const get_billings_due = await db.query.billing.findMany({
		where: and(
			gt(billing.balance, 0),
			eq(billing.patient_id, get_billing?.patient_id || 0),
			ne(billing.id, get_billing?.id || 0)
		)
	});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.related_type, 'billing'), eq(uploads.related_id, get_billing?.id || 0))
	});
	return {
		get_billing: {
			...get_billing,
			uploads: get_upload
		},
		get_payment_types,
		get_currency,
		get_billings_due
	};
};
export const actions: Actions = {
	create_product_order: async (e) => {
		await checkout.create_product_order(e);
	},
	remove_product_order: async (e) => {
		await checkout.remove_product_order(e);
	},
	update_product_order: async (e) => {
		await checkout.update_product_order(e);
	},
	discount_product_order: async (e) => {
		await checkout.discount_product_order(e);
	},
	process_billing: async (e) => {
		await checkout.process_billing(e);
	},
	hold: async (e) => {
		await checkout.hold(e);
	},
	delete_payment: async (e) => {
		await checkout.delete_payment(e);
	}
};
