import { db } from '$lib/server/db';
import { and, eq, gt, ne } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { billing } from '$lib/server/schemas';
import { checkout } from '$lib/server/models';
import { productQuery } from '$lib/server/models/productQuery';

export const load: PageServerLoad = async ({ url, params }) => {
	const { id } = params;
	const get_currency = await db.query.currency.findFirst({});
	const get_billing = await db.query.billing.findFirst({
		where: eq(billing.visit_id, +id || 0),
		with: {
			payment: {
				with: {
					paymentType: true
				}
			},
			patient: true,
			visit: {
				with: {
					presrciption: {
						with: {
							product: true
						}
					}
				}
			},
			progressNote: {
				with: {
					presrciption: {
						with: {
							product: true
						}
					}
				}
			},
			charge: {
				with: {
					productOrder: {
						with: {
							product: {
								with: {
									subUnit: {
										with: {
											unit: true
										}
									},
									unit: true
								}
							},
							unit: true
						}
					}
				}
			}
		}
	});
	// if (get_billing?.status !== 'process') redirect(303, '/billing/opd');
	const get_products = await productQuery({ url, page: false });
	const get_billings_due = await db.query.billing.findMany({
		where: and(
			gt(billing.balance, 0),
			eq(billing.patient_id, get_billing?.patient_id || 0),
			ne(billing.id, get_billing?.id || 0)
		)
	});
	const charge_on_imagerie = get_billing?.charge.find((e) => e.charge_on === 'imagerie');
	const charge_on_laboratory = get_billing?.charge.find((e) => e.charge_on === 'laboratory');
	const charge_on_general = get_billing?.charge.find((e) => e.charge_on === 'general');
	const charge_on_service = get_billing?.charge.find((e) => e.charge_on === 'service');
	const charge_on_prescription = get_billing?.charge.find((e) => e.charge_on === 'prescription');
	const charge_on_vaccine = get_billing?.charge.find((e) => e.charge_on === 'vaccine');
	const charge_on_bed = get_billing?.charge.find((e) => e.charge_on === 'bed');
	return {
		...get_products,
		charge_on_imagerie,
		charge_on_laboratory,
		charge_on_general,
		charge_on_service,
		charge_on_prescription,
		get_billing,
		charge_on_vaccine,
		get_currency,
		charge_on_bed,
		get_billings_due
	};
};
export const actions: Actions = {
	create_product_order: async (e) => {
		await checkout.create_product_order(e);
	},
	remove_product_order: async (e) => {
		await checkout.remove_product_order(e);
	},
	update_product_order: async (e) => {
		await checkout.update_product_order(e);
	},
	discount_product_order: async (e) => {
		await checkout.discount_product_order(e);
	},
	process_billing: async (e) => {
		await checkout.process_billing(e);
	},
	hold: async (e) => {
		await checkout.hold(e);
	},
	delete_payment: async (e) => {
		await checkout.delete_payment(e);
	}
};
