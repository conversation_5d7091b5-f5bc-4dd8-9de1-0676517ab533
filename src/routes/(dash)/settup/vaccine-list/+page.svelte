<script lang="ts">
	import type { PageServerData } from './$types';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import { store } from '$lib/store/store.svelte';
	import TextEditor from '$lib/coms-cu/TextEditor.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let group_id: number | undefined = $state();
	let loading = $state(false);
	let { get_category } = $derived(data);
	let get_groups = $derived(get_category?.group);
	let get_group = $derived(get_groups?.find((e) => e.id === group_id));
</script>

<!-- <DeleteModal action="?/delete_vaccine_type" id={find_vaccin_types?.id} /> -->
<div class="modal fade" id="create_dose" data-bs-backdrop="static">
	<div class="modal-dialog modal-dialog-scrollabl modal-xl">
		<Form
			enctype="multipart/form-data"
			action="?/update_vaccine_dose"
			method="post"
			bind:loading
			fnSuccess={() => {
				group_id = 0;
				document.getElementById('close_vaccine_dose')?.click();
			}}
			class="modal-content"
		>
			<div class="modal-header">
				<h4 class="modal-title">Dose</h4>
				<button
					id="close_vaccine_dose"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="card-body pt-0">
				<div class="modal-body">
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<input value={get_group?.id} type="hidden" name="group_id" />
								<TextEditor setValue={get_group?.vaccine_dose ?? ''} name="vaccine_dose" />
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>

<div class="row">
	<div class="col-sm-6">
		<h2>Vaccine dose</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					Home
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tools"></i>
					Settup
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/settup/vaccine-type" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-viruses nav-icon"></i>
					Vaccine dose
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<div class="row">
			<div class="col">
				<input
					type="text"
					name="table_search"
					class="form-control float-right"
					placeholder="Search"
				/>
			</div>
		</div>
	</div>
	<div style="max-height: {store.inerHight};" class="card-body table-responsive p-0">
		<table class="table table-bordered table-light">
			<thead class="table-active table-light sticky-top">
				<tr>
					<th class="text-center" style="width: 5%;">N</th>
					<th>{locale.T('group')}</th>
					<th>Dose</th>
				</tr>
			</thead>
			<tbody>
				{#each get_category?.group || [] as item, index}
					<tr>
						<td class="text-center">{index + 1}</td>
						<td>{item?.name ?? ''}</td>
						<td>
							<button
								onclick={() => {
									group_id = 0;
									group_id = item.id;
								}}
								type="button"
								class="btn btn-success btn-sm"
								data-bs-toggle="modal"
								data-bs-target="#create_dose"
								>Dose
							</button>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
</div>
