import { db } from '$lib/server/db';
import { parameter, product, category } from '$lib/server/schemas';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { asc, eq, like } from 'drizzle-orm';
import logError from '$lib/server/utils/logError';

export const load = (async () => {
	const get_lab_groups = await db.query.laboratoryGroup.findMany({});
	const get_para_units = await db.query.paraUnit.findMany({});
	const get_category = await db.query.category.findFirst({
		where: like(category.name, 'Laboratory')
	});
	const get_product_labo = await db.query.product.findMany({
		where: eq(product.category_id, get_category?.id || 0),
		with: {
			laboratoryGroup: true,
			category: true,
			parameter: {
				with: {
					paraUnit: true
				}
			}
		},
		orderBy: asc(product.products)
	});
	const get_currency = await db.query.currency.findFirst({});

	return {
		get_product_labo,
		get_lab_groups,
		get_para_units,
		get_currency
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_parameter_group: async ({ request, url }) => {
		const body = await request.formData();
		const { product_name, lab_group_id, price } = Object.fromEntries(body) as Record<
			string,
			string
		>;
		const validErr = {
			product_name: false,
			lab_group_id: false,
			price: false
		};
		if (!product_name.trim()) validErr.product_name = true;
		if (isNaN(+lab_group_id)) validErr.lab_group_id = true;
		if (isNaN(+price) || !price.trim()) validErr.price = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		await db
			.insert(product)
			.values({
				price: +price,
				products: product_name,
				category_id: 5,
				laboratory_group_id: Number(lab_group_id)
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_parameter_group: async ({ request, url }) => {
		const body = await request.formData();
		const { product_name, lab_group_id, price, product_id } = Object.fromEntries(body) as Record<
			string,
			string
		>;
		const validErr = {
			product_name: false,
			lab_group_id: false,
			price: false,
			product_id: false
		};
		if (!product_name.trim()) validErr.product_name = true;
		if (isNaN(+lab_group_id)) validErr.lab_group_id = true;
		if (isNaN(+price) || !price.trim()) validErr.price = true;
		if (isNaN(+product_id) || !product_id.trim()) validErr.product_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		await db
			.update(product)
			.set({
				price: +price,
				products: product_name,
				laboratory_group_id: Number(lab_group_id)
			})
			.where(eq(product.id, Number(product_id)))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	delete_parameter_group: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.delete(product)
			.where(eq(product.id, Number(id)))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	create_parameter: async ({ request, url }) => {
		const body = await request.formData();
		const { parameter_, description, para_unit_id, gender, mini, product_id, maxi, sign } =
			Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			parameter_: false,
			description: false,
			para_unit_id: false,
			gender: false,
			mini: false,
			product_id: false,
			maxi: false,
			sign: false
		};
		if (!parameter_.trim()) validErr.parameter_ = true;
		if (!gender.trim()) validErr.gender = true;
		if (isNaN(+product_id)) validErr.product_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		await db
			.insert(parameter)
			.values({
				description: description,
				gender: gender,
				maxi: +maxi || 0,
				mini: +mini || 0,
				sign: sign,
				parameter: parameter_,
				para_unit_id: +para_unit_id || undefined,
				product_id: Number(product_id)
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
