<script lang="ts">
	import type { ActionData, PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import CreateParameterGroup from '$lib/coms-cu/CreateParameterGroup.svelte';
	import CreateParameter from '$lib/coms-cu/CreateParameter.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	interface Props {
		form: ActionData;
		data: PageServerData;
	}

	let { form, data }: Props = $props();
	let product_lab_id: number | undefined = $state();
	let { get_product_labo, get_lab_groups, get_para_units, get_currency } = $derived(data);
	let find_product_labo = $derived(get_product_labo.filter((e) => e.id === product_lab_id));
</script>

<CreateParameterGroup
	data={{
		get_lab_groups: get_lab_groups,
		get_product_labo: find_product_labo,
		get_currency: get_currency
	}}
	{form}
	bind:product_lab_id
/>
<DeleteModal action="?/delete_parameter_group" id={find_product_labo[0]?.id} />
<CreateParameter
	data={{ get_para_units: get_para_units }}
	{form}
	bind:product_id={product_lab_id}
/>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('group')} {locale.T('parameter')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tools"></i>
					{locale.T('settup')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/settup/parameter/group" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-vial nav-icon"></i>
					{locale.T('group')}
					{locale.T('parameter')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<div class="row">
			<div class="col">
				<input
					type="text"
					name="table_search"
					class="form-control float-right"
					placeholder="Search"
				/>
			</div>
			<div class="col-auto">
				<button
					onclick={() => {
						product_lab_id = 0;
					}}
					type="button"
					class="btn btn-success"
					data-bs-toggle="modal"
					data-bs-target="#create_parameter_group"
					><i class="fa-solid fa-square-plus"></i>
					{locale.T('add_parameter_roup')}
				</button>
			</div>
		</div>
	</div>
	<div style="max-height: {store.inerHight};" class="card-body table-responsive p-0">
		<table class="table table-bordered border table-light">
			<thead class="table-light table-active sticky-top">
				<tr>
					<th class="text-center" style="width: 5%;">{locale.T('n')}</th>
					<th>{locale.T('group')} {locale.T('parameter')}</th>
					<th>{locale.T('parameter')}</th>
					<th>{locale.T('lab_group')}</th>
					<th>{locale.T('price')}</th>
					<th></th>
				</tr>
			</thead>
			<tbody>
				{#each get_product_labo as item, index}
					<tr>
						<td class="text-center">{index + 1}</td>
						<td style="width: 30%;">{item.products}</td>
						<td style="width: 15%;">
							<a href="/settup/parameter/{item.id}" class="btn btn-primary btn-sm"
								>{item.parameter.length} <i class="fas fa-vial"></i>
							</a>
							<button
								aria-label="createparameter"
								onclick={() => {
									product_lab_id = 0;
									product_lab_id = item.id;
								}}
								type="button"
								class="btn btn-light btn-sm"
								data-bs-toggle="modal"
								data-bs-target="#create_parameter"
								><i class="fa-solid fa-square-plus"></i>
							</button>
						</td>
						<td style="width: 30%;">{item.laboratoryGroup?.laboratory_group}</td>
						<td style="width: 10%;">
							<Currency amount={item?.price} symbol={get_currency?.currency} />
						</td>
						<td style="width: 15%;">
							<button
								aria-label="createparametergroup"
								onclick={() => {
									product_lab_id = 0;
									product_lab_id = item.id;
								}}
								type="button"
								class="btn btn-primary btn-sm"
								data-bs-toggle="modal"
								data-bs-target="#create_parameter_group"
								><i class="fa-solid fa-file-pen"></i>
							</button>
							<button
								aria-label="deletemodal"
								onclick={() => {
									product_lab_id = 0;
									product_lab_id = item.id;
								}}
								type="button"
								class="btn btn-danger btn-sm"
								data-bs-toggle="modal"
								data-bs-target="#delete_modal"
								><i class="fa-solid fa-trash-can"></i>
							</button>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
</div>
