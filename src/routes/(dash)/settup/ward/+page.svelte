<script lang="ts">
	import type { PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import { store } from '$lib/store/store.svelte';
	interface Props {
		data: PageServerData;
	}

	let { data }: Props = $props();
	let ward_id: number | null = $state(null);
	let loading = $state(false);
	let { wards, get_progress_note } = $derived(data);
	let find_word = $derived(wards.find((e) => e.id === ward_id));
</script>

{ward_id}
<DeleteModal delete_modal="delete_ward" action="?/delete_ward" id={ward_id} />
<!-- @_Add_Ward-->
<div class="modal fade" id="create_ward" data-bs-backdrop="static">
	<div class="modal-dialog modal-xl">
		<Form
			action={find_word?.id ? '?/update_ward' : '?/create_ward'}
			method="post"
			class="modal-content"
			bind:loading
			fnSuccess={() => {
				ward_id = 0;
				document.getElementById('close_ward')?.click();
			}}
		>
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('ward')}</h4>
				<button
					id="close_ward"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div class="card-body pt-0">
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<input value={find_word?.id} type="hidden" name="id" />
								<label for="ward">{locale.T('name')}</label>
								<input
									value={find_word?.ward ?? ''}
									name="ward"
									type="text"
									class="form-control"
									id="ward"
								/>
								<!-- {#if form?.ward}
									<p class="text-danger">{locale.T('input_data')}</p>
								{/if} -->
							</div>
						</div>
					</div>
				</div>
			</div>
			<div
				class={find_word
					? 'modal-footer justify-content-between'
					: 'modal-footer justify-content-end'}
			>
				{#if find_word}
					<button
						type="button"
						data-bs-toggle="modal"
						class="btn btn-danger"
						data-bs-target="#delete_ward"
					>
						<i class="fa-solid fa-trash-can"></i> {locale.T('delete_')}</button
					>
				{/if}
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('ward')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tools"></i>
					{locale.T('settup')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/settup/ward" class="btn btn-link p-0 text-secondary"
					><i class="fa-solid fa-hotel"></i>
					{locale.T('ward')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<div class="row">
			<div class="col">
				<input
					type="text"
					name="table_search"
					class="form-control float-right"
					placeholder="Search"
				/>
			</div>
			<div class="col-auto">
				<button
					onclick={() => (ward_id = 0)}
					type="button"
					class="btn btn-success"
					data-bs-toggle="modal"
					data-bs-target="#create_ward"
					><i class="fa-solid fa-square-plus"></i>
					{locale.T('add')}
				</button>
			</div>
		</div>
	</div>
	<div style="max-height: {store.inerHight}" class="card-body table-responsive p-0">
		<table class="table table-sm table-bordered table-light">
			<tbody>
				{#each wards as { room, ward, id }}
					<tr class="border border-3">
						<td style="width: 40%;" class="position-relative text-bg-primary">
							<div class="justify-content-between row p-2 g-0 gap-0">
								<a href="/settup/ward/{id}" class="btn btn-outline-light btn-sm fs-3 col-auto"
									><i class="fa-solid fa-hospital"></i> {ward}
								</a>
								<button
									aria-label="createward"
									onclick={() => {
										ward_id = 0;
										ward_id = id;
									}}
									data-bs-toggle="modal"
									data-bs-target="#create_ward"
									type="button"
									class="btn btn-primary btn-sm fs-3 col-2"
									><i class="fa-solid fa-file-pen"></i>
								</button>
							</div>
						</td>
						<td style="width: 60;" class="text-start p-0 m-0">
							{#each room as iitem}
								{@const beds = iitem.bed || []}
								<table class="table table-sm my-2">
									<thead>
										<tr>
											<td
												style="width: 50%;"
												class="position-relative justify-content-center align-content-center text-bg-success text-start fs-5 m-0"
											>
												&nbsp;<i class="fa-regular fa-window-maximize"></i>&nbsp;
												{iitem.room}
												{iitem.product ? `( ${iitem?.product?.products} )` : ''}
												<span class="badge bg-primary rounded-pill">
													#{iitem.department?.products ?? ''}
												</span>
											</td>
											<td style="width: 50%;">
												{#each beds as bed}
													<button
														class:btn-danger={get_progress_note.some(
															(e) =>
																e.activeBed.find((e) => e.datetime_out === null)?.bed_id === bed.id
														)}
														class="btn btn-info rounded-0 me-2 mb-2"
														type="button"
													>
														<i class="fa-solid fa-bed"></i>
														{bed.bed}
													</button>
												{/each}
											</td>
										</tr>
									</thead>
								</table>
							{/each}
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
</div>
