import { db } from '$lib/server/db';
import { paraUnit } from '$lib/server/schemas';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { eq } from 'drizzle-orm';
import logError from '$lib/server/utils/logError';

export const load = (async () => {
	const get_para_units = await db.query.paraUnit.findMany({});
	return {
		get_para_units
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_para_unit: async ({ request, url }) => {
		const body = await request.formData();
		const { unit_ } = Object.fromEntries(body) as Record<string, string>;
		if (!unit_.trim()) return fail(400, { unit_: true });
		await db
			.insert(paraUnit)
			.values({
				unit: unit_
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_para_unit: async ({ request, url }) => {
		const body = await request.formData();
		const { unit_, para_unit_id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.update(paraUnit)
			.set({
				unit: unit_
			})
			.where(eq(paraUnit.id, Number(para_unit_id)))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	delete_para_unit: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.delete(paraUnit)
			.where(eq(paraUnit.id, Number(id)))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
