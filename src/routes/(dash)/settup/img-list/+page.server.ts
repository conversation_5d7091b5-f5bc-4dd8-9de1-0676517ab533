import { db } from '$lib/server/db';
import { product, category, template } from '$lib/server/schemas';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { asc, eq, like } from 'drizzle-orm';
import logError from '$lib/server/utils/logError';

export const load = (async () => {
	const get_currency = await db.query.currency.findFirst({});
	const get_categories = await db.query.category.findMany({});
	const get_templates = await db.query.template.findMany({
		orderBy: asc(template.diagnosis)
	});
	const get_category = await db.query.category.findFirst({
		where: like(category.name, 'Imagerie'),
		with: {
			group: true
		}
	});

	const get_products = await db.query.product.findMany({
		where: eq(product.category_id, get_category?.id || 0),
		with: {
			group: true,
			unit: true
		}
	});
	return {
		get_products,
		get_categories,
		get_templates,
		get_currency,
		get_category
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	create_ImagerieGroup: async ({ request, url }) => {
		const body = await request.formData();
		const { name_product, group_id, price } = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			name_product: false,
			group_id: false,
			price: false
		};
		if (!name_product.trim()) validErr.name_product = true;
		if (isNaN(+price)) validErr.price = true;
		if (isNaN(+group_id)) validErr.group_id = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		await db
			.insert(product)
			.values({
				price: Number(price),
				products: name_product,
				group_id: +group_id,
				category_id: 2
			})
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	update_ImagerieGroup: async ({ request, url }) => {
		const body = await request.formData();
		const { name_product, group_id, price, product_id } = Object.fromEntries(body) as Record<
			string,
			string
		>;
		await db
			.update(product)
			.set({
				price: Number(price),
				products: name_product,
				group_id: Number(group_id)
			})
			.where(eq(product.id, Number(product_id)))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	},
	delete_ImagerieGroup: async ({ request, url }) => {
		const body = await request.formData();
		const { id } = Object.fromEntries(body) as Record<string, string>;
		await db
			.delete(product)
			.where(eq(product.id, Number(id)))
			.catch((e) => {
				logError({ url, body, err: e });
			});
	}
};
