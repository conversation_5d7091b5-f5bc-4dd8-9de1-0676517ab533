<script lang="ts">
	import type { PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import SubmitButton from '$lib/coms/SubmitButton.svelte';
	import { store } from '$lib/store/store.svelte';
	import Currency from '$lib/coms/Currency.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	interface Props {
		data: PageServerData;
	}
	let { data }: Props = $props();
	let product_id: number = $state(0);
	let loading = $state(false);
	let { get_products, get_category, get_currency } = $derived(data);
	let find_product = $derived(get_products.find((e) => e.id === product_id));
</script>

<DeleteModal action="?/create_ImagerieGroup" id={find_product?.id} />
<!-- @_Add_product_as_ -->
<div class="modal fade" id="create-product-as-imagerie" data-bs-backdrop="static">
	<div class="modal-dialog modal-xl">
		<Form
			action={find_product?.id ? '?/update_ImagerieGroup' : '?/create_ImagerieGroup'}
			method="post"
			enctype="multipart/form-data"
			class="modal-content"
			bind:loading
			fnSuccess={() => {
				product_id = 0;
				document.getElementById('close')?.click();
			}}
		>
			<div class="modal-header">
				<h4 class="modal-title">{locale.T('imagerie')}</h4>
				<button
					id="close"
					type="button"
					class="btn-close"
					data-bs-dismiss="modal"
					aria-label="Close"
				>
				</button>
			</div>
			<div class="modal-body">
				<div class="card-body pt-0">
					<div class="row">
						<div class="col-12">
							<div class=" pb-3">
								<input value={find_product?.id} type="hidden" name="product_id" />
								<label for="name_product">{locale.T('imagerie_group')} </label>
								<input
									value={find_product?.products ?? ''}
									name="name_product"
									type="text"
									class="form-control"
									id="name_product"
								/>
							</div>
						</div>
						<div class="col-12">
							<div class=" pb-3">
								<label for="group_id">{locale.T('group')}</label>
								<select
									value={find_product?.group_id}
									id="group_id"
									class="form-control"
									name="group_id"
								>
									{#each get_category?.group || [] as item}
										<option value={item.id}>{item.name}</option>
									{/each}
								</select>
							</div>
						</div>
						<div class="col-12">
							<div class=" pb-3">
								<label for="price">{locale.T('price')}</label>
								<input
									value={find_product?.price ?? ''}
									name="price"
									type="text"
									class="form-control"
									id="price"
								/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer justify-content-end">
				<SubmitButton {loading} />
			</div>
		</Form>
	</div>
</div>

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('imagerie_list')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tools"></i>
					{locale.T('settup')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/settup/img-list" class="btn btn-link p-0 text-secondary"
					><i class="fa-regular fa-image nav-icon"></i>
					{locale.T('imagerie_list')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<div class="row">
			<div class="col">
				<input
					type="text"
					name="table_search"
					class="form-control float-right"
					placeholder="Search"
				/>
			</div>

			<div class="col-auto">
				<button
					onclick={() => {
						product_id = 0;
					}}
					type="button"
					class="btn btn-success"
					data-bs-toggle="modal"
					data-bs-target="#create-product-as-imagerie"
					><i class="fa-solid fa-square-plus"></i>
					{locale.T('add_imagerie')}
				</button>
			</div>
		</div>
	</div>
	<div style="max-height: {store.inerHight};" class="card-body table-responsive p-0">
		<table class="table table-bordered table-light">
			<thead class="table-active table-light sticky-top">
				<tr>
					<th class="text-center" style="width: 5%;">{locale.T('n')}</th>
					<th style="width: 30%;">{locale.T('products')}</th>
					<th>{locale.T('group')}</th>
					<th>{locale.T('unit')}</th>
					<th>{locale.T('price')}</th>
					<th></th>
				</tr>
			</thead>
			<tbody>
				{#each get_products || [] as item, index}
					<tr>
						<td class="text-center">{index + 1}</td>
						<td>{item.products}</td>
						<td>{item.group?.name ?? ''}</td>
						<td>{item.unit?.unit ?? ''}</td>
						<td>
							<Currency amount={item.price} symbol={get_currency?.currency} />
						</td>
						<td>
							<div>
								<button
									aria-label="createproductasimageire"
									onclick={() => {
										product_id = 0;
										product_id = item.id;
									}}
									type="button"
									class="btn btn-primary btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#create-product-as-imagerie"
									><i class="fa-solid fa-file-pen"></i>
								</button>
								<button
									aria-label="deletemodal"
									onclick={() => {
										product_id = 0;
										product_id = item.id;
									}}
									type="button"
									class="btn btn-danger btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#delete_modal"
									><i class="fa-solid fa-trash-can"></i>
								</button>
							</div>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
</div>
