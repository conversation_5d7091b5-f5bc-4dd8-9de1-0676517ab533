<script lang="ts">
	import type { ActionData, PageServerData } from './$types';
	import DeleteModal from '$lib/coms/DeleteModal.svelte';
	import CreateExam from '$lib/coms-cu/CreateExam.svelte';
	import CreatePhysical from '$lib/coms-cu/CreatePhysical.svelte';
	import UpdatePhysical from '$lib/coms-cu/UpdatePhysical.svelte';
	import { store } from '$lib/store/store.svelte';
	import { locale } from '$lib/translations/locales.svelte';
	interface Props {
		data: PageServerData;
		form: ActionData;
	}

	let { data, form }: Props = $props();
	let exam_id: number | undefined = $state();
	let { get_examas } = $derived(data);
	let find_exam = $derived(get_examas.filter((e) => e.id === exam_id));
</script>

<UpdatePhysical data={{ get_examas: find_exam }} bind:exam_id />
<CreatePhysical data={{ get_examas: find_exam }} bind:exam_id />
<DeleteModal action="?/delete_exam" id={find_exam[0]?.id} />
<CreateExam data={{ get_examas: find_exam }} {form} bind:exam_id />

<div class="row">
	<div class="col-sm-6">
		<h2>{locale.T('examination')}</h2>
	</div>
	<div class="col-sm-6">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tools"></i>
					{locale.T('settup')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/settup/physical-exam" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-book-open nav-icon"></i>
					{locale.T('physical_exam')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<div class="row">
			<div class="col">
				<input
					type="text"
					name="table_search"
					class="form-control float-right"
					placeholder="Search"
				/>
			</div>
			<div class="col-auto">
				<button
					onclick={() => (exam_id = 0)}
					type="button"
					class="btn btn-success"
					data-bs-toggle="modal"
					data-bs-target="#create-exam"
					><i class="fa-solid fa-square-plus"></i>
					{locale.T('add_examination')}
				</button>
			</div>
		</div>
	</div>
	<div style="max-height: {store.inerHight};" class="card-body table-responsive p-0">
		<table class="table table-bordered table-light table-hover text-nowrap">
			<thead class="table-light table-active sticky-top">
				<tr>
					<th class="text-center" style="width: 5%;">{locale.T('n')}</th>
					<th>{locale.T('examination')}</th>
					<th>{locale.T('physical')}</th>
					<th></th>
				</tr>
			</thead>
			<tbody class="table-sm">
				{#each get_examas || [] as item, index}
					<tr>
						<td class="text-center">{index + 1}</td>
						<td>{item.examination}</td>
						<td>
							<div>
								<button
									onclick={() => {
										exam_id = item.id;
									}}
									type="button"
									class="btn btn-light btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#update-physical"
									>{item.physical.length} <i class="fa-regular fa-folder-open"></i>
								</button>
								<button
									aria-label="createphysical"
									onclick={() => {
										exam_id = item.id;
									}}
									type="button"
									class="btn btn-light btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#create-physical"
									><i class="fa-solid fa-square-plus"></i>
								</button>
							</div>
						</td>
						<td>
							<div>
								<button
									aria-label="createexam"
									onclick={() => {
										exam_id = item.id;
									}}
									type="button"
									class="btn btn-primary btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#create-exam"
									><i class="fa-solid fa-file-pen"></i>
								</button>
								<button
									aria-label="deletemodal"
									onclick={() => {
										exam_id = item.id;
									}}
									type="button"
									class="btn btn-danger btn-sm"
									data-bs-toggle="modal"
									data-bs-target="#delete_modal"
									><i class="fa-solid fa-trash-can"></i>
								</button>
							</div>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
</div>
