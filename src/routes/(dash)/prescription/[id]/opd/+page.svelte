<script lang="ts">
	import type { ActionData, PageServerData } from './$types';
	import { locale } from '$lib/translations/locales.svelte';
	import Form from '$lib/coms-form/Form.svelte';
	import DDMMYYYYFormat from '$lib/coms/DDMMYYYYFormat.svelte';
	import { goto } from '$app/navigation';
	import ConfirmSubmit from '$lib/coms-form/ConfirmSubmit.svelte';
	interface Props {
		data: PageServerData;
		form: ActionData;
	}

	let { data, form }: Props = $props();
	let { get_prescriptions, get_visit, get_prescription, charge_on_prescription } = $derived(data);
	let get_appionment = $derived(get_visit?.appointment);
	let get_advice_teaching = $derived(get_visit?.adviceTeaching);
	let loading = $state(false);
	let prescription_id: number = $state(0);
	let product_id: number | null = $derived(get_prescription?.product_id || null);
	let get_productOrder = $derived(
		charge_on_prescription?.productOrder.find((e) => e.product_id === product_id)
	);
</script>

<div class="row">
	<div class="col-sm-8">
		<h4>
			<span>{locale.T('patient_name')}</span>
			<span class="text-primary">
				@{get_visit?.patient?.name_khmer},
				{get_visit?.patient?.name_latin}
			</span>
			<span>
				<DDMMYYYYFormat date={get_visit?.date_checkup} />
			</span>
		</h4>
	</div>
	<div class="col-sm-4">
		<ol class="breadcrumb justify-content-end">
			<li class="breadcrumb-item">
				<a href="/dashboard" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-tachometer-alt"></i>
					{locale.T('home')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href="/billing/opd" class="btn btn-link p-0 text-secondary"
					><i class="fas fa-stethoscope"></i>
					{locale.T('opd')}
				</a>
			</li>
			<li class="breadcrumb-item">
				<a href={'#'} class="btn btn-link p-0 text-secondary"
					><i class="fas fa-money-bills"></i>
					{locale.T('prescription')}
				</a>
			</li>
		</ol>
	</div>
</div>

<div class="card bg-light">
	<div class="card-header">
		<div class="row g-1">
			<div class="col fs-5">
				<a href="/billing/opd/" class="btn btn-link"
					><i class="fa-solid fa-rotate-left"></i> {locale.T('back')}
				</a>
				<span># {locale.T('prescription')}</span>
			</div>
			<div class="col"></div>
			<div class="col-auto">
				<div class="row g-1">
					<div class="col-auto">
						<ConfirmSubmit
							header="សូមបញ្ជាក់ជាមួយអ្នកជំងឺជាមុន"
							class="btn btn-success btn-sm"
							name="អ្នកជំងឺមិនយកថ្នាំ"
							action="?/delete_product_order"
						></ConfirmSubmit>
					</div>
					<div class="col">
						{#if get_prescriptions?.length}
							<a
								target="_blank"
								aria-label="nersing_process"
								href="/report/{get_prescriptions[0]?.visit_id}/prescription"
								class="btn btn-success btn-sm"
								><i class="fa-solid fa-print"></i>
							</a>
						{/if}
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="alert alert-secondary rounded-0 mb-0">
		<fieldset disabled={!get_prescription?.id}>
			<Form fnSuccess={() => goto('?')} action="?/update_product_order" bind:loading method="post">
				<input value={get_prescription?.id || ''} type="hidden" name="prescription_id" />
				<input value={get_prescription?.product_id} type="hidden" name="product_id" />

				<div class="row pb-3">
					<div class="col-sm-6">
						<label for="select_prodcts">{locale.T('products')} </label>
						<input
							type="text"
							name=""
							readonly
							class="form-control"
							value={get_prescription?.product?.products}
							id=""
						/>
					</div>
					<div class="col-6">
						<div class="row g-1">
							<div class="col-sm-6">
								<label for="Amount">{locale.T('amount')}</label>
								<input
									value={get_productOrder?.qty ?? ''}
									id="Amount"
									name="amount"
									type="number"
									class="form-control"
								/>
								{#if form?.amount}
									<div class="text-danger">
										{locale.T('input_data')}
									</div>
								{/if}
							</div>
							<div class="col-sm-6">
								<label for=""></label>
								<button type="submit" class="form-control btn text-bg-primary border-0">
									{locale.T('save')}
								</button>
							</div>
						</div>
					</div>
				</div>
			</Form>
		</fieldset>
	</div>

	<div class="card-body table-responsive p-0">
		<table class="table mb-0 table-bordered table-hover text-nowrap table-light">
			<thead class="table-active">
				<tr>
					<th>{locale.T('n')}</th>
					<th>{locale.T('medicine')}</th>
					<th>{locale.T('use')}</th>
					<th>{locale.T('time_to_use')}</th>
					<th class="text-center">{locale.T('duration')}</th>
					<th class="text-center">{locale.T('amount')} {locale.T('prescription')}</th>
					<th class="text-center">{locale.T('amount')} {locale.T('take')}</th>
					<th></th>
				</tr>
			</thead>
			<tbody class="table-sm">
				{#each get_prescriptions as item, index}
					{@const productOrder = charge_on_prescription?.productOrder.find(
						(e) => e.product_id === item.product_id
					)}
					<tr
						class:table-active={item.id === prescription_id}
						class:table-primary={item.product_id === product_id}
					>
						<td class="text-center">{index + 1}</td>
						<td
							>{item.product?.products} <br />
							<span class="badge text-bg-primary">{item.product?.generic_name ?? ''}</span>
						</td>

						<td>
							{item.use ?? ''}
						</td>
						<td>
							<div>
								<span class="badge text-bg-warning">
									{#if item.morning !== 0}
										{locale.T('morning')} {item.morning}
									{/if}
								</span>
								<span class="badge text-bg-warning">
									{#if item.noon !== 0}
										{locale.T('noon')} {item.noon}
									{/if}
								</span>
								<span class="badge text-bg-warning">
									{#if item.afternoon !== 0}
										{locale.T('afternoon')} {item.afternoon}
									{/if}
								</span>
								<span class="badge text-bg-warning">
									{#if item.evening !== 0}
										{locale.T('evening')} {item.evening}
									{/if}
								</span>
								<span class="badge text-bg-warning">
									{#if item.night !== 0}
										{locale.T('night')} {item.night}
									{/if}
								</span>
							</div>
						</td>
						<td class="text-center">{item.duration ?? ''}</td>
						<td class="text-center">{item.amount} {item?.unit?.unit} </td>
						<td class="text-center">
							{productOrder?.qty ?? 0}
							{productOrder?.unit?.unit}
						</td>
						<td class="text-center">
							<div>
								<a
									data-sveltekit-noscroll
									href="?prescription_id={item.id}"
									aria-label="createprescription"
									class="btn btn-primary btn-sm"
									><i class="fa-solid fa-pills"></i>
								</a>
							</div>
						</td>
					</tr>
				{/each}
				{#if get_advice_teaching}
					<tr>
						<td class="text-center table-active" colspan="2">{locale.T('advice_or_teaching')}</td>
						<td colspan="6">
							<div class="text-break">
								{get_advice_teaching?.description ?? ''}
							</div>
						</td>
					</tr>
				{/if}
				{#if get_appionment}
					<tr>
						<td class="text-center table-active" colspan="2">{locale.T('appintment')}</td>
						<td colspan="6">
							<div class="text-break">
								<span class="text-primary">
									@<DDMMYYYYFormat date={get_appionment?.datetime} />
								</span>
								{get_appionment?.description}
							</div>
						</td>
					</tr>
				{/if}
			</tbody>
		</table>
	</div>
</div>
