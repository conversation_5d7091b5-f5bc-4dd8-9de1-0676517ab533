import { db } from '$lib/server/db';
import { billing, presrciption, progressNote } from '$lib/server/schemas';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { eq } from 'drizzle-orm';
import { createProductOrder, deleteProductOrder, updateProductOrder } from '$lib/server/models';

export const load = (async ({ params, url }) => {
	const progress_note_id = params.id;
	const prescription_id = url.searchParams.get('prescription_id') ?? '';
	const get_progress_note = await db.query.progressNote.findFirst({
		where: eq(progressNote.id, Number(progress_note_id)),
		with: {
			patient: true,
			billing: {
				with: {
					charge: {
						with: {
							productOrder: {
								with: {
									product: true,
									unit: true
								}
							}
						}
					}
				}
			},
			appointment: true
		}
	});
	const get_prescription = await db.query.presrciption.findFirst({
		where: eq(presrciption.id, +prescription_id),
		with: {
			product: {
				with: {
					category: true,
					unit: true
				}
			},
			unit: true
		}
	});
	const get_prescriptions = await db.query.presrciption.findMany({
		where: eq(presrciption.progress_note_id, +progress_note_id),
		with: {
			product: {
				with: {
					category: true,
					unit: true
				}
			},
			unit: true
		}
	});

	const charge_on_prescription = get_progress_note?.billing?.charge.find(
		(e) => e.charge_on === 'prescription'
	);
	return {
		get_progress_note,
		get_prescriptions,
		get_prescription,
		charge_on_prescription
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	update_product_order: async ({ request, params, url }) => {
		const progress_note_id = params.id;
		const body = await request.formData();
		const { product_id, amount } = Object.fromEntries(body) as Record<string, string>;
		const validErr = {
			product_id: false,
			amount: false
		};
		if (!product_id || isNaN(+product_id)) validErr.product_id = true;
		if (!amount || isNaN(+amount)) validErr.amount = true;
		if (Object.values(validErr).includes(true)) return fail(400, validErr);
		const get_progress_note = await db.query.progressNote.findFirst({
			where: eq(progressNote.id, +progress_note_id),
			with: {
				billing: {
					with: {
						charge: {
							with: {
								productOrder: true
							}
						}
					}
				},
				presrciption: {
					with: {
						product: true
					}
				}
			}
		});
		const charge_on_prescription = get_progress_note?.billing?.charge.find(
			(e) => e.charge_on === 'prescription'
		);
		const get_product_order = charge_on_prescription?.productOrder.find(
			(e) => e.product_id === +product_id
		);
		if (get_product_order && get_progress_note?.id) {
			await updateProductOrder({
				disc: '',
				price: Number(get_product_order?.price),
				qty: +amount,
				product_order_id: get_product_order!.id,
				body: body,
				url: url
			});
		} else if (get_progress_note?.presrciption.some((e) => e.product_id === +product_id)) {
			const presrciption = get_progress_note?.presrciption.find(
				(e) => e.product_id === +product_id
			);
			await createProductOrder({
				charge_id: charge_on_prescription!.id,
				product_id: +product_id,
				price: presrciption?.product?.price ? +presrciption?.product?.price : null,
				qty: +amount,
				body: body,
				url: url
			});
		}
	},
	delete_product_order: async ({ params }) => {
		const progress_note_id = params.id;
		const get_billing = await db.query.billing.findFirst({
			where: eq(billing.progress_note_id, +progress_note_id),
			with: {
				charge: {
					with: {
						productOrder: true
					}
				}
			}
		});
		const charge_on_prescription = get_billing?.charge.find((e) => e.charge_on === 'prescription');
		if (!get_billing || !charge_on_prescription) return fail(400, { errId: true });
		for (const e of charge_on_prescription?.productOrder ?? []) {
			if (e.id) {
				await deleteProductOrder(e.id);
			}
		}
	}
};
