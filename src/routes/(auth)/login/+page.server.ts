import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { db } from '$lib/server/db';
import { verify } from '@node-rs/argon2';
import { and, eq } from 'drizzle-orm';
import { uploads, user } from '$lib/server/schemas';
import * as auth from '$lib/server/auth';
export const load: PageServerLoad = async ({ locals }) => {
	if (locals.session) redirect(307, `/dashboard`);
	const get_clinichinfo = await db.query.clinicinfo.findFirst({});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.mimeType, 'logo0'), eq(uploads.related_type, 'clinicinfo'))
	});
	return { get_clinichinfo, get_upload };
};

export const actions: Actions = {
	login: async (event) => {
		const body = await event.request.formData();
		const username = body.get('username');
		const password = body.get('password');
		if (!validateUsername(username)) {
			return fail(400, { message: 'Invalid username' });
		}
		if (!validatePassword(password)) {
			return fail(400, { message: 'Invalid password' });
		}

		const results = await db.select().from(user).where(eq(user.username, username));

		const existingUser = results.at(0);
		if (!existingUser) {
			return fail(400, { message: 'Incorrect username or password' });
		}

		const validPassword = await verify(existingUser?.password_hash ?? '', password, {
			memoryCost: 19456,
			timeCost: 2,
			outputLen: 32,
			parallelism: 1
		});
		if (!validPassword) {
			return fail(400, { message: 'Incorrect username or password' });
		}
		// await db.delete(session).where(eq(session.user_id, existingUser.id)); Delete all session
		const sessionToken = auth.generateSessionToken();
		const session_ = await auth.createSession(sessionToken, existingUser.id);
		auth.setSessionTokenCookie(event, sessionToken, session_.expires_at);
		redirect(302, '/dashboard');
	}
};

function validateUsername(username: unknown): username is string {
	return (
		typeof username === 'string' &&
		username.length >= 3 &&
		username.length <= 31 &&
		/^[a-z0-9_-]+$/.test(username)
	);
}

function validatePassword(password: unknown): password is string {
	return typeof password === 'string' && password.length >= 6 && password.length <= 255;
}
