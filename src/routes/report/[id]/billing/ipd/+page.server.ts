import { db } from '$lib/server/db';
import { billing, uploads, progressNote } from '$lib/server/schemas';
import { and, eq, gt, ne } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { totalIPD } from '$lib/server/models/calulatorBillingIPD';

export const load = (async ({ params }) => {
	const { id } = params;
	const get_clinic_info = await db.query.clinicinfo.findFirst({});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.mimeType, 'logo0'), eq(uploads.related_type, 'clinicinfo'))
	});
	const get_currency = await db.query.currency.findFirst({});
	const get_progress_note = await db.query.progressNote.findFirst({
		where: eq(progressNote.id, +id),
		with: {
			billing: {
				with: {
					charge: {
						with: {
							productOrder: {
								with: {
									product: true,
									unit: true
								}
							}
						}
					}
				}
			},
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			visit: {
				with: {
					billing: {
						with: {
							payment: {
								with: {
									paymentType: true
								}
							},
							visit: {
								with: {
									presrciption: {
										with: {
											product: true
										}
									}
								}
							},
							progressNote: {
								with: {
									presrciption: {
										with: {
											product: true
										}
									}
								}
							},
							charge: {
								with: {
									productOrder: {
										with: {
											product: true,
											unit: true
										}
									}
								}
							}
						}
					}
				}
			}
		}
	});
	const get_billing = await db.query.billing.findFirst({
		where: eq(billing.id, +id),
		with: {
			progressNote: true,
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			charge: {
				with: {
					productOrder: {
						with: {
							product: true
						}
					}
				}
			},
			payment: {
				with: {
					paymentType: true
				}
			}
		}
	});
	const get_billings = await db.query.billing.findMany({
		where: and(
			gt(billing.balance, 0),
			eq(billing.patient_id, get_billing?.patient_id || 0),
			ne(billing.id, +id)
		)
	});
	const previous_due = get_billings.reduce((s: number, i) => s + +i.balance!, 0);
	const all_money = await totalIPD(+id);
	const ipd_charge_on_bed = get_progress_note?.billing?.charge.find((e) => e.charge_on === 'bed');
	const ipd_charge_on_prescription = get_progress_note?.billing?.charge.find(
		(e) => e.charge_on === 'prescription'
	);
	const ipd_charge_on_general = get_progress_note?.billing?.charge.find(
		(e) => e.charge_on === 'general'
	);
	const ipd_charge_on_service = get_progress_note?.visit
		?.filter((e) => e.checkin_type === 'SERVICE')
		.flatMap((e) => {
			return e.billing?.charge?.find((e) => e.charge_on === 'service');
		});
	const opd_charge = get_progress_note?.visit?.filter((e) => e.checkin_type !== 'SERVICE');
	const patient = {
		patient: get_progress_note?.patient,
		date_checkout: get_progress_note?.date_checkout,
		id: get_progress_note?.id,
		patient_id: get_progress_note?.patient_id
	};

	return {
		get_billing: get_progress_note?.billing,
		get_currency,
		get_clinic_info,
		previous_due,
		all_money,
		ipd_charge_on_bed,
		ipd_charge_on_prescription,
		ipd_charge_on_general,
		ipd_charge_on_service,
		opd_charge,
		patient,
		get_upload
	};
}) satisfies PageServerLoad;
