import { db } from '$lib/server/db';
import { billing, visit } from '$lib/server/schemas';
import { and, eq, gt, ne } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { uploads } from '$lib/server/schemas';

export const load = (async ({ params }) => {
	const { id } = params;
	const get_clinic_info = await db.query.clinicinfo.findFirst({});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.mimeType, 'logo0'), eq(uploads.related_type, 'clinicinfo'))
	});
	const get_currency = await db.query.currency.findFirst({});

	const get_visit = await db.query.visit.findFirst({
		where: eq(visit.id, +id),
		with: {
			patient: {
				with: {
					commune: true,
					district: true,
					provice: true,
					village: true
				}
			},
			billing: {
				with: {
					charge: {
						with: {
							productOrder: {
								with: {
									product: true,
									unit: true
								}
							}
						}
					},
					payment: {
						with: {
							paymentType: true
						}
					}
				}
			}
		}
	});
	const get_billings = await db.query.billing.findMany({
		where: and(
			gt(billing.balance, 0),
			eq(billing.patient_id, Number(get_visit?.patient_id)),
			ne(billing.id, +id)
		)
	});
	const patient = {
		patient: get_visit?.patient,
		date_checkout: get_visit?.date_checkup,
		id: get_visit?.id,
		patient_id: get_visit?.patient_id
	};
	const previous_due = get_billings.reduce((s: number, i) => s + +i.balance!, 0);

	return {
		get_currency,
		get_clinic_info,
		previous_due,
		patient,
		get_visit,
		get_upload
	};
}) satisfies PageServerLoad;
