import { db } from '$lib/server/db';
import { and, eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { uploads, visit } from '$lib/server/schemas';

export const load = (async ({ params }) => {
	const { id } = params;
	const get_visit = await db.query.visit.findFirst({
		where: eq(visit.id, +id),
		with: {
			presrciption: {
				with: {
					product: {
						with: {
							group: true,
							unit: true
						}
					}
				}
			},
			patient: {
				with: {
					village: true,
					district: true,
					commune: true,
					provice: true
				}
			},
			staff: true,
			accessment: true,
			adviceTeaching: true,
			appointment: true
		}
	});
	const get_clinic_info = await db.query.clinicinfo.findFirst({});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.mimeType, 'logo0'), eq(uploads.related_type, 'clinicinfo'))
	});
	return { get_clinic_info, get_visit, get_upload };
}) satisfies PageServerLoad;
