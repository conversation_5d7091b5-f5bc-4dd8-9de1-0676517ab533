import { db } from '$lib/server/db';
import { and, eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { uploads, imagerieRequest } from '$lib/server/schemas';
import { redirect } from '@sveltejs/kit';
export const load: PageServerLoad = async ({ params }) => {
	const imagerie_request_id = params.id;
	const get_imagerie_request = await db.query.imagerieRequest.findFirst({
		with: {
			inputBy: {
				with: {
					title: true
				}
			},
			visit: {
				with: {
					patient: {
						with: {
							commune: true,
							district: true,
							provice: true,
							village: true
						}
					},
					staff: {
						with: {
							title: true
						}
					}
				}
			},
			product: true,
			resultImagerie: {
				with: {
					resultForm: {
						with: {
							options: true
						}
					}
				}
			}
		},
		where: eq(imagerieRequest.id, +imagerie_request_id || 0)
	});
	const get_clinic_info = await db.query.clinicinfo.findFirst({});
	const get_upload = await db.query.uploads.findFirst({
		where: and(eq(uploads.mimeType, 'logo0'), eq(uploads.related_type, 'clinicinfo'))
	});
	const get_uploads = await db.query.uploads.findMany({
		where: and(
			eq(uploads.related_type, 'imagerieRequest'),
			eq(uploads.related_id, get_imagerie_request?.id || 0)
		)
	});
	if (!get_imagerie_request?.status) redirect(303, '/imagerie');
	const get_inputer_sign = await db.query.uploads.findFirst({
		where: and(
			eq(uploads.related_type, 'staffSign'),
			eq(uploads.related_id, get_imagerie_request?.input_by_id || 0)
		)
	});
	return {
		get_imagerie_request: {
			...get_imagerie_request,
			uploads: get_uploads,
			visit: {
				...get_imagerie_request.visit,
				staff: {
					...get_imagerie_request?.visit?.staff
				}
			}
		},
		get_inputer_sign,
		get_clinic_info,
		get_upload
	};
};
