import { db } from '$lib/server/db';
import { uploads, type related_type } from '$lib/server/schemas';
import { and, eq } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { fail, redirect } from '@sveltejs/kit';
import { fileHandle } from '$lib/server/upload';

export const load = (async ({ url }) => {
	const uuid = url.searchParams.get('uuid') as string;
	const get_upload = await db.query.uploads.findFirst({
		where: eq(uploads.mimeType, uuid)
	});
	return {
		uuid: get_upload?.mimeType,
		old_filename: get_upload?.filename,
		related_id: Number(url.searchParams.get('related_id')),
		related_type: url.searchParams.get('related_type') as related_type
	};
}) satisfies PageServerLoad;

export const actions: Actions = {
	upload: async ({ request }) => {
		const body = await request.formData();
		const uuid = body.get('uuid') as string;
		const related_id = body.get('related_id') as string;
		const related_type_ = body.get('related_type') as string;
		const file = body.get('file') as File;
		if (!uuid || !file.size || !related_id || !related_type_) return fail(400, { err: true });
		if (Number(file.size) > 31457280) {
			fail(400, { err: true });
		}
		const get_upload = await db.query.uploads.findFirst({
			where: and(
				eq(uploads.mimeType, uuid),
				eq(uploads.related_id, +related_id),
				eq(uploads.related_type, related_type_ as related_type)
			)
		});
		if (get_upload) {
			await fileHandle.auto(body);
			// await fileHandle.drop(get_upload?.filename as string);
			// await fileHandle.insert(file, +related_id, related_type_ as related_type);
			redirect(303, '?');
		}
	}
};
